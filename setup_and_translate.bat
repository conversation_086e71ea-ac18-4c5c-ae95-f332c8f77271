@echo off
chcp 65001
echo ========================================
echo SRT字幕翻译工具安装和运行脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.6+
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在安装必要的依赖包...
echo 安装googletrans翻译库...
pip install googletrans==4.0.0rc1

echo 安装requests库...
pip install requests

echo.
echo 依赖安装完成！
echo.

echo 开始翻译所有英文SRT文件...
python translate_srt_offline.py --all

echo.
echo ========================================
echo 翻译完成！
echo 中文字幕文件已生成，文件名格式为: *.zh.srt
echo ========================================
pause
