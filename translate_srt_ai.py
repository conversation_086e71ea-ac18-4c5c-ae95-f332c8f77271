#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SRT字幕文件英译中翻译工具（AI版本）
使用AI能力进行高质量翻译，保持原有格式和时间码
"""

import os
import re
from typing import List, Dict
import argparse
import json

class AISRTTranslator:
    def __init__(self):
        """初始化翻译器"""
        self.subtitle_pattern = re.compile(
            r'(\d+)\n'  # 字幕序号
            r'(\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3})\n'  # 时间码
            r'(.*?)\n\n',  # 字幕内容
            re.DOTALL
        )
    
    def parse_srt_file(self, file_path: str) -> List[Dict]:
        """
        解析SRT文件
        
        Args:
            file_path: SRT文件路径
            
        Returns:
            包含字幕信息的字典列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except UnicodeDecodeError:
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
        
        # 确保文件以双换行结尾，便于正则匹配
        if not content.endswith('\n\n'):
            content += '\n\n'
        
        subtitles = []
        matches = self.subtitle_pattern.findall(content)
        
        for match in matches:
            subtitle_num, timecode, text = match
            subtitles.append({
                'number': int(subtitle_num),
                'timecode': timecode,
                'text': text.strip()
            })
        
        return subtitles
    
    def translate_batch_with_ai(self, texts: List[str]) -> List[str]:
        """
        使用AI批量翻译文本
        
        Args:
            texts: 要翻译的英文文本列表
            
        Returns:
            翻译后的中文文本列表
        """
        # 准备翻译提示
        translation_prompt = """请将以下英文字幕翻译成中文。要求：
1. 翻译要准确、自然、符合中文表达习惯
2. 保持原文的语气和风格
3. 对于专业术语（如AI、ChatGPT等）可保留英文或使用通用中文译名
4. 对于[MUSIC]、[APPLAUSE]等标记，翻译为[音乐]、[掌声]等
5. 返回格式为JSON数组，每个元素对应一条翻译

英文字幕内容：
"""
        
        # 将文本列表转换为JSON格式
        texts_json = json.dumps(texts, ensure_ascii=False, indent=2)
        full_prompt = translation_prompt + texts_json
        
        print("正在使用AI翻译...")
        print(f"翻译提示：{translation_prompt}")
        print(f"待翻译内容：{texts_json[:200]}...")
        
        # 这里需要用户手动输入翻译结果，或者集成具体的AI API
        print("\n请将上述内容复制到AI助手中进行翻译，然后将翻译结果粘贴到这里：")
        print("（翻译结果应为JSON数组格式）")
        
        # 等待用户输入翻译结果
        translated_json = input("请粘贴翻译结果：")
        
        try:
            translated_texts = json.loads(translated_json)
            if len(translated_texts) != len(texts):
                print(f"警告：翻译结果数量({len(translated_texts)})与原文数量({len(texts)})不匹配")
            return translated_texts
        except json.JSONDecodeError:
            print("翻译结果格式错误，使用原文")
            return texts
    
    def translate_subtitles(self, subtitles: List[Dict], batch_size: int = 10) -> List[Dict]:
        """
        翻译字幕列表
        
        Args:
            subtitles: 字幕信息列表
            batch_size: 批处理大小
            
        Returns:
            翻译后的字幕信息列表
        """
        translated_subtitles = []
        
        # 分批处理
        for i in range(0, len(subtitles), batch_size):
            batch = subtitles[i:i+batch_size]
            batch_texts = [sub['text'] for sub in batch]
            
            print(f"\n正在翻译第 {i+1}-{min(i+batch_size, len(subtitles))} 条字幕...")
            
            # 使用AI翻译这一批文本
            translated_batch = self.translate_batch_with_ai(batch_texts)
            
            # 构建翻译后的字幕
            for j, subtitle in enumerate(batch):
                translated_text = translated_batch[j] if j < len(translated_batch) else subtitle['text']
                translated_subtitles.append({
                    'number': subtitle['number'],
                    'timecode': subtitle['timecode'],
                    'text': translated_text
                })
        
        return translated_subtitles
    
    def write_srt_file(self, subtitles: List[Dict], output_path: str):
        """
        写入SRT文件
        
        Args:
            subtitles: 字幕信息列表
            output_path: 输出文件路径
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            for subtitle in subtitles:
                f.write(f"{subtitle['number']}\n")
                f.write(f"{subtitle['timecode']}\n")
                f.write(f"{subtitle['text']}\n\n")
    
    def translate_srt_file(self, input_path: str, output_path: str, batch_size: int = 10):
        """
        翻译单个SRT文件
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            batch_size: 批处理大小
        """
        print(f"开始处理文件: {input_path}")
        
        # 解析SRT文件
        subtitles = self.parse_srt_file(input_path)
        print(f"解析到 {len(subtitles)} 条字幕")
        
        # 翻译字幕
        translated_subtitles = self.translate_subtitles(subtitles, batch_size)
        
        # 写入翻译后的文件
        self.write_srt_file(translated_subtitles, output_path)
        print(f"翻译完成，输出文件: {output_path}")
    
    def translate_all_srt_files(self, directory: str = ".", batch_size: int = 10):
        """
        翻译目录中所有的英文SRT文件
        
        Args:
            directory: 目录路径，默认为当前目录
            batch_size: 批处理大小
        """
        # 查找所有英文SRT文件
        srt_files = []
        for filename in os.listdir(directory):
            if filename.endswith('.en.srt'):
                srt_files.append(filename)
        
        if not srt_files:
            print("未找到英文SRT文件（*.en.srt）")
            return
        
        print(f"找到 {len(srt_files)} 个英文SRT文件")
        
        for srt_file in srt_files:
            input_path = os.path.join(directory, srt_file)
            # 生成中文文件名：将.en.srt替换为.zh.srt
            output_filename = srt_file.replace('.en.srt', '.zh.srt')
            output_path = os.path.join(directory, output_filename)
            
            try:
                self.translate_srt_file(input_path, output_path, batch_size)
                print(f"✓ 成功翻译: {srt_file} -> {output_filename}")
            except Exception as e:
                print(f"✗ 翻译失败: {srt_file}, 错误: {e}")
            
            print("-" * 50)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SRT字幕文件英译中翻译工具（AI版本）')
    parser.add_argument('--input', '-i', help='输入SRT文件路径')
    parser.add_argument('--output', '-o', help='输出SRT文件路径')
    parser.add_argument('--directory', '-d', default='.', help='处理目录，默认为当前目录')
    parser.add_argument('--all', '-a', action='store_true', help='翻译目录中所有英文SRT文件')
    parser.add_argument('--batch-size', '-b', type=int, default=10, help='批处理大小，默认为10')
    
    args = parser.parse_args()
    
    translator = AISRTTranslator()
    
    if args.all or (not args.input and not args.output):
        # 翻译目录中所有文件
        translator.translate_all_srt_files(args.directory, args.batch_size)
    elif args.input and args.output:
        # 翻译单个文件
        translator.translate_srt_file(args.input, args.output, args.batch_size)
    else:
        print("请指定输入和输出文件，或使用 --all 参数翻译所有文件")

if __name__ == "__main__":
    main()
