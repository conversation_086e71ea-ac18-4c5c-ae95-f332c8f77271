1
00:00:00,000 --> 00:00:05,246
[音乐]

2
00:00:05,246 --> 00:00:06,587
Welcome 要 我们的 课程

3
00:00:06,587 --> 00:00:09,268
加速 你的 学习 与 ChatGPT.

4
00:00:09,268 --> 00:00:11,092
I'm <PERSON>,

5
00:00:11,092 --> 00:00:17,190
a distinguished professor 的 工程 at Oakland University.

6
00:00:17,190 --> 00:00:21,783
你 might 知道 我 从 我们的 课程 学习 如何 要 学习

7
00:00:21,783 --> 00:00:28,082
一个 的 <PERSON><PERSON>'s 最 受欢迎的 课程 或者 my books 在 学习 技巧

8
00:00:28,082 --> 00:00:33,759
Today, I'm thrilled 要 introduce 你 要 这 exciting 课程 那个

9
00:00:33,759 --> 00:00:41,150
explores 这个 intersection 的 generative AI 和 effective 学习 strategies.

10
00:00:41,150 --> 00:00:45,099
好吧 dive 成 这个 neuroscience 的 学习

11
00:00:45,099 --> 00:00:49,819
showing 你 什么 happens in 你的 brain 当 你 学习 和

12
00:00:49,819 --> 00:00:54,080
如何 generative AI 可以 complement 这些 processes.

13
00:00:54,080 --> 00:00:59,769
You'll discover 如何 要 使用 AI 要 创造 engaging hooks 为

14
00:00:59,769 --> 00:01:06,039
你的 studies, 要 connect 想法 要 你的 personal experiences,

15
00:01:06,039 --> 00:01:11,860
和 even overcome 这个 limitations 的 working memory.

16
00:01:11,860 --> 00:01:18,084
Joining 我 是 my friend, Professor Jules White 从 Vanderbilt University,

17
00:01:18,084 --> 00:01:21,208
a leading expertise in generative AI

18
00:01:21,208 --> 00:01:24,996
Jules, 什么 可以 我们的 learners expect?

19
00:01:24,996 --> 00:01:29,128
和 please, no 机器人 模仿 这 time.

20
00:01:29,128 --> 00:01:32,604
>> 好吧 芭芭 我 承诺 不 要 模仿 任何 机器人

21
00:01:32,604 --> 00:01:37,214
好吧 不 完全 因为 如果 我们 想 关于 Star wars 和 C three po,

22
00:01:37,214 --> 00:01:42,044
he's 这个 protocol droid 那个 帮助 要 翻译 什么 人们 正在 说 周围

23
00:01:42,044 --> 00:01:44,040
他 成 不同的 语言

24
00:01:44,040 --> 00:01:46,860
和 这 是 什么 we're 要 要 学习 作为 一个 的 这个 最

25
00:01:46,860 --> 00:01:51,202
重要的 概念 与 这些 模型 当 我们 开始 思考 关于 学习

26
00:01:51,202 --> 00:01:55,454
是 如何 做 我们 让 它们 要 翻译 想法 成 方式 那个 我们 可以 学习 它们 和

27
00:01:55,454 --> 00:01:58,455
接近 它们 更 有效地 成 analogies 和

28
00:01:58,455 --> 00:02:00,608
隐喻 那个 共鸣 与 我们

29
00:02:00,608 --> 00:02:03,040
我 don't 想 的 它 作为 那个 我 can't 学习 某些东西

30
00:02:03,040 --> 00:02:06,357
It's 那个 我 haven't 看到 这个 translation 的 这个 想法 或者

31
00:02:06,357 --> 00:02:09,784
这个 概念 成 一个 解释 那个 使 有意义 要 我

32
00:02:09,784 --> 00:02:13,788
和 we're 要 要 帮助 你 成为 能够 要 桥接 那个 差距 要 成为 能够 要 去 和

33
00:02:13,788 --> 00:02:15,495
创造 和 翻译 想法 和

34
00:02:15,495 --> 00:02:19,252
复杂的 概念 成 解释 那个 使 有意义 为 你

35
00:02:19,252 --> 00:02:22,492
但是 we're 也 要 要 有 要 看 如何 要 做 令人惊奇的 事情

36
00:02:22,492 --> 00:02:26,790
像 拍 a 照片 的 某些东西 和 学习 如何 要 烘烤 它 或者 如何 要 制作 它 或者

37
00:02:26,790 --> 00:02:30,312
拍 a 照片 的 a 设备 那个 we'd 像 要 学习 如何 要 使用

38
00:02:30,312 --> 00:02:34,256
We're 要 要 谈论 关于 为什么 我们 can't 放弃 上 在 传统的 学习 和

39
00:02:34,256 --> 00:02:38,702
这个 重要性 的 实际上 知道 a 很多 的 概念 从 传统的 学习 当

40
00:02:38,702 --> 00:02:40,280
我们 去 和 开始 提示

41
00:02:40,280 --> 00:02:44,402
现在 你 可能 知道 我 从 这个 课程 那个 我 教 叫做 提示 工程 为

42
00:02:44,402 --> 00:02:48,536
聊天 GPT 这 是 一个 的 这个 最 受欢迎的 课程 在 Coursera 去 年

43
00:02:48,536 --> 00:02:53,063
和 I'm 真的 兴奋 关于 帮助 要 探索 如何 我们 使用 简单的 提示

44
00:02:53,063 --> 00:02:58,028
技巧 要 丰富 我们的 理解 和 我们的 学习 关于 不同的 主题

45
00:02:58,028 --> 00:03:02,141
现在 我 承诺 那个 我 won't 制作 任何 机器人 模仿 但是

46
00:03:02,141 --> 00:03:06,903
我 绝对 将 告诉 你 一些 迷人的 能力 那个 AI 将

47
00:03:06,903 --> 00:03:11,197
允许 你 要 利用 成 要 改进 和 加速 你的 学习

48
00:03:11,197 --> 00:03:12,180
[音乐]

