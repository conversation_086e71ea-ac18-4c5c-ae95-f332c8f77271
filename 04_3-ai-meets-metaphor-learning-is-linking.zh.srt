1
00:00:00,400 --> 00:00:01,849
<PERSON><PERSON> here.

2
00:00:01,849 --> 00:00:06,972
In this video, we'll introduce
you to how metaphor can help

3
00:00:06,972 --> 00:00:14,150
you with your learning and how generative
AI can help you with your metaphors.

4
00:00:14,150 --> 00:00:18,870
But let's begin by looking
at how the brain learns.

5
00:00:18,870 --> 00:00:19,918
Fundamentally.

6
00:00:19,918 --> 00:00:23,972
The brain, of course,
is very complicated, but

7
00:00:23,972 --> 00:00:28,317
we can simplify our understanding
of it by focusing on

8
00:00:28,317 --> 00:00:32,880
the fundamental building
block called the neuron.

9
00:00:32,880 --> 00:00:37,360
There's about 86 billion
of them in the brain.

10
00:00:37,360 --> 00:00:42,075
In essence,
neurons have an arm that reaches out, and

11
00:00:42,075 --> 00:00:46,200
they've got these legs down below.

12
00:00:46,200 --> 00:00:53,010
What they're doing is reaching out and
sending signals to one another.

13
00:00:53,010 --> 00:00:59,970
These signals jump that little gap between
the neurons, connecting them together.

14
00:00:59,970 --> 00:01:03,562
This is what's happening when
you're learning something.

15
00:01:03,562 --> 00:01:10,690
You're making connections between neurons
in long term memory in the brain.

16
00:01:10,690 --> 00:01:17,131
This is no matter what you're learning,
whether it's how to do a dance step,

17
00:01:17,131 --> 00:01:22,012
some new ideas related to generative AI,
like ChatGPT, or

18
00:01:22,012 --> 00:01:27,820
math problems, or how to conjugate
a verb in a foreign language.

19
00:01:27,820 --> 00:01:34,900
Whatever you're learning, you're simply
making new connections between neurons.

20
00:01:34,900 --> 00:01:40,300
When you're learning, you're
actually building those connections.

21
00:01:40,300 --> 00:01:43,089
The more you practice with something,

22
00:01:43,089 --> 00:01:47,240
the stronger those links
between the neurons can become.

23
00:01:48,300 --> 00:01:54,747
Well, unless, of course, you forget or
don't have the time to practice or

24
00:01:54,747 --> 00:01:59,183
repeatedly study the materials
you're learning,

25
00:01:59,183 --> 00:02:06,820
then your little synoptic janitors can
sweep away those connections you've made.

26
00:02:06,820 --> 00:02:12,759
That's why, if you're not restudying
the material and learning well,

27
00:02:12,759 --> 00:02:19,280
those connections can be swept away
as if you hadn't learned it at all.

28
00:02:19,280 --> 00:02:23,240
But let's say you do
practice with the material.

29
00:02:23,240 --> 00:02:29,440
As you're practicing, those sets
of links get stronger and richer.

30
00:02:29,440 --> 00:02:36,240
You really learn the material well and
can more easily pull it to mind.

31
00:02:36,240 --> 00:02:42,100
These links are like links in a necklace,
and they're strong and rich.

32
00:02:43,280 --> 00:02:46,390
Here's an interesting point.

33
00:02:46,390 --> 00:02:51,373
When you are learning something,
then you create sets of links,

34
00:02:51,373 --> 00:02:56,910
or many sets of links related to
whatever you've been learning.

35
00:02:56,910 --> 00:03:01,865
But once you've learned that concept,
you can use it as

36
00:03:01,865 --> 00:03:07,990
a metaphor to help bridge to
the new thing you're learning.

37
00:03:07,990 --> 00:03:16,090
An easy example of this is understanding
how the current of water flows.

38
00:03:16,090 --> 00:03:18,786
It seems simple to you now, but

39
00:03:18,786 --> 00:03:24,074
you played with how water flows
as a baby and as a toddler, and

40
00:03:24,074 --> 00:03:29,570
it took you a few years to
really figure out that concept.

41
00:03:29,570 --> 00:03:35,412
Once you've got that idea,
though, of how water flows,

42
00:03:35,412 --> 00:03:40,898
knowing that concept can serve
as an easy mental bridge

43
00:03:40,898 --> 00:03:47,780
to help you grasp the new idea
of how electrical current flows.

44
00:03:47,780 --> 00:03:52,384
Metaphor is incredibly
valuable in learning.

45
00:03:52,384 --> 00:03:55,740
Why is metaphor valuable?

46
00:03:55,740 --> 00:04:01,260
Well, I was working on a book
called a mind for numbers.

47
00:04:01,260 --> 00:04:06,488
As I was working on the manuscript for
this book, I sent out thousands

48
00:04:06,488 --> 00:04:12,348
of emails to top rated professors who
were known for their quality teaching,

49
00:04:12,348 --> 00:04:18,600
and I asked them if they might take
a look at the manuscript for the book.

50
00:04:18,600 --> 00:04:24,080
Shocking percentages of them said,
sure, I'd be glad to.

51
00:04:24,080 --> 00:04:30,320
When they looked, they gave me a lot
of insight about how we learn.

52
00:04:30,320 --> 00:04:35,344
How do we have a mind for
numbers or a mind for language or

53
00:04:35,344 --> 00:04:41,220
a mind for learning anything at all,
for that matter?

54
00:04:41,220 --> 00:04:45,420
One thing they told me,
though, was really surprising.

55
00:04:45,420 --> 00:04:50,571
They said, I don't usually
talk to other professors or

56
00:04:50,571 --> 00:04:58,940
teachers about how I teach because I use
an approach that they often don't like.

57
00:04:58,940 --> 00:05:02,020
What was the approach they used?

58
00:05:02,020 --> 00:05:07,470
It was the use of metaphor and analogy.

59
00:05:07,470 --> 00:05:11,025
They didn't like to tell
other professors or

60
00:05:11,025 --> 00:05:16,735
teachers about their approach
because the others would often say,

61
00:05:16,735 --> 00:05:20,790
so that's why you're so
popular as a teacher.

62
00:05:20,790 --> 00:05:24,570
You just make things so
easy for your students.

63
00:05:25,910 --> 00:05:28,982
But isn't that the job of teachers and

64
00:05:28,982 --> 00:05:33,260
professors to make that
new learning easier?

65
00:05:33,260 --> 00:05:40,060
Because learning is really hard,
as you can see from this paper here.

66
00:05:40,060 --> 00:05:44,865
But we can make it easier on
ourselves by using metaphor,

67
00:05:44,865 --> 00:05:49,940
just as these brilliant
teaching professors do.

68
00:05:49,940 --> 00:05:55,979
Now, metaphor is a great way to
help you learn some new ideas,

69
00:05:55,979 --> 00:05:59,810
but metaphors always break down.

70
00:05:59,810 --> 00:06:05,001
For example,
when it comes to electrical current flow,

71
00:06:05,001 --> 00:06:11,650
we know that at a quantum level,
water is not a very good metaphor.

72
00:06:11,650 --> 00:06:19,690
But when a metaphor breaks down, we can
just throw it away and get a new metaphor.

73
00:06:19,690 --> 00:06:25,090
The trick is,
how do we come up with metaphors?

74
00:06:25,090 --> 00:06:30,590
It's been really tough in
the past to find good metaphors,

75
00:06:30,590 --> 00:06:34,090
except now it's not hard at all.

76
00:06:34,090 --> 00:06:40,170
It's not hard now because
we can use generative AI.

77
00:06:40,170 --> 00:06:42,850
Let me show you what I mean.

78
00:06:42,850 --> 00:06:48,581
Let's say we're learning
about main guard and

79
00:06:48,581 --> 00:06:52,770
scope in Python programming.

80
00:06:52,770 --> 00:06:55,746
We could ask ChatGPT, Claude, or

81
00:06:55,746 --> 00:07:00,570
any of the other major
foundational models to explain,

82
00:07:00,570 --> 00:07:04,879
using a metaphor,
what those two concepts are and

83
00:07:04,879 --> 00:07:09,950
what the difference between
these two concepts is.

84
00:07:09,950 --> 00:07:14,541
Surprisingly, these
generative AI models can

85
00:07:14,541 --> 00:07:20,110
come up with brilliant and
beautiful metaphors.

86
00:07:20,110 --> 00:07:28,129
For example, you can think of main guard
as being like a gatekeeper of a castle,

87
00:07:28,129 --> 00:07:34,110
and scope is like various
rooms within that castle.

88
00:07:34,110 --> 00:07:38,030
Then it gives you a lot more information.

89
00:07:38,030 --> 00:07:45,651
Using a metaphor is a great way to help
you grasp new and difficult ideas.

90
00:07:45,651 --> 00:07:50,685
When you're trying to learn something new,
ask ChatGPT or

91
00:07:50,685 --> 00:07:55,819
any of the generative AI models,
what's a good metaphor for

92
00:07:55,819 --> 00:08:01,476
this, and you don't have to accept
the first option it gives you.

93
00:08:01,476 --> 00:08:04,328
Try four other different metaphors and

94
00:08:04,328 --> 00:08:09,120
you'll be amazed at how different
those other ones can be.

95
00:08:09,120 --> 00:08:14,120
Jules will be giving you some
more ideas along these lines.

96
00:08:14,120 --> 00:08:20,796
I like to think of metaphors as
being a little bit like a tent.

97
00:08:20,796 --> 00:08:27,010
Let's say you're raising a tent and
you have two poles.

98
00:08:27,010 --> 00:08:33,539
Well, the tent touches the cloth
wherever those poles come up.

99
00:08:33,539 --> 00:08:40,019
You can think of it as a metaphor
is very strongly connected or

100
00:08:40,019 --> 00:08:48,350
related to the underlying idea where
that tent pole touches the cloth.

101
00:08:48,350 --> 00:08:55,854
Here you can see a metaphor can have two
points in common with another concept.

102
00:08:55,854 --> 00:09:03,830
It can have maybe three points in common,
or can have even more points in common.

103
00:09:03,830 --> 00:09:09,170
As we're looking at these metaphors,
we can have places of good fit,

104
00:09:09,170 --> 00:09:13,785
places of poor fit, and
also more places or more metaphors,

105
00:09:13,785 --> 00:09:18,850
different metaphors that
have good fits and bad fits.

106
00:09:18,850 --> 00:09:23,403
You can ask for metaphors that
have many points in common, or

107
00:09:23,403 --> 00:09:28,402
you can ask what's a good metaphor
that describes, for example,

108
00:09:28,402 --> 00:09:31,330
a molecule and how it functions?

109
00:09:31,330 --> 00:09:37,970
Anything that puzzles you, that's what
you want to ask for a metaphor for.

110
00:09:37,970 --> 00:09:40,938
You can ask ChatGPT, Claude, or

111
00:09:40,938 --> 00:09:46,636
any of the good generative AI
platforms for great metaphors.

112
00:09:46,636 --> 00:09:50,340
Of course, proper prompting helps too.

113
00:09:50,340 --> 00:09:54,420
And for more help here,
let's turn to Jules.

