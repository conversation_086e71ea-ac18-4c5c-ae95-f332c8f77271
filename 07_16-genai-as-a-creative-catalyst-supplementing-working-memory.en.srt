1
00:00:00,000 --> 00:00:03,080
In this video, we're
going to be talking about

2
00:00:03,080 --> 00:00:05,380
generative AI and how you can

3
00:00:05,380 --> 00:00:08,760
use it to supplement
your working memory.

4
00:00:08,760 --> 00:00:11,910
Now, what is working memory?

5
00:00:11,910 --> 00:00:14,520
It turns out that
that's the part

6
00:00:14,520 --> 00:00:17,310
of your brain or your memory,

7
00:00:17,310 --> 00:00:21,180
where you're just holding
something temporarily.

8
00:00:21,180 --> 00:00:25,125
For example, let's say
someone tells you a code like

9
00:00:25,125 --> 00:00:29,845
23709 and they want
you to remember it.

10
00:00:29,845 --> 00:00:32,300
You repeat it to yourself,

11
00:00:32,300 --> 00:00:39,240
23709, 23709, and that's
what you do to remember it.

12
00:00:39,240 --> 00:00:43,820
You're repeating it as part
of your working memory,

13
00:00:43,820 --> 00:00:46,610
that temporary storage place.

14
00:00:46,610 --> 00:00:51,360
We can contrast this
with long-term memory.

15
00:00:51,360 --> 00:00:54,610
Long-term memory
is the information

16
00:00:54,610 --> 00:00:57,935
that we store all
around the neocortex,

17
00:00:57,935 --> 00:00:59,570
and it's the information we

18
00:00:59,570 --> 00:01:02,130
hold for long periods of time.

19
00:01:02,130 --> 00:01:06,550
When you're remembering the
date of some important event

20
00:01:06,550 --> 00:01:08,190
or you're trying to remember

21
00:01:08,190 --> 00:01:09,590
even your address or

22
00:01:09,590 --> 00:01:11,210
something else you
might have learned,

23
00:01:11,210 --> 00:01:12,880
maybe even in school,

24
00:01:12,880 --> 00:01:17,155
you're drawing on your
long-term memory.

25
00:01:17,155 --> 00:01:20,330
To help you visualize
the difference

26
00:01:20,330 --> 00:01:23,545
between working memory
and long-term memory,

27
00:01:23,545 --> 00:01:25,650
let's imagine that in

28
00:01:25,650 --> 00:01:29,410
your head is something a
little like an octopus.

29
00:01:29,410 --> 00:01:31,610
It's in the front of your brain.

30
00:01:31,610 --> 00:01:34,500
Well, I like to think of it

31
00:01:34,500 --> 00:01:38,540
as if your working
memory was an octopus,

32
00:01:38,540 --> 00:01:41,525
but with only four arms.

33
00:01:41,525 --> 00:01:44,510
It's something like a quadrupus.

34
00:01:44,510 --> 00:01:47,820
This quadrupus lies towards

35
00:01:47,820 --> 00:01:50,400
the front mostly of your brain

36
00:01:50,400 --> 00:01:56,860
and holds about on average
four pieces of information.

37
00:01:56,860 --> 00:01:59,760
The size of the pieces of

38
00:01:59,760 --> 00:02:02,680
information can
vary depending on

39
00:02:02,680 --> 00:02:04,280
whether you know a lot about

40
00:02:04,280 --> 00:02:09,175
the material and what
previous training you've had.

41
00:02:09,175 --> 00:02:12,330
This temporary working memory

42
00:02:12,330 --> 00:02:15,410
contrasts with long-term memory,

43
00:02:15,410 --> 00:02:21,225
and that long-term memory is
more like a set of lockers.

44
00:02:21,225 --> 00:02:24,305
Inside those lockers scattered

45
00:02:24,305 --> 00:02:28,330
around your brain are
sets of neural links.

46
00:02:28,330 --> 00:02:30,690
When you're trying
to learn something,

47
00:02:30,690 --> 00:02:34,770
oftentimes what's happening
is your working memory is

48
00:02:34,770 --> 00:02:39,835
working a way trying to
create those sets of links.

49
00:02:39,835 --> 00:02:42,630
Then once it's created them,

50
00:02:42,630 --> 00:02:46,070
it stores them in
long-term memory.

51
00:02:46,070 --> 00:02:49,180
Then later, you can go around

52
00:02:49,180 --> 00:02:52,760
and access those sets
of neural links,

53
00:02:52,760 --> 00:02:54,560
like when you're
trying to remember

54
00:02:54,560 --> 00:02:56,805
the birthday of your friend.

55
00:02:56,805 --> 00:03:02,460
This brings me to my
younger daughter, Rachel.

56
00:03:02,460 --> 00:03:07,130
Rachel was, not long before
this video was filmed,

57
00:03:07,130 --> 00:03:10,025
learning how to back up a car.

58
00:03:10,025 --> 00:03:14,050
I asked her to model
for us what it felt

59
00:03:14,050 --> 00:03:18,350
like when she was first
learning to back up that car.

60
00:03:18,350 --> 00:03:22,390
If you watch Rachel,
watch carefully.

61
00:03:22,390 --> 00:03:25,725
You'll notice her
little face is like,

62
00:03:25,725 --> 00:03:28,370
which mirror should I look in?

63
00:03:28,370 --> 00:03:30,170
Should I look in the front?

64
00:03:30,170 --> 00:03:33,670
Maybe the back, and then,

65
00:03:33,670 --> 00:03:37,730
of course, off she
goes into the ditch.

66
00:03:37,730 --> 00:03:40,970
Now, the thing is
that when she was

67
00:03:40,970 --> 00:03:44,270
first learning about
how to back up a car,

68
00:03:44,270 --> 00:03:49,610
Rachel's working memory had
a heavy cognitive load.

69
00:03:49,610 --> 00:03:53,070
She was trying to learn
this information,

70
00:03:53,070 --> 00:03:56,250
but all of the arms
on her octopus

71
00:03:56,250 --> 00:04:00,190
or quadrupus were taken
up with information,

72
00:04:00,190 --> 00:04:03,090
and so it was really
hard for her to think

73
00:04:03,090 --> 00:04:07,485
and to maneuver and to
actually back up the car.

74
00:04:07,485 --> 00:04:11,450
But once she created
those sets of links,

75
00:04:11,450 --> 00:04:15,510
it became very easy for
her to back up the car.

76
00:04:15,510 --> 00:04:17,750
All she had to do was think,

77
00:04:17,750 --> 00:04:20,030
I want to back up the car,

78
00:04:20,030 --> 00:04:24,030
and she pulls those
neural links to mind.

79
00:04:24,030 --> 00:04:27,770
She has a light cognitive load.

80
00:04:27,770 --> 00:04:30,710
You can see that on the octopus,

81
00:04:30,710 --> 00:04:33,710
some of the arms are
free on her and she

82
00:04:33,710 --> 00:04:37,170
can think about not only
backing up the car,

83
00:04:37,170 --> 00:04:39,630
but also about other things

84
00:04:39,630 --> 00:04:42,030
with those other octopus arms,

85
00:04:42,030 --> 00:04:44,450
like what's playing
on the radio or

86
00:04:44,450 --> 00:04:47,570
whether her seat
belt is fastened.

87
00:04:47,570 --> 00:04:52,910
Ultimately, she's got more
working memory available for

88
00:04:52,910 --> 00:04:56,150
more complex thinking
because she's already

89
00:04:56,150 --> 00:05:00,350
learned this information
about how to back up a car.

90
00:05:00,350 --> 00:05:04,670
Now, when we go to take
a test, for example,

91
00:05:04,670 --> 00:05:09,490
what we want to be able
to do is to be creative,

92
00:05:09,490 --> 00:05:12,050
but also to access

93
00:05:12,050 --> 00:05:16,435
information that relates to
what we're being tested on.

94
00:05:16,435 --> 00:05:20,300
What we're really doing
as we're being tested

95
00:05:20,300 --> 00:05:26,040
is our working memories
are hopping up there,

96
00:05:26,040 --> 00:05:29,900
they're pulling up various sets

97
00:05:29,900 --> 00:05:31,560
of neural links that we have

98
00:05:31,560 --> 00:05:34,125
practiced with and
are familiar with,

99
00:05:34,125 --> 00:05:38,680
and often putting them
together in novel ways.

100
00:05:38,680 --> 00:05:44,505
This is optimally how we
want to prepare for tests.

101
00:05:44,505 --> 00:05:47,750
But sometimes what
can happen is,

102
00:05:47,750 --> 00:05:52,550
if people have not practice
using retrieval practice,

103
00:05:52,550 --> 00:05:56,090
they can end up sitting
down to the test,

104
00:05:56,090 --> 00:06:00,550
and that's the first time
they begin to realize that

105
00:06:00,550 --> 00:06:02,470
they haven't actually put

106
00:06:02,470 --> 00:06:05,025
the information into
long-term memory,

107
00:06:05,025 --> 00:06:09,290
and so there's nothing
there for them to access.

108
00:06:09,290 --> 00:06:12,330
Learning requires
a careful dance

109
00:06:12,330 --> 00:06:15,590
between long-term memory
and working memory,

110
00:06:15,590 --> 00:06:17,970
and we need to have both

111
00:06:17,970 --> 00:06:23,395
prepared in order to be able
to do good creative work.

112
00:06:23,395 --> 00:06:27,800
Remember, we only
on average have

113
00:06:27,800 --> 00:06:32,540
four arms on our quadrupus
or our working memory.

114
00:06:32,540 --> 00:06:34,780
That means we can't really hold

115
00:06:34,780 --> 00:06:38,640
very much information
in mind at once.

116
00:06:38,640 --> 00:06:42,240
But generative AI,
on the other hand,

117
00:06:42,240 --> 00:06:46,060
has tens of thousands of arms.

118
00:06:46,060 --> 00:06:48,760
It can put together
information in

119
00:06:48,760 --> 00:06:52,120
creative new ways
that can be difficult

120
00:06:52,120 --> 00:06:54,680
for us to do because

121
00:06:54,680 --> 00:06:59,360
we don't have an
immense working memory.

122
00:06:59,490 --> 00:07:03,490
Generative AI is a
wonderful supplement

123
00:07:03,490 --> 00:07:05,190
that we can use when we

124
00:07:05,190 --> 00:07:07,530
want to be creative or simply

125
00:07:07,530 --> 00:07:11,010
want to look at things with
different perspectives.

126
00:07:11,010 --> 00:07:16,690
It's valuable to use generative
AI from this perspective,

127
00:07:16,690 --> 00:07:22,590
and it's valuable for us
to join the AI revolution.

128
00:07:22,590 --> 00:07:26,370
That generative AI
revolution will

129
00:07:26,370 --> 00:07:31,170
bring immense creativity
to our own lives.

130
00:07:31,170 --> 00:07:34,410
Let's turn now to Jules
who will give you

131
00:07:34,410 --> 00:07:39,050
some more insight into
how this can take place.