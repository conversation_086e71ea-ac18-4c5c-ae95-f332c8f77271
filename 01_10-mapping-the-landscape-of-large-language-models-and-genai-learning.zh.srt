1
00:00:00,240 --> 00:00:05,190
和 我们 ' re back 在 这个 视频 , 我 ' m going 到 显示 你 如何 你

2
00:00:05,190 --> 00:00:10,437
能 想 关于 这个 bigger picture 的 large 语言 models 和

3
00:00:10,437 --> 00:00:13,950
生成式 AI 在 你的 学习 .

4
00:00:13,950 --> 00:00:15,521
如果 你 ' ll remember ,

5
00:00:15,521 --> 00:00:21,230
我们 有 talked 关于 一些 的 这个 不同的 foundational models earlier .

6
00:00:21,230 --> 00:00:26,736
现在 我 ' ll focus 在 在 foundational models 从 这个 west .

7
00:00:26,736 --> 00:00:31,845
google ' s gemini , incidentally , 是 interesting 因为 google

8
00:00:31,845 --> 00:00:37,190
有 access 到 所有 这些 youtube videos 为了 training its engine .

9
00:00:37,190 --> 00:00:42,924
但是 anyway , 你 能 看 那个 这些 不同的 foundational

10
00:00:42,924 --> 00:00:48,070
models 有 一个 lot 的 apps 那个 grow 从 他们 .

11
00:00:48,070 --> 00:00:52,852
api , incidentally , 是 一个 common term 那个 stands 为了

12
00:00:52,852 --> 00:00:56,950
application programming interface .

13
00:00:56,950 --> 00:01:01,878
在 这个 计算机 world , 一个 api 是 一个 set 的 protocols ,

14
00:01:01,878 --> 00:01:08,190
routines 和 tools 为了 building software applications .

15
00:01:08,190 --> 00:01:14,396
它 specifies 如何 software components 应该 interact 与 每个 其他 ,

16
00:01:14,396 --> 00:01:21,600
allowing 不同的 programs 或 systems 到 communicate 和 share 数据 .

17
00:01:21,600 --> 00:01:26,460
通过 这个 使用 的 apis , 我们 有 apps 和 tools 为了

18
00:01:26,460 --> 00:01:30,628
specific functions 许多 的 这个 新的 apps 和

19
00:01:30,628 --> 00:01:34,333
tools 那个 有 arisen 有 grown 从

20
00:01:34,333 --> 00:01:39,760
ChatGPT 因为 它 ' s 是 first out 的 这个 gate .

21
00:01:39,760 --> 00:01:46,220
let ' s 拿 一个 quick overview 在 just 一个 few 的 这个 currently available tools .

22
00:01:47,450 --> 00:01:52,081
这里 你 看 一些 的 这个 top writing assistants .

23
00:01:52,081 --> 00:01:59,147
那里 ' s jennie 和 那里 ' s grammarly , 哪个 是 pretty familiar program 为了

24
00:01:59,147 --> 00:02:04,810
许多 , 但是 那里 ' s 许多 更多 like writesonic 和 jasper .

25
00:02:04,810 --> 00:02:08,618
这些 tools 能 真的 帮助 你 write .

26
00:02:08,618 --> 00:02:14,139
它 ' s worth 你的 time 到 去 和 look 在 一些 的 他们的 free access versions

27
00:02:14,139 --> 00:02:20,137
到 看 什么 approaches 他们 使用 那个 能 帮助 你 write 所有 sorts 的 material .

28
00:02:20,137 --> 00:02:24,977
从 podcast scripts 到 blog posts 到 essays 和

29
00:02:24,977 --> 00:02:28,990
很多 更多 和 很多 更多 easily .

30
00:02:28,990 --> 00:02:34,990
当 它 comes 到 research , 那里 ' s 一个 boatload 的 tools available .

31
00:02:34,990 --> 00:02:41,770
这些 tools 是 often trained 在 research rather than 这个 web as 一个 whole .

32
00:02:41,770 --> 00:02:47,666
这个 web 是 huge , 但是 它 also 有 一个 lot 的 garbage out 那里 .

33
00:02:47,666 --> 00:02:54,158
那里 是 satire 和 parody websites 哪个 我 personally kind 的 enjoy .

34
00:02:54,158 --> 00:02:59,684
但是 那里 ' s also 任何 number 的 just 真的 bizarre things out 在 这个 web ,

35
00:02:59,684 --> 00:03:06,570
和 许多 large foundational models 能 ' t 帮助 但是 include 他们 在 他们的 training .

36
00:03:06,570 --> 00:03:10,120
但是 research models 能 是 不同的 .

37
00:03:10,120 --> 00:03:19,290
为了 例子 , semantic scholar 是 trained 在 over 200 million research papers .

38
00:03:19,290 --> 00:03:23,660
然后 那里 是 direct live 到 internet tools ,

39
00:03:23,660 --> 00:03:28,712
including copilot , 哪个 你 可能 是 familiar 与 .

40
00:03:28,712 --> 00:03:35,010
那里 是 also powerpoint generators , 但是 我 ' d 说 到 是 一个 little careful 与 他们 .

41
00:03:35,010 --> 00:03:39,713
在 我的 经验 , 他们 often result 在 bland presentations

42
00:03:39,713 --> 00:03:44,080
与 lots 的 bullets 和 generic looking pictures .

43
00:03:44,080 --> 00:03:48,020
also , beware 的 什么 是 called wrappers .

44
00:03:49,080 --> 00:03:52,698
这些 aren ' t wrappers as 在 这个 singers , 但是

45
00:03:52,698 --> 00:03:56,312
wrappers like 一个 wrapping around 一个 package .

46
00:03:56,312 --> 00:04:00,511
一些 的 这些 websites involve simplistic wrappers 那个 wrap

47
00:04:00,511 --> 00:04:02,698
themselves around 这个 engine .

48
00:04:02,698 --> 00:04:07,521
但是 don ' t add 很多 value beyond 什么 你 能 做 yourself 通过 simply

49
00:04:07,521 --> 00:04:11,120
提示 你的 engine 关于 什么 你 want .

50
00:04:11,120 --> 00:04:16,120
所以 你 want 到 play 与 这个 free versions 的 一些 的 这些 apps

51
00:04:16,120 --> 00:04:20,940
到 看 如果 他们 是 genuinely worth 这个 subscription fees .

52
00:04:22,160 --> 00:04:25,640
那里 是 tools 为了 teachers 和 instructors .

53
00:04:25,640 --> 00:04:30,850
这些 include magic school , 教 mate 和 所以 forth .

54
00:04:30,850 --> 00:04:33,034
need 到 write 一个 课程 plan ?

55
00:04:33,034 --> 00:04:36,530
这些 sites 能 制作 它 一个 snap .

56
00:04:36,530 --> 00:04:43,330
然后 那里 是 creative productivity platforms like canva 和 gamma .

57
00:04:43,330 --> 00:04:49,360
as far as coding tools , 那里 是 various options available 和

58
00:04:49,360 --> 00:04:54,280
当 它 comes 到 imagery , 那里 是 许多 tools .

59
00:04:54,280 --> 00:04:59,247
people 是 often aware 的 dall - e , 哪个 grows 从 ChatGPT ,

60
00:04:59,247 --> 00:05:04,028
但是 那里 ' s also imagery arising 从 independent sources

61
00:05:04,028 --> 00:05:07,888
like mid journey , dream studio 和 所以 forth .

62
00:05:07,888 --> 00:05:13,179
likewise , 那里 ' s runway growing 从 gemini ,

63
00:05:13,179 --> 00:05:17,768
和 那里 是 lots 和 lots 的 视频 tools .

64
00:05:17,768 --> 00:05:23,065
这个 是 真的 growing fast 和 comes 从 一个 number 的 不同的 sources 和

65
00:05:23,065 --> 00:05:25,260
also independent platforms .

66
00:05:26,560 --> 00:05:30,587
所有 的 这个 provides just 一个 little hint 的 一些 的

67
00:05:30,587 --> 00:05:34,440
这个 不同的 tools 那个 是 out 那里 .

68
00:05:34,440 --> 00:05:38,448
这个 方式 我 ' m presenting things 这里 grew 从

69
00:05:38,448 --> 00:05:43,472
一个 beautiful description 在 这个 book teaching 与 AI .

70
00:05:43,472 --> 00:05:49,400
它 ' s 一个 wonderful book 通过 jose bowen 和 c edward watson .

71
00:05:49,400 --> 00:05:50,944
我 love 这个 book .

72
00:05:50,944 --> 00:05:57,097
它 gives 你 一个 lot 的 好的 ideas as far as 两个 teaching 和 学习 .

73
00:05:57,097 --> 00:06:02,898
我 also want 到 bring 到 你的 attention ChatGPT 和 这个 future 的 AI ,

74
00:06:02,898 --> 00:06:07,890
哪个 是 written 通过 我的 colleague terry sejnowski .

75
00:06:07,890 --> 00:06:13,940
它 ' s 一个 terrific book 那个 gives 你 一个 nice solid foundational

76
00:06:13,940 --> 00:06:19,530
知识 的 insight 进入 如何 生成式 AI works .

77
00:06:19,530 --> 00:06:26,090
我 also love 这个 book co - 智能 通过 ethan mollick , 它 ' s 一个 great book .

78
00:06:26,090 --> 00:06:30,463
所有 的 这些 是 practical kinds 的 books 那个 给 你 insights

79
00:06:30,463 --> 00:06:35,170
那个 你 能 拿 away 和 使用 到 帮助 enhance 你的 工作 .

80
00:06:35,170 --> 00:06:40,330
那里 是 also theoretical books 那个 能 是 非常 helpful .

81
00:06:40,330 --> 00:06:47,210
one 的 我的 favorites 是 这个 world ' s 我 看 通过 fei - fei li .

82
00:06:47,210 --> 00:06:51,935
它 talks 关于 如何 她 came 上 与 一些 的 这个 ideas 那个

83
00:06:51,935 --> 00:06:57,250
helped underpin 这个 development 的 生成式 AI .

84
00:06:57,250 --> 00:07:01,930
这个 book 这个 alignment 问题 通过 brian christian 是

85
00:07:01,930 --> 00:07:07,898
一个 enlightening book 那个 gives 你 一个 sense 的 如何 我们 真的 学习 .

86
00:07:07,898 --> 00:07:14,110
和 lastly , 那里 ' s 一个 好的 worldwide overview book 的 AI 通过 kai - fu lee .

87
00:07:15,330 --> 00:07:20,005
好的 courses 在 生成式 AI , 的 课程 , include ,

88
00:07:20,005 --> 00:07:23,340
well , 相当 一个 few 从 Coursera .

89
00:07:23,340 --> 00:07:28,451
和 我 also 必须 point 到 这个 inimitable 朱尔斯 怀特 ,

90
00:07:28,451 --> 00:07:31,780
谁 有 created 许多 courses .

91
00:07:31,780 --> 00:07:36,410
这个 是 just 一个 little bite sized bit 的 insight 进入

92
00:07:36,410 --> 00:07:41,260
这个 许多 courses 那个 朱尔斯 有 created .

93
00:07:41,260 --> 00:07:49,700
现在 let ' s turn 到 greg brockman , 谁 ' s 这个 co - founder 和 president 的 openai .

94
00:07:49,700 --> 00:07:56,760
他 famously wrote out 在 一个 little notebook page 这个 instructions 为了 一个 joke website .

95
00:07:56,760 --> 00:08:00,904
他 just took 一个 picture 的 他的 notebook page 和

96
00:08:00,904 --> 00:08:04,940
loaded 那个 onto ChatGPT , lol 和 behold ,

97
00:08:04,940 --> 00:08:10,300
什么 他 got 是 这个 coding needed 到 create 一个 joke website .

98
00:08:11,440 --> 00:08:16,800
我 took 一个 picture 的 brockman ' s picture 和 做 这个 myself .

99
00:08:16,800 --> 00:08:22,608
我 tried 它 在 ChatGPT , 和 ChatGPT 是 like , sure , 这里 你 去 .

100
00:08:22,608 --> 00:08:24,980
和 它 gave 我 这个 coding .

101
00:08:24,980 --> 00:08:30,628
然后 我 went 到 claude , 哪个 是 另一个 的 我的 favorite large 语言 models .

102
00:08:30,628 --> 00:08:34,692
claude said , 我 看 这个 joke .

103
00:08:34,692 --> 00:08:38,380
你 want 我 到 create 一个 joke website ?

104
00:08:38,380 --> 00:08:40,943
我 am 不 going 到 做 它 , 因为 guess 什么 ?

105
00:08:40,943 --> 00:08:44,980
你 能 hurt people ' s feelings 与 这个 joke website .

106
00:08:44,980 --> 00:08:49,968
我 有 到 argue 与 claude 到 得到 它 到 finally admit 那个

107
00:08:49,968 --> 00:08:54,956
我们 做 不 want 到 有 一个 completely humorless world 和

108
00:08:54,956 --> 00:08:59,416
那个 它 能 是 valuable 到 有 一个 joke website .

109
00:08:59,416 --> 00:09:03,330
这个 brings 我 到 一个 重要的 想法 .

110
00:09:03,330 --> 00:09:09,669
每个 生成式 AI platform 有 its own individual characteristics ,

111
00:09:09,669 --> 00:09:13,778
这个 模型 architecture 那个 你 是 using .

112
00:09:13,778 --> 00:09:18,911
incidentally , 我 应该 note 那个 这个 GPT 在 GPT - 4 ,

113
00:09:18,911 --> 00:09:25,344
为了 例子 , stands 为了 生成式 pre - trained transformer .

114
00:09:25,344 --> 00:09:30,456
它 能 是 closed , meaning 我们 don ' t 知道 什么 这个 weights

115
00:09:30,456 --> 00:09:36,600
的 那些 神经的 nets underlying 这个 fundamental 模型 是 .

116
00:09:36,600 --> 00:09:42,334
那里 是 actually models 哪里 我们 做 知道 什么 这个 weights 是 like llama .

117
00:09:42,334 --> 00:09:47,440
这些 是 called open , 或 在 least relatively open engines .

118
00:09:48,460 --> 00:09:56,340
这个 模型 architecture 在...之间 不同的 engines 能 是 真的 相当 不同的 .

119
00:09:56,340 --> 00:09:59,404
这个 training used 能 是 不同的 .

120
00:09:59,404 --> 00:10:04,168
ChatGPT trained 在 这个 massive internet , 为了 例子 ,

121
00:10:04,168 --> 00:10:11,270
while semantic scholar , as 我 mentioned earlier , trained 在 research papers .

122
00:10:11,270 --> 00:10:16,101
这个 training 数据 used 能 actually 制作 一个 difference 在 什么 你

123
00:10:16,101 --> 00:10:21,565
经验 当 你 去 到 一些 的 这些 不同的 foundational models .

124
00:10:21,565 --> 00:10:23,890
tuning 能 是 不同的 .

125
00:10:23,890 --> 00:10:28,118
它 能 是 optimized 为了 , perhaps conversational

126
00:10:28,118 --> 00:10:32,870
responses versus 在 depth research responses .

127
00:10:32,870 --> 00:10:37,400
这些 engines 能 also 有 不同的 personalities .

128
00:10:37,400 --> 00:10:44,320
一些 的 他们 是 非常 formal 和 informative , others 是 friendlier .

129
00:10:44,320 --> 00:10:50,343
我 suppose 这个 是 为什么 当 我 ' m arguing 与 一个 生成式 AI engine ,

130
00:10:50,343 --> 00:10:57,464
我 能 找到 那个 他们 能 respond 相当 differently 与 所有 的 这些 insights .

131
00:10:57,464 --> 00:11:03,808
你 ' ve got 一个 little bit 的 一个 foundation 现在 yourself 到 move forward , 但是

132
00:11:03,808 --> 00:11:10,070
你 ' ll 做 even better as 朱尔斯 brings 这些 ideas even further 到 life .

