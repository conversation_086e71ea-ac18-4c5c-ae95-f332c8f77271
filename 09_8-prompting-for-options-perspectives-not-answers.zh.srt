1
00:00:00,960 --> 00:00:04,118
Barb's told us some really important
things that we have to take to heart when

2
00:00:04,118 --> 00:00:06,678
we start using generative AI,
particularly in learning.

3
00:00:06,678 --> 00:00:11,466
And one of those important things
is that AI won't replace humans,

4
00:00:11,466 --> 00:00:15,998
but humans that use AI will
replace other humans that don't.

5
00:00:15,998 --> 00:00:18,588
That's really important for
us to think about.

6
00:00:18,588 --> 00:00:21,082
So how do we go and engage with it and

7
00:00:21,082 --> 00:00:24,269
learn to use it so
that we aren't replaced?

8
00:00:24,269 --> 00:00:26,923
But at the same time,
we've seen the Flynn effect, right?

9
00:00:26,923 --> 00:00:31,201
We don't want to have something happen to
ourselves similar to that introduction

10
00:00:31,201 --> 00:00:34,162
of the calculator,
where suddenly our IQ goes down.

11
00:00:34,162 --> 00:00:37,586
So what are some techniques that
we can use to help combat this?

12
00:00:37,586 --> 00:00:41,342
Well, if you think about what we do a lot
of times with generative AI is we go in

13
00:00:41,342 --> 00:00:43,188
and we try to get to an answer faster.

14
00:00:43,188 --> 00:00:45,098
We're trying to get an answer,

15
00:00:45,098 --> 00:00:49,706
we're trying to offload the solving of
the problem onto the generative AI.

16
00:00:49,706 --> 00:00:52,666
Write this email for
me, get it done for me.

17
00:00:52,666 --> 00:00:56,067
And then what we see is that
people become even more and

18
00:00:56,067 --> 00:00:59,922
more used to just accepting
the answer that it's giving us,

19
00:00:59,922 --> 00:01:03,678
even if we can be possibly flawed or
incomplete or draft.

20
00:01:03,678 --> 00:01:06,952
The most classic example of this
is I receive emails sometimes from

21
00:01:06,952 --> 00:01:09,414
students where they have
this beautiful email.

22
00:01:09,414 --> 00:01:10,822
Would you write me a recommendation?

23
00:01:10,822 --> 00:01:14,962
And I received one the other day that
said, you know, would you let me come and

24
00:01:14,962 --> 00:01:17,630
be a research intern in
your lab over the summer?

25
00:01:17,630 --> 00:01:20,124
And then it had, in brackets,
fill in your name here,

26
00:01:20,124 --> 00:01:23,520
and the student hadn't even bothered
to read it and fill in their name.

27
00:01:23,520 --> 00:01:27,535
And that's exactly the type of
thing we want to guard against,

28
00:01:27,535 --> 00:01:31,343
is this trust in it where we just
let it do all the work for us.

29
00:01:31,343 --> 00:01:35,000
Now, what is something that I
can absolutely guarantee that we

30
00:01:35,000 --> 00:01:36,935
can replace with generative AI?

31
00:01:36,935 --> 00:01:41,036
Well, if your role in life is to copy and
paste my question from my test

32
00:01:41,036 --> 00:01:45,423
into generative AI, get the answer
that generative AI comes up with, and

33
00:01:45,423 --> 00:01:48,920
then copy and paste the answer
from generative AI back in.

34
00:01:48,920 --> 00:01:53,684
And you've done nothing to enhance or
improve or think through the answer,

35
00:01:53,684 --> 00:01:57,557
then that's something that we
absolutely can automate, and

36
00:01:57,557 --> 00:01:59,604
that's not where we want to go.

37
00:01:59,604 --> 00:02:02,548
So how do we use it in a way that's
more intentional and thoughtful?

38
00:02:02,548 --> 00:02:07,044
Well, the most basic thing we can do is we
can tap into this generative capability.

39
00:02:07,044 --> 00:02:10,124
And what we do is we never ask for
an answer.

40
00:02:10,124 --> 00:02:13,448
We always ask for options and
perspectives, and our goal is

41
00:02:13,448 --> 00:02:18,038
to start thinking more about the problems
we're solving when we bring them to AI,

42
00:02:18,038 --> 00:02:19,117
degenerative AI.

43
00:02:19,117 --> 00:02:22,758
We don't want to start thinking less
because we're prompting in the wrong way.

44
00:02:22,758 --> 00:02:24,733
So what does this look like?

45
00:02:24,733 --> 00:02:28,549
Well, the first thing we're going to
do is we're never going to ask for

46
00:02:28,549 --> 00:02:29,766
one of anything.

47
00:02:29,766 --> 00:02:33,523
We do not want just one,
because the moment we have just one,

48
00:02:33,523 --> 00:02:36,670
it has given us the answer
that is not what we want.

49
00:02:36,670 --> 00:02:39,537
Instead, the bare minimum we
want is we want to ask for

50
00:02:39,537 --> 00:02:41,206
more than one of something.

51
00:02:41,206 --> 00:02:45,778
Because the moment we have options that as
a human being, I have to think about which

52
00:02:45,778 --> 00:02:49,760
option do I want to choose and why my
own personal aesthetics come in, but

53
00:02:49,760 --> 00:02:53,940
also my reasoning, my critical thinking,
my knowledge of the situation,

54
00:02:53,940 --> 00:02:57,898
all the things that are important
that cannot be replaced about me.

55
00:02:57,898 --> 00:03:01,370
And also it allows me to practice
something that's going to be very

56
00:03:01,370 --> 00:03:05,772
important in terms of skills when we start
using generative AI all the time, which is

57
00:03:05,772 --> 00:03:10,112
our ability to be the editor and selector,
the one who is going and curating for many

58
00:03:10,112 --> 00:03:14,582
different options, putting them together
or using them to inspire other things.

59
00:03:14,582 --> 00:03:16,950
So let's look at an example of this.

60
00:03:16,950 --> 00:03:21,550
Give me five completely different ways
to build a chair out of Cheerios and

61
00:03:21,550 --> 00:03:23,486
compare and contrast them.

62
00:03:23,486 --> 00:03:26,183
The chair needs to be strong
enough to support a real person.

63
00:03:26,183 --> 00:03:31,186
So what I'm trying to do is I'm
bringing a problem to generative AI,

64
00:03:31,186 --> 00:03:35,864
and I'm asking it to give me
possible solutions to the problem.

65
00:03:35,864 --> 00:03:41,648
But also to give me perspectives on why
I might select one solution or another.

66
00:03:41,648 --> 00:03:45,247
Now, something Barb talked about is
this idea that often problems and

67
00:03:45,247 --> 00:03:46,800
answers are hidden from us.

68
00:03:46,800 --> 00:03:49,976
And this is a tool that can give us
lots of different problems and answers.

69
00:03:49,976 --> 00:03:55,853
But even more importantly, it can give us
many different approaches and perspectives

70
00:03:55,853 --> 00:04:01,018
on how to solve the problem and how one
approach or the other might be better.

71
00:04:01,018 --> 00:04:05,633
So we then get back, Cheerio and
resin composite chair, so we could go and

72
00:04:05,633 --> 00:04:08,096
put Cheerios in a resin, apparently.

73
00:04:08,096 --> 00:04:12,264
Mix Cheerios with a strong epoxy
resin to create a composite material.

74
00:04:12,264 --> 00:04:15,167
We get Cheerio brick
chair create bricks by

75
00:04:15,167 --> 00:04:19,939
compressing cheerios into dense
blocks using a food grade adhesive.

76
00:04:19,939 --> 00:04:21,862
Cheerio and metal framework chair,

77
00:04:21,862 --> 00:04:24,910
use a metal framework as
the structural core of the chair.

78
00:04:24,910 --> 00:04:28,459
Attach Cheerios to the metal
frame using a strong adhesive.

79
00:04:28,459 --> 00:04:32,559
Compressed Cheerio plywood chair,
grind Cheerios into a pattern and

80
00:04:32,559 --> 00:04:36,466
mix with a binding agent to create
a dense wood-like material.

81
00:04:36,466 --> 00:04:40,002
And then we get Cheerio
reinforced polymer chair.

82
00:04:40,002 --> 00:04:41,967
But what has this done for us?

83
00:04:41,967 --> 00:04:46,530
It's given us different perspectives
on solving the problem.

84
00:04:46,530 --> 00:04:52,023
We still have to go and decide what
is the appropriate solution and why.

85
00:04:52,023 --> 00:04:55,793
Now if you think about what we do in art,
for example, I started off as

86
00:04:55,793 --> 00:04:59,708
a visual arts major, and one of the things
we do is we go into the museum.

87
00:04:59,708 --> 00:05:02,061
We look at all the paintings on the wall,
and

88
00:05:02,061 --> 00:05:04,993
we see all the different
styles of expressing things.

89
00:05:04,993 --> 00:05:09,886
All the different ways that you can use
paint to achieve some idea that you're

90
00:05:09,886 --> 00:05:15,077
trying to communicate, or feeling, or
whatever it is that you're trying to do,

91
00:05:15,077 --> 00:05:17,958
but you're learning from seeing examples.

92
00:05:17,958 --> 00:05:19,891
And that's what we're getting here,

93
00:05:19,891 --> 00:05:24,090
is we're getting lots of different
perspectives or options on the problem.

94
00:05:24,090 --> 00:05:25,946
There's no one way to paint a flower.

95
00:05:25,946 --> 00:05:27,793
There's many different
ways to paint a flower.

96
00:05:27,793 --> 00:05:31,797
And the more of them I've been exposed to,
the more I can inform my own thinking and

97
00:05:31,797 --> 00:05:34,122
sensibilities on how I
want to paint a flower.

98
00:05:34,122 --> 00:05:38,672
Or I might learn some new technique or
some new visual sensibility that I care

99
00:05:38,672 --> 00:05:42,058
about from this experience
of walking into the museum.

100
00:05:42,058 --> 00:05:45,775
And what we've done is we've just
unlocked the museum where we've walked

101
00:05:45,775 --> 00:05:49,492
into a museum, and now we've seen all
these different ways to build a chair

102
00:05:49,492 --> 00:05:51,922
out of Cheerios that's
strong enough to sit in.

103
00:05:51,922 --> 00:05:55,535
But we've also learned why we might
use one of the other, because we see,

104
00:05:55,535 --> 00:05:56,982
it now says, comparison.

105
00:05:56,982 --> 00:06:00,888
Strength, very high, durability,
very high, aesthetic, modern and

106
00:06:00,888 --> 00:06:02,469
sleek, complexity, high.

107
00:06:02,469 --> 00:06:06,166
But it's also done this for each one of
them, and so it's informed our thinking.

108
00:06:06,166 --> 00:06:09,422
So those problems don't
have to be hidden anymore.

109
00:06:09,422 --> 00:06:11,891
We can get the problem and
the solution, but

110
00:06:11,891 --> 00:06:15,078
we can also get why we might
solve it one particular way.

111
00:06:15,078 --> 00:06:17,204
And if we don't like
that way of solving it,

112
00:06:17,204 --> 00:06:20,368
we can get many different potential
ways of solving the problem.

113
00:06:20,368 --> 00:06:24,102
And this is some of the most valuable
information that we can get out of it is

114
00:06:24,102 --> 00:06:26,126
ways that we might go about solving it.

115
00:06:26,126 --> 00:06:29,631
We go and look into the museum of
ways of solving the problem to

116
00:06:29,631 --> 00:06:30,955
inform our thinking.

117
00:06:30,955 --> 00:06:34,873
And in the process of being exposed to the
different ways of thinking about solving

118
00:06:34,873 --> 00:06:39,150
the problem, the different solution
approaches, we're learning something.

119
00:06:39,150 --> 00:06:41,531
Now, what else might we do?

120
00:06:41,531 --> 00:06:45,480
Well, as humans,
we are really biased, right?

121
00:06:45,480 --> 00:06:48,680
We talk all the time about AI bias.

122
00:06:48,680 --> 00:06:53,761
But that bias in AI came from the human
beings that created it, right?

123
00:06:53,761 --> 00:06:58,624
And it turns out that generative
AI can help us identify and

124
00:06:58,624 --> 00:07:00,968
overcome our own biases.

125
00:07:00,968 --> 00:07:02,939
Now, how can it do this?

126
00:07:02,939 --> 00:07:09,070
We simply ask it to give us multiple,
conflicting interpretations of something.

127
00:07:09,070 --> 00:07:13,880
And suddenly we now have to think through
and reconcile them in our head, but

128
00:07:13,880 --> 00:07:19,013
it forces us to confront different
eventualities or different possibilities.

129
00:07:19,013 --> 00:07:22,370
Different paths that maybe
we hadn't considered and

130
00:07:22,370 --> 00:07:24,690
potentially overcome our own bias.

131
00:07:24,690 --> 00:07:26,114
So let's look at this example.

132
00:07:26,114 --> 00:07:28,594
I've given it some
economic data in a chart.

133
00:07:28,594 --> 00:07:33,837
I've given ChatGPT a picture of some
economic data about the United States.

134
00:07:33,837 --> 00:07:38,402
And I say, give me three different
conflicting interpretations of this data.

135
00:07:38,402 --> 00:07:42,323
So I might have confirmation bias and
I might have interpreted it one way, and

136
00:07:42,323 --> 00:07:46,301
then, therefore, I may go and do a whole
bunch of things, make a whole bunch of

137
00:07:46,301 --> 00:07:50,434
decisions because of my misinterpretation,
because of my confirmation bias.

138
00:07:50,434 --> 00:07:53,569
And so what I'm doing it here is,
I'm telling it, give me conflicting

139
00:07:53,569 --> 00:07:56,842
interpretations so that I can think
through them and reconcile them.

140
00:07:56,842 --> 00:07:58,341
And then it comes back and it says,

141
00:07:58,341 --> 00:08:00,892
here are three different
conflicting interpretations.

142
00:08:00,892 --> 00:08:04,480
Interpretation 1,
economic growth is strong and resilient.

143
00:08:04,480 --> 00:08:08,480
Interpretation 2,
economic volatility and uncertainty.

144
00:08:08,480 --> 00:08:12,288
And then interpretation three,
recovery from a downturn.

145
00:08:12,288 --> 00:08:17,017
So it's given me different interpretations
of the data that now I have to

146
00:08:17,017 --> 00:08:18,216
think through.

147
00:08:18,216 --> 00:08:22,272
I have to look at its explanation, and
I go, I have to go and understand it, and

148
00:08:22,272 --> 00:08:25,184
I have to think about is what
I think is correct or not.

149
00:08:25,184 --> 00:08:27,942
Now, in some ways,
this is kind of what managers do.

150
00:08:27,942 --> 00:08:31,963
When you're a leader, you're going to be
working with many different people who may

151
00:08:31,963 --> 00:08:35,825
have direct relationships to the data or
to the problems that are being solved.

152
00:08:35,825 --> 00:08:38,925
And they're coming up and
they're presenting to you as the leader.

153
00:08:38,925 --> 00:08:42,068
Here's what information we've gathered,
here's my perspective.

154
00:08:42,068 --> 00:08:44,489
And you may have another leader
who has a different, you know,

155
00:08:44,489 --> 00:08:47,184
person who's reporting to you has
a different perspective on it, and

156
00:08:47,184 --> 00:08:49,684
then another one who has a different
perspective on where to go.

157
00:08:49,684 --> 00:08:54,111
And you, as the leader, now have to decide
what is the direction we take and why,

158
00:08:54,111 --> 00:08:57,870
particularly when they are,
you know, conflicting opinions.

159
00:08:57,870 --> 00:09:02,891
And so this allows you to develop and
practice that skill of going and

160
00:09:02,891 --> 00:09:06,944
looking at a diverse set of options or
perspectives and

161
00:09:06,944 --> 00:09:13,111
understanding how to unify them or select
the best one or understand some broader,

162
00:09:13,111 --> 00:09:18,734
you know, idea or trend that's taking
place that underlies all of them.

163
00:09:18,734 --> 00:09:22,829
But the key is,
is that we've moved away from this light,

164
00:09:22,829 --> 00:09:26,392
cheating where we use it
to do all the work for us.

165
00:09:26,392 --> 00:09:29,831
And we've moved into that world
of the museum where we go in and

166
00:09:29,831 --> 00:09:31,963
we look at perspectives and options.

167
00:09:31,963 --> 00:09:35,951
We do not assume that we're going to
just choose whatever it gives us,

168
00:09:35,951 --> 00:09:39,872
we're not going to just take the first
one and go the fastest and copy and

169
00:09:39,872 --> 00:09:41,480
paste as quickly as we can.

170
00:09:41,480 --> 00:09:45,448
In fact, we want to do everything
in our power not to copy and paste.

171
00:09:45,448 --> 00:09:51,073
Our goal is to inform our own ideas and
thinking, to provide additional materials

172
00:09:51,073 --> 00:09:56,770
to actually build the final solution,
not to let it build the solution for us.

173
00:09:56,770 --> 00:10:01,401
Even better, if we go and look at all the
different ways the problem can be solved,

174
00:10:01,401 --> 00:10:04,641
learn from them, and
then solve the problem ourselves.

175
00:10:04,641 --> 00:10:08,375
Because at the end of the day,
what we do not want is we do not

176
00:10:08,375 --> 00:10:13,331
want to be that automaton that is copying
and pasting somebody else's ideas,

177
00:10:13,331 --> 00:10:17,928
questions, thoughts, whatever it is,
into a large language model.

178
00:10:17,928 --> 00:10:22,720
And then copying the large language
model's thoughts, ideas, or whatever

179
00:10:22,720 --> 00:10:28,034
we're going to call this output, back into
some email, or some form, or some test.

180
00:10:28,034 --> 00:10:31,298
That's the worst thing we can
do to remain relevant in a world

181
00:10:31,298 --> 00:10:34,098
where people are going to
be using generative AI.

182
00:10:34,098 --> 00:10:37,498
The most important thing we can do
is to develop our critical thinking.

183
00:10:37,498 --> 00:10:40,732
Because that critical thinking,
that humanness, those aesthetics,

184
00:10:40,732 --> 00:10:44,071
those things that we care about,
the thing that makes you go into a museum,

185
00:10:44,071 --> 00:10:47,464
see all the paintings, and then paint
something completely different than

186
00:10:47,464 --> 00:10:49,424
the next person who walks in the museum.

187
00:10:49,424 --> 00:10:53,996
That's what makes you unique and important
in this world, not the ability to copy and

188
00:10:53,996 --> 00:10:57,885
paste or photograph an existing painting
and then hand it in as your own.

189
00:10:57,885 --> 00:11:00,234
That's a really, really important concept.

190
00:11:00,234 --> 00:11:03,992
So whenever you go in prompt, don't
ever ask for one of anything, ask for

191
00:11:03,992 --> 00:11:06,240
many different perspectives and options.

