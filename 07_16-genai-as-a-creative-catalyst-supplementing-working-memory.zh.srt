1
00:00:00,000 --> 00:00:03,080
在 这个 视频 , 我们 ' re going 到 是 talking 关于

2
00:00:03,080 --> 00:00:05,380
生成式 AI 和 如何 你 能

3
00:00:05,380 --> 00:00:08,760
使用 它 到 supplement 你的 working 记忆 .

4
00:00:08,760 --> 00:00:11,910
现在 , 什么 是 working 记忆 ?

5
00:00:11,910 --> 00:00:14,520
它 turns out 那个 那个 ' s 这个 部分

6
00:00:14,520 --> 00:00:17,310
的 你的 大脑 或 你的 记忆 ,

7
00:00:17,310 --> 00:00:21,180
哪里 你 ' re just holding something temporarily .

8
00:00:21,180 --> 00:00:25,125
为了 例子 , let ' s 说 someone tells 你 一个 code like

9
00:00:25,125 --> 00:00:29,845
23709 和 他们 want 你 到 remember 它 .

10
00:00:29,845 --> 00:00:32,300
你 repeat 它 到 yourself ,

11
00:00:32,300 --> 00:00:39,240
23709 , 23709 , 和 那个 ' s 什么 你 做 到 remember 它 .

12
00:00:39,240 --> 00:00:43,820
你 ' re repeating 它 as 部分 的 你的 working 记忆 ,

13
00:00:43,820 --> 00:00:46,610
那个 temporary storage place .

14
00:00:46,610 --> 00:00:51,360
我们 能 contrast 这个 与 long - term 记忆 .

15
00:00:51,360 --> 00:00:54,610
long - term 记忆 是 这个 信息

16
00:00:54,610 --> 00:00:57,935
那个 我们 store 所有 around 这个 neocortex ,

17
00:00:57,935 --> 00:00:59,570
和 它 ' s 这个 信息 我们

18
00:00:59,570 --> 00:01:02,130
hold 为了 long periods 的 time .

19
00:01:02,130 --> 00:01:06,550
当 你 ' re remembering 这个 date 的 一些 重要的 event

20
00:01:06,550 --> 00:01:08,190
或 你 ' re trying 到 remember

21
00:01:08,190 --> 00:01:09,590
even 你的 address 或

22
00:01:09,590 --> 00:01:11,210
something else 你 可能 有 learned ,

23
00:01:11,210 --> 00:01:12,880
maybe even 在 school ,

24
00:01:12,880 --> 00:01:17,155
你 ' re drawing 在 你的 long - term 记忆 .

25
00:01:17,155 --> 00:01:20,330
到 帮助 你 visualize 这个 difference

26
00:01:20,330 --> 00:01:23,545
在...之间 working 记忆 和 long - term 记忆 ,

27
00:01:23,545 --> 00:01:25,650
let ' s imagine 那个 在

28
00:01:25,650 --> 00:01:29,410
你的 head 是 something 一个 little like 一个 octopus .

29
00:01:29,410 --> 00:01:31,610
它 ' s 在 这个 front 的 你的 大脑 .

30
00:01:31,610 --> 00:01:34,500
well , 我 like 到 想 的 它

31
00:01:34,500 --> 00:01:38,540
as 如果 你的 working 记忆 是 一个 octopus ,

32
00:01:38,540 --> 00:01:41,525
但是 与 only four arms .

33
00:01:41,525 --> 00:01:44,510
它 ' s something like 一个 quadrupus .

34
00:01:44,510 --> 00:01:47,820
这个 quadrupus lies towards

35
00:01:47,820 --> 00:01:50,400
这个 front mostly 的 你的 大脑

36
00:01:50,400 --> 00:01:56,860
和 holds 关于 在 average four pieces 的 信息 .

37
00:01:56,860 --> 00:01:59,760
这个 size 的 这个 pieces 的

38
00:01:59,760 --> 00:02:02,680
信息 能 vary depending 在

39
00:02:02,680 --> 00:02:04,280
whether 你 知道 一个 lot 关于

40
00:02:04,280 --> 00:02:09,175
这个 material 和 什么 previous training 你 ' ve 有 .

41
00:02:09,175 --> 00:02:12,330
这个 temporary working 记忆

42
00:02:12,330 --> 00:02:15,410
contrasts 与 long - term 记忆 ,

43
00:02:15,410 --> 00:02:21,225
和 那个 long - term 记忆 是 更多 like 一个 set 的 lockers .

44
00:02:21,225 --> 00:02:24,305
inside 那些 lockers scattered

45
00:02:24,305 --> 00:02:28,330
around 你的 大脑 是 sets 的 神经的 links .

46
00:02:28,330 --> 00:02:30,690
当 你 ' re trying 到 学习 something ,

47
00:02:30,690 --> 00:02:34,770
oftentimes 什么 ' s happening 是 你的 working 记忆 是

48
00:02:34,770 --> 00:02:39,835
working 一个 方式 trying 到 create 那些 sets 的 links .

49
00:02:39,835 --> 00:02:42,630
然后 once 它 ' s created 他们 ,

50
00:02:42,630 --> 00:02:46,070
它 stores 他们 在 long - term 记忆 .

51
00:02:46,070 --> 00:02:49,180
然后 later , 你 能 去 around

52
00:02:49,180 --> 00:02:52,760
和 access 那些 sets 的 神经的 links ,

53
00:02:52,760 --> 00:02:54,560
like 当 你 ' re trying 到 remember

54
00:02:54,560 --> 00:02:56,805
这个 birthday 的 你的 friend .

55
00:02:56,805 --> 00:03:02,460
这个 brings 我 到 我的 younger daughter , rachel .

56
00:03:02,460 --> 00:03:07,130
rachel 是 , 不 long 在...之前 这个 视频 是 filmed ,

57
00:03:07,130 --> 00:03:10,025
学习 如何 到 back 上 一个 car .

58
00:03:10,025 --> 00:03:14,050
我 asked 她 到 模型 为了 我们 什么 它 felt

59
00:03:14,050 --> 00:03:18,350
like 当 她 是 first 学习 到 back 上 那个 car .

60
00:03:18,350 --> 00:03:22,390
如果 你 watch rachel , watch carefully .

61
00:03:22,390 --> 00:03:25,725
你 ' ll notice 她 little face 是 like ,

62
00:03:25,725 --> 00:03:28,370
哪个 mirror 应该 我 look 在 ?

63
00:03:28,370 --> 00:03:30,170
应该 我 look 在 这个 front ?

64
00:03:30,170 --> 00:03:33,670
maybe 这个 back , 和 然后 ,

65
00:03:33,670 --> 00:03:37,730
的 课程 , off 她 goes 进入 这个 ditch .

66
00:03:37,730 --> 00:03:40,970
现在 , 这个 thing 是 那个 当 她 是

67
00:03:40,970 --> 00:03:44,270
first 学习 关于 如何 到 back 上 一个 car ,

68
00:03:44,270 --> 00:03:49,610
rachel ' s working 记忆 有 一个 heavy cognitive load .

69
00:03:49,610 --> 00:03:53,070
她 是 trying 到 学习 这个 信息 ,

70
00:03:53,070 --> 00:03:56,250
但是 所有 的 这个 arms 在 她 octopus

71
00:03:56,250 --> 00:04:00,190
或 quadrupus 是 taken 上 与 信息 ,

72
00:04:00,190 --> 00:04:03,090
和 所以 它 是 真的 困难的 为了 她 到 想

73
00:04:03,090 --> 00:04:07,485
和 到 maneuver 和 到 actually back 上 这个 car .

74
00:04:07,485 --> 00:04:11,450
但是 once 她 created 那些 sets 的 links ,

75
00:04:11,450 --> 00:04:15,510
它 became 非常 容易的 为了 她 到 back 上 这个 car .

76
00:04:15,510 --> 00:04:17,750
所有 她 有 到 做 是 想 ,

77
00:04:17,750 --> 00:04:20,030
我 want 到 back 上 这个 car ,

78
00:04:20,030 --> 00:04:24,030
和 她 pulls 那些 神经的 links 到 思维 .

79
00:04:24,030 --> 00:04:27,770
她 有 一个 light cognitive load .

80
00:04:27,770 --> 00:04:30,710
你 能 看 那个 在 这个 octopus ,

81
00:04:30,710 --> 00:04:33,710
一些 的 这个 arms 是 free 在 她 和 她

82
00:04:33,710 --> 00:04:37,170
能 想 关于 不 only backing 上 这个 car ,

83
00:04:37,170 --> 00:04:39,630
但是 also 关于 其他 things

84
00:04:39,630 --> 00:04:42,030
与 那些 其他 octopus arms ,

85
00:04:42,030 --> 00:04:44,450
like 什么 ' s playing 在 这个 radio 或

86
00:04:44,450 --> 00:04:47,570
whether 她 seat belt 是 fastened .

87
00:04:47,570 --> 00:04:52,910
ultimately , 她 ' s got 更多 working 记忆 available 为了

88
00:04:52,910 --> 00:04:56,150
更多 复杂的 思考 因为 她 ' s already

89
00:04:56,150 --> 00:05:00,350
learned 这个 信息 关于 如何 到 back 上 一个 car .

90
00:05:00,350 --> 00:05:04,670
现在 , 当 我们 去 到 拿 一个 test , 为了 例子 ,

91
00:05:04,670 --> 00:05:09,490
什么 我们 want 到 是 able 到 做 是 到 是 creative ,

92
00:05:09,490 --> 00:05:12,050
但是 also 到 access

93
00:05:12,050 --> 00:05:16,435
信息 那个 relates 到 什么 我们 ' re 是 tested 在 .

94
00:05:16,435 --> 00:05:20,300
什么 我们 ' re 真的 doing as 我们 ' re 是 tested

95
00:05:20,300 --> 00:05:26,040
是 我们的 working memories 是 hopping 上 那里 ,

96
00:05:26,040 --> 00:05:29,900
他们 ' re pulling 上 various sets

97
00:05:29,900 --> 00:05:31,560
的 神经的 links 那个 我们 有

98
00:05:31,560 --> 00:05:34,125
practiced 与 和 是 familiar 与 ,

99
00:05:34,125 --> 00:05:38,680
和 often putting 他们 together 在 novel ways .

100
00:05:38,680 --> 00:05:44,505
这个 是 optimally 如何 我们 want 到 prepare 为了 tests .

101
00:05:44,505 --> 00:05:47,750
但是 sometimes 什么 能 happen 是 ,

102
00:05:47,750 --> 00:05:52,550
如果 people 有 不 练习 using retrieval 练习 ,

103
00:05:52,550 --> 00:05:56,090
他们 能 end 上 sitting down 到 这个 test ,

104
00:05:56,090 --> 00:06:00,550
和 那个 ' s 这个 first time 他们 begin 到 realize 那个

105
00:06:00,550 --> 00:06:02,470
他们 haven ' t actually put

106
00:06:02,470 --> 00:06:05,025
这个 信息 进入 long - term 记忆 ,

107
00:06:05,025 --> 00:06:09,290
和 所以 那里 ' s nothing 那里 为了 他们 到 access .

108
00:06:09,290 --> 00:06:12,330
学习 requires 一个 careful dance

109
00:06:12,330 --> 00:06:15,590
在...之间 long - term 记忆 和 working 记忆 ,

110
00:06:15,590 --> 00:06:17,970
和 我们 need 到 有 两个

111
00:06:17,970 --> 00:06:23,395
prepared 在 order 到 是 able 到 做 好的 creative 工作 .

112
00:06:23,395 --> 00:06:27,800
remember , 我们 only 在 average 有

113
00:06:27,800 --> 00:06:32,540
four arms 在 我们的 quadrupus 或 我们的 working 记忆 .

114
00:06:32,540 --> 00:06:34,780
那个 means 我们 能 ' t 真的 hold

115
00:06:34,780 --> 00:06:38,640
非常 很多 信息 在 思维 在 once .

116
00:06:38,640 --> 00:06:42,240
但是 生成式 AI , 在 这个 其他 hand ,

117
00:06:42,240 --> 00:06:46,060
有 tens 的 thousands 的 arms .

118
00:06:46,060 --> 00:06:48,760
它 能 put together 信息 在

119
00:06:48,760 --> 00:06:52,120
creative 新的 ways 那个 能 是 difficult

120
00:06:52,120 --> 00:06:54,680
为了 我们 到 做 因为

121
00:06:54,680 --> 00:06:59,360
我们 don ' t 有 一个 immense working 记忆 .

122
00:06:59,490 --> 00:07:03,490
生成式 AI 是 一个 wonderful supplement

123
00:07:03,490 --> 00:07:05,190
那个 我们 能 使用 当 我们

124
00:07:05,190 --> 00:07:07,530
want 到 是 creative 或 simply

125
00:07:07,530 --> 00:07:11,010
want 到 look 在 things 与 不同的 perspectives .

126
00:07:11,010 --> 00:07:16,690
它 ' s valuable 到 使用 生成式 AI 从 这个 perspective ,

127
00:07:16,690 --> 00:07:22,590
和 它 ' s valuable 为了 我们 到 join 这个 AI revolution .

128
00:07:22,590 --> 00:07:26,370
那个 生成式 AI revolution 将

129
00:07:26,370 --> 00:07:31,170
bring immense creativity 到 我们的 own lives .

130
00:07:31,170 --> 00:07:34,410
let ' s turn 现在 到 朱尔斯 谁 将 给 你

131
00:07:34,410 --> 00:07:39,050
一些 更多 insight 进入 如何 这个 能 拿 place .

