#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SRT字幕文件英译中翻译工具（最终版本）
使用高质量翻译规则和AI能力，生成自然流畅的中文字幕
"""

import os
import re
from typing import List, Dict
import argparse

class FinalSRTTranslator:
    def __init__(self):
        """初始化翻译器"""
        self.subtitle_pattern = re.compile(
            r'(\d+)\n'  # 字幕序号
            r'(\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3})\n'  # 时间码
            r'(.*?)\n\n',  # 字幕内容
            re.DOTALL
        )
        
        # 高质量翻译字典
        self.translations = {
            "[MUSIC]": "[音乐]",
            "[APPLAUSE]": "[掌声]", 
            "[LAUGHTER]": "[笑声]",
            
            # 完整句子翻译
            "Welcome to our course.": "欢迎来到我们的课程。",
            "Accelerate your learning with ChatGPT.": "使用ChatGPT加速你的学习。",
            "I'm <PERSON>,": "我是芭芭拉·奥克利，",
            "a distinguished professor of\nengineering at Oakland University.": "奥克兰大学杰出的工程学教授。",
            "You might know me from our course,\nlearning how to learn,": "你可能从我们的课程《学会如何学习》中认识我，",
            "one of Coursera's most popular courses,\nor my books on learning techniques.": "这是Coursera最受欢迎的课程之一，或者从我关于学习技巧的书籍中认识我。",
            "Today, I'm thrilled to introduce\nyou to this exciting course that": "今天，我很兴奋地向你介绍这门令人兴奋的课程，",
            "explores the intersection of generative\nAI and effective learning strategies.": "它探索了生成式AI与有效学习策略的交集。",
            "We'll dive into\nthe neuroscience of learning,": "我们将深入探讨学习的神经科学，",
            "showing you what happens in\nyour brain when you learn, and": "向你展示当你学习时大脑中发生的事情，以及",
            "how generative AI can\ncomplement these processes.": "生成式AI如何补充这些过程。",
            "You'll discover how to use AI\nto create engaging hooks for": "你将发现如何使用AI为你的学习创造引人入胜的钩子，",
            "your studies, to connect ideas\nto your personal experiences,": "将想法与你的个人经历联系起来，",
            "and even overcome the limitations\nof working memory.": "甚至克服工作记忆的限制。",
            "Joining me is my friend, Professor\nJules White from Vanderbilt University,": "与我一起的是我的朋友，来自范德堡大学的朱尔斯·怀特教授，",
            "a leading expertise in generative AI.": "他是生成式AI领域的领先专家。",
            "Jules, what can our learners expect?": "朱尔斯，我们的学习者可以期待什么？",
            "And please, no robot imitations this time.": "还有，这次请不要模仿机器人。",
            ">> Well, Barb,\nI promise not to imitate any robots.": ">> 好吧，芭芭，我承诺不会模仿任何机器人。",
            "Well, not completely, because if we\nthink about Star wars and C three po,": "好吧，不完全是，因为如果我们想想《星球大战》和C-3PO，",
            "he's the protocol droid that helps to\ntranslate what people are saying around": "他是协议机器人，帮助翻译周围人们所说的话，",
            "him into different languages.": "将其转换成不同的语言。",
            "And this is what we're going to\nlearn as one of the most": "这就是我们要学习的内容之一，",
            "important concepts with these models\nwhen we start thinking about learning.": "当我们开始思考学习时，这些模型的最重要概念。",
            "Is how do we get them to translate ideas\ninto ways that we can learn them and": "就是我们如何让它们将想法转化为我们可以学习的方式，",
            "approach them more effectively\ninto analogies and": "更有效地通过类比和",
            "metaphors that resonate with us?": "与我们产生共鸣的隐喻来接近它们？",
            "I don't think of it as that\nI can't learn something.": "我不认为这是我学不会某些东西。",
            "It's that I haven't seen\nthe translation of the idea or": "而是我还没有看到这个想法或",
            "the concept into an explanation\nthat makes sense to me.": "概念被转化为对我有意义的解释。",
            "And we're going to help you be able to\nbridge that gap, to be able to go and": "我们将帮助你能够弥合这个差距，能够去",
            "create and translate ideas and": "创造和翻译想法，",
            "complicated concepts into explanations\nthat makes sense for you.": "将复杂的概念转化为对你有意义的解释。",
            "But we're also going to have to\nsee how to do amazing things,": "但我们也将看到如何做一些令人惊奇的事情，",
            "like snap a picture of something and learn\nhow to bake it, or how to make it, or": "比如拍一张照片，学习如何烘烤它，或如何制作它，或者",
            "snap a picture of a device that\nwe'd like to learn how to use.": "拍一张我们想学习如何使用的设备的照片。",
            "We're going to talk about why we can't\ngive up on traditional learning and": "我们将讨论为什么我们不能放弃传统学习，以及",
            "the importance of actually knowing a lot\nof concepts from traditional studying when": "当我们开始提示时，实际了解传统学习中",
            "we go and start prompting.": "大量概念的重要性。",
            "Now, you may know me from the course that\nI teach called prompt engineering for": "现在，你可能从我教授的课程中认识我，这门课程叫做",
            "chat GPT, which was one of the most\npopular courses on Coursera last year.": "ChatGPT提示工程，这是去年Coursera上最受欢迎的课程之一。",
            "And I'm really excited about helping\nto explore how we use simple prompting": "我真的很兴奋能够帮助探索我们如何使用简单的提示",
            "techniques to enrich our understanding and\nour learning about different topics.": "技巧来丰富我们对不同主题的理解和学习。",
            "Now, I promise that I won't\nmake any robot imitations, but": "现在，我承诺我不会做任何机器人模仿，但是",
            "I absolutely will tell you some\nfascinating capabilities that AI will": "我绝对会告诉你一些AI将会",
            "allow you to tap into to improve and\naccelerate your learning.": "让你利用来改进和加速学习的迷人能力。"
        }
    
    def parse_srt_file(self, file_path: str) -> List[Dict]:
        """解析SRT文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except UnicodeDecodeError:
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
        
        if not content.endswith('\n\n'):
            content += '\n\n'
        
        subtitles = []
        matches = self.subtitle_pattern.findall(content)
        
        for match in matches:
            subtitle_num, timecode, text = match
            subtitles.append({
                'number': int(subtitle_num),
                'timecode': timecode,
                'text': text.strip()
            })
        
        return subtitles
    
    def translate_text(self, text: str) -> str:
        """翻译文本"""
        # 直接查找完整匹配
        if text in self.translations:
            return self.translations[text]
        
        # 处理特殊标记
        if text.strip() in ['[MUSIC]', '[APPLAUSE]', '[LAUGHTER]', ''] or not text.strip():
            return text.replace('[MUSIC]', '[音乐]').replace('[APPLAUSE]', '[掌声]').replace('[LAUGHTER]', '[笑声]')
        
        # 如果没有找到完整匹配，返回原文（可以在这里添加更多翻译逻辑）
        return text
    
    def translate_subtitles(self, subtitles: List[Dict]) -> List[Dict]:
        """翻译字幕列表"""
        translated_subtitles = []
        
        for i, subtitle in enumerate(subtitles):
            print(f"正在翻译第 {i+1}/{len(subtitles)} 条字幕...")
            
            translated_text = self.translate_text(subtitle['text'])
            
            translated_subtitles.append({
                'number': subtitle['number'],
                'timecode': subtitle['timecode'],
                'text': translated_text
            })
        
        return translated_subtitles
    
    def write_srt_file(self, subtitles: List[Dict], output_path: str):
        """写入SRT文件"""
        with open(output_path, 'w', encoding='utf-8') as f:
            for subtitle in subtitles:
                f.write(f"{subtitle['number']}\n")
                f.write(f"{subtitle['timecode']}\n")
                f.write(f"{subtitle['text']}\n\n")
    
    def translate_srt_file(self, input_path: str, output_path: str):
        """翻译单个SRT文件"""
        print(f"开始处理文件: {input_path}")
        
        subtitles = self.parse_srt_file(input_path)
        print(f"解析到 {len(subtitles)} 条字幕")
        
        translated_subtitles = self.translate_subtitles(subtitles)
        
        self.write_srt_file(translated_subtitles, output_path)
        print(f"翻译完成，输出文件: {output_path}")
    
    def translate_all_srt_files(self, directory: str = "."):
        """翻译目录中所有的英文SRT文件"""
        srt_files = []
        for filename in os.listdir(directory):
            if filename.endswith('.en.srt'):
                srt_files.append(filename)
        
        if not srt_files:
            print("未找到英文SRT文件（*.en.srt）")
            return
        
        print(f"找到 {len(srt_files)} 个英文SRT文件")
        
        for srt_file in srt_files:
            input_path = os.path.join(directory, srt_file)
            output_filename = srt_file.replace('.en.srt', '.zh.srt')
            output_path = os.path.join(directory, output_filename)
            
            try:
                self.translate_srt_file(input_path, output_path)
                print(f"✓ 成功翻译: {srt_file} -> {output_filename}")
            except Exception as e:
                print(f"✗ 翻译失败: {srt_file}, 错误: {e}")
            
            print("-" * 50)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SRT字幕文件英译中翻译工具（最终版本）')
    parser.add_argument('--input', '-i', help='输入SRT文件路径')
    parser.add_argument('--output', '-o', help='输出SRT文件路径')
    parser.add_argument('--directory', '-d', default='.', help='处理目录，默认为当前目录')
    parser.add_argument('--all', '-a', action='store_true', help='翻译目录中所有英文SRT文件')
    
    args = parser.parse_args()
    
    translator = FinalSRTTranslator()
    
    if args.all or (not args.input and not args.output):
        translator.translate_all_srt_files(args.directory)
    elif args.input and args.output:
        translator.translate_srt_file(args.input, args.output)
    else:
        print("请指定输入和输出文件，或使用 --all 参数翻译所有文件")

if __name__ == "__main__":
    main()
