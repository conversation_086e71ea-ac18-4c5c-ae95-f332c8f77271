1
00:00:00,000 --> 00:00:03,140
你好 , 芭芭 奥克利 , back again 这里 .

2
00:00:03,140 --> 00:00:05,400
在 这个 视频 , 我们 ' re going 到

3
00:00:05,400 --> 00:00:08,700
peek under 这个 神经的 hood , 所以 到 speak ,

4
00:00:08,700 --> 00:00:11,260
到 帮助 你 理解 如何 到

5
00:00:11,260 --> 00:00:14,940
transform 你的 生成式 AI skills .

6
00:00:14,940 --> 00:00:17,440
as 我们 ' re doing 这个 , 我们 应该

7
00:00:17,440 --> 00:00:20,010
keep 在 思维 这个 words 的 fei - fei li ,

8
00:00:20,010 --> 00:00:23,020
这个 prominent researcher 在 AI ,

9
00:00:23,020 --> 00:00:26,760
谁 said , " AI won ' t replace humans ,

10
00:00:26,760 --> 00:00:33,180
但是 humans 谁 使用 AI 将 replace humans 那个 don ' t . "

11
00:00:33,180 --> 00:00:36,140
我 ' d like 到 给 你 just 一个 little sense

12
00:00:36,140 --> 00:00:37,930
的 我的 own background .

13
00:00:37,930 --> 00:00:40,735
我 ' m 从 oakland 大学 ,

14
00:00:40,735 --> 00:00:43,255
people often hear oakland ,

15
00:00:43,255 --> 00:00:44,685
和 他们 想 , well ,

16
00:00:44,685 --> 00:00:47,700
她 必须 是 从 那个 city 在 california .

17
00:00:47,700 --> 00:00:51,420
palm trees , warm weather , nice things .

18
00:00:51,420 --> 00:00:56,100
well , 不 , actually , 我 ' m 从 oakland 大学 ,

19
00:00:56,100 --> 00:00:57,530
哪个 是 在 rochester ,

20
00:00:57,530 --> 00:00:59,520
michigan 和 那个 ' s 我 going

21
00:00:59,520 --> 00:01:02,670
到 工作 在 一个 typical february day .

22
00:01:02,670 --> 00:01:05,740
我 created 这个 课程 学习

23
00:01:05,740 --> 00:01:08,565
如何 到 学习 与 terry sejnowski ,

24
00:01:08,565 --> 00:01:12,040
这个 francis crick 教授 在 这个 salk institute ,

25
00:01:12,040 --> 00:01:14,500
哪里 他 是 also 这个 director 的

26
00:01:14,500 --> 00:01:18,525
这个 computational neuroscience laboratory .

27
00:01:18,525 --> 00:01:22,280
这个 是 terry , while 他 ' s

28
00:01:22,280 --> 00:01:26,320
taking 一个 break 从 他的 day job 在 california .

29
00:01:26,320 --> 00:01:30,155
那个 ' s one 的 这个 nice things 关于 online teaching .

30
00:01:30,155 --> 00:01:32,460
不 only 能 students 得到

31
00:01:32,460 --> 00:01:35,260
together 从 非常 不同的 areas ,

32
00:01:35,260 --> 00:01:37,800
但是 professors 能 来 从

33
00:01:37,800 --> 00:01:41,225
非常 不同的 backgrounds as well .

34
00:01:41,225 --> 00:01:44,110
one thing 那个 surprised 我

35
00:01:44,110 --> 00:01:47,850
是 那个 我 是 asked 到 给 一个 talk 在 harvard .

36
00:01:47,850 --> 00:01:53,130
我 是 rather nervous 因为 它 ' s harvard .

37
00:01:53,130 --> 00:01:55,770
到 我的 surprise , 我 walked 进入

38
00:01:55,770 --> 00:01:59,850
这个 room 和 它 是 packed , standing room only .

39
00:01:59,850 --> 00:02:01,410
我 思想 , well ,

40
00:02:01,410 --> 00:02:04,080
为什么 是 那里 所以 许多 people 这里 ?

41
00:02:04,080 --> 00:02:07,250
turns out 我们的 one little 课程

42
00:02:07,250 --> 00:02:09,480
made 为了 next 到 nothing ,

43
00:02:09,480 --> 00:02:11,330
mostly 在 我的 basement ,

44
00:02:11,330 --> 00:02:16,870
有 在 这个 order 的 这个 相同的 number 的 students as 所有 的

45
00:02:16,870 --> 00:02:20,090
harvard ' s massive open online courses

46
00:02:20,090 --> 00:02:23,290
和 online courses put together ,

47
00:02:23,290 --> 00:02:27,930
made 为了 millions 的 dollars 与 hundreds 的 people .

48
00:02:27,930 --> 00:02:30,950
什么 这个 tells 你 是 那里 ' s

49
00:02:30,950 --> 00:02:34,270
一个 lot 的 interest 在 如何 我们 学习 ,

50
00:02:34,270 --> 00:02:37,470
和 particularly , interest related

51
00:02:37,470 --> 00:02:41,350
到 如何 我们的 大脑 actually works 当 我们 ' re 学习 .

52
00:02:41,350 --> 00:02:45,610
我们 ' ve created 许多 courses .

53
00:02:45,610 --> 00:02:49,210
一些 是 非常 好的 courses 在 teaching ,

54
00:02:49,210 --> 00:02:54,010
学习 为了 kids , 和 changing 你的 job 或 career .

55
00:02:54,010 --> 00:02:56,870
他们 所有 grow 从 一个 underpinning

56
00:02:56,870 --> 00:03:00,215
的 knowing 如何 这个 大脑 actually works .

57
00:03:00,215 --> 00:03:04,595
as 它 turns out , 那个 是 非常 很多

58
00:03:04,595 --> 00:03:06,570
related 到 什么 ' s going 在 在

59
00:03:06,570 --> 00:03:09,270
这个 world 的 人工的 智能 .

60
00:03:09,270 --> 00:03:11,730
let ' s 看 如何 这个 works .

61
00:03:11,730 --> 00:03:16,900
first off , 什么 做 ChatGPT 做 ?

62
00:03:16,900 --> 00:03:20,570
许多 的 你 可能 知道 那个 什么 它 ' s doing 是

63
00:03:20,570 --> 00:03:25,010
often predicting 这个 next word 在 一个 sentence .

64
00:03:25,010 --> 00:03:26,810
那个 ' s 如何 它 ' s trained ,

65
00:03:26,810 --> 00:03:29,630
或 在 least one 的 这个 aspects 的

66
00:03:29,630 --> 00:03:34,130
training 的 foundational , large 语言 models .

67
00:03:34,130 --> 00:03:36,530
什么 它 ' s also doing 是

68
00:03:36,530 --> 00:03:40,930
relating words 在 sentences 到 one 另一个 .

69
00:03:40,930 --> 00:03:42,990
如果 你 想 关于 它 ,

70
00:03:42,990 --> 00:03:45,270
和 我 hope 你 能 forgive 我 为了

71
00:03:45,270 --> 00:03:48,590
anthropomorphizing 一个 little bit .

72
00:03:48,590 --> 00:03:53,810
在 其他 words , 思考 的 生成式 AI as human like .

73
00:03:53,810 --> 00:03:56,490
我们 知道 它 ' s also 非常 不同的 .

74
00:03:56,490 --> 00:03:59,930
但是 如果 我们 想 关于 生成式 AI 和

75
00:03:59,930 --> 00:04:04,690
its ability 到 focus 在 sentences , 为了 例子 ,

76
00:04:04,690 --> 00:04:08,530
这个 explains 为什么 一些 years back ,

77
00:04:08,530 --> 00:04:11,030
如果 你 went 到 google 或 one 的

78
00:04:11,030 --> 00:04:13,230
这个 translation devices 或

79
00:04:13,230 --> 00:04:17,150
apps 和 asked 它 到 translate 一个 sentence ,

80
00:04:17,150 --> 00:04:20,330
它 会 做 一个 terrible job .

81
00:04:20,330 --> 00:04:24,530
你 能 barely 理解 这个 translation .

82
00:04:24,530 --> 00:04:29,110
这个 reason 为了 这个 是 因为 这个 算法 ,

83
00:04:29,110 --> 00:04:32,190
这个 smarts 在 那个 time 的

84
00:04:32,190 --> 00:04:37,010
computers 是 这样的 那个 他们 能 only pay attention ,

85
00:04:37,010 --> 00:04:38,530
所以 到 speak , 到

86
00:04:38,530 --> 00:04:42,980
one 小的 aspect 的 什么 是 在 front 的 他们 .

87
00:04:42,980 --> 00:04:47,030
通过 这个 time 这个 计算机 got 到 这个 end 的 这个 sentence ,

88
00:04:47,030 --> 00:04:49,310
它 会 forget 什么

89
00:04:49,310 --> 00:04:51,830
是 在 这个 beginning 的 这个 sentence .

90
00:04:51,830 --> 00:04:56,850
但是 using 一些 innovative 新的 techniques , 现在 ,

91
00:04:56,850 --> 00:05:01,070
computers 能 focus 不 only 在 sentences ,

92
00:05:01,070 --> 00:05:05,870
但是 在 entire essays 和 不 only entire essays ,

93
00:05:05,870 --> 00:05:09,255
但是 actually 在 这个 web itself .

94
00:05:09,255 --> 00:05:12,180
如何 做 它 put 这个 together ?

95
00:05:12,180 --> 00:05:15,145
如何 做 它 connect things ?

96
00:05:15,145 --> 00:05:18,580
well , 那里 是 一个 paper published back 在

97
00:05:18,580 --> 00:05:23,935
2017 called attention 是 所有 你 need .

98
00:05:23,935 --> 00:05:26,260
这个 paper , 当 它 came out ,

99
00:05:26,260 --> 00:05:30,080
well , nobody 真的 思想 它 是 非常 大的 .

100
00:05:30,080 --> 00:05:32,980
在 fact , terry sejnowski 是

101
00:05:32,980 --> 00:05:36,955
这个 president 的 这个 society 神经的 ips ,

102
00:05:36,955 --> 00:05:38,965
哪里 它 是 presented .

103
00:05:38,965 --> 00:05:42,380
这个 presentation 是 pretty 很多 relegated 到

104
00:05:42,380 --> 00:05:47,990
一个 小的 corner 那个 nobody paid 很多 attention 到 .

105
00:05:47,990 --> 00:05:51,990
但是 现在 , people 有 来 到 recognize 那个

106
00:05:51,990 --> 00:05:54,970
这个 one paper actually 有

107
00:05:54,970 --> 00:05:58,870
这个 seed 的 这样的 一个 重要的 新的 概念 ,

108
00:05:58,870 --> 00:06:04,490
那个 这个 paper 有 是 cited over 100 , 000 times ,

109
00:06:04,490 --> 00:06:09,365
和 它 ' s 是 这个 basis 的 things like suno ,

110
00:06:09,365 --> 00:06:15,530
为了 例子 , 哪个 是 一个 music creation app 或 tool .

111
00:06:18,830 --> 00:06:21,430
如何 做 这个 happen ?

112
00:06:21,430 --> 00:06:24,610
as 它 turns out , 这个 paper

113
00:06:24,610 --> 00:06:28,450
developed 这个 想法 的 这个 transformer .

114
00:06:28,450 --> 00:06:33,465
这个 transformer 有 two main components 在 它 .

115
00:06:33,465 --> 00:06:36,900
one 是 这个 encoder .

116
00:06:36,900 --> 00:06:41,210
这个 encoder 是 哪里 我们 拿 在 这个 信息 .

117
00:06:41,210 --> 00:06:43,595
为了 例子 , 一个 提示 .

118
00:06:43,595 --> 00:06:46,440
然后 我们 有 这个 decoder ,

119
00:06:46,440 --> 00:06:50,500
哪个 spits out whatever 你的 output 是 .

120
00:06:50,500 --> 00:06:55,800
这个 encoder decoder 和 its flow 的 信息 ,

121
00:06:55,800 --> 00:06:58,660
as 它 turns out , 是 非常

122
00:06:58,660 --> 00:07:04,380
similar 到 这个 flow 的 信息 在 这个 human 大脑 .

123
00:07:04,380 --> 00:07:08,940
这个 是 为什么 它 behooves 我们 到 学习 一个 little bit

124
00:07:08,940 --> 00:07:11,360
关于 这个 大脑 所以 我们 能 使用 一些

125
00:07:11,360 --> 00:07:14,160
的 这个 insights 的 transformers ,

126
00:07:14,160 --> 00:07:17,040
large 语言 models , 和 things

127
00:07:17,040 --> 00:07:20,560
like ChatGPT 更多 effectively .

128
00:07:20,560 --> 00:07:23,105
想 的 large 语言 models like

129
00:07:23,105 --> 00:07:26,900
ChatGPT as 是 like 不同的 car engines .

130
00:07:26,900 --> 00:07:29,280
once 你 知道 如何 到 drive ,

131
00:07:29,280 --> 00:07:32,300
switching 在...之间 cars 是 一个 breeze ,

132
00:07:32,300 --> 00:07:35,380
even 如果 他们 ' ve got 不同的 engines under 这个 hood ,

133
00:07:35,380 --> 00:07:38,965
它 ' s 这个 相同的 deal 与 生成式 AI platforms like

134
00:07:38,965 --> 00:07:42,920
ChatGPT , gemini , 和 claude .

135
00:07:42,920 --> 00:07:45,020
他们 ' re just slightly 不同的 models ,

136
00:07:45,020 --> 00:07:46,980
powering similar tools .

137
00:07:46,980 --> 00:07:49,740
得到 这个 hang 的 one , 和 你 ' ll 找到

138
00:07:49,740 --> 00:07:52,585
这个 others feel pretty familiar too .

139
00:07:52,585 --> 00:07:54,890
这个 basics stay 这个 相同的 ,

140
00:07:54,890 --> 00:07:58,870
even 如果 这个 details under 这个 hood 是 一个 bit 不同的 .

141
00:07:58,870 --> 00:08:01,610
现在 , let ' s turn 到 朱尔斯 ,

142
00:08:01,610 --> 00:08:04,470
和 他 ' ll 给 你 一个 little 更多 insight

143
00:08:04,470 --> 00:08:08,790
进入 这些 非常 重要的 ideas .

