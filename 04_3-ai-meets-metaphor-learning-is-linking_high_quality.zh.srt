1
00:00:00,400 --> 00:00:01,849
芭芭·奥克利在这里。

2
00:00:01,849 --> 00:00:06,972
在这个视频中，我们将向你介绍
隐喻如何帮助

3
00:00:06,972 --> 00:00:14,150
你的学习，以及生成式AI
如何帮助你使用隐喻。

4
00:00:14,150 --> 00:00:18,870
但让我们先来看看
大脑是如何学习的。

5
00:00:18,870 --> 00:00:19,918
从根本上说。

6
00:00:19,918 --> 00:00:23,972
大脑当然是
非常复杂的，但是

7
00:00:23,972 --> 00:00:28,317
我们可以通过专注于

8
00:00:28,317 --> 00:00:32,880
被称为神经元的基本构建块
来简化我们对它的理解。

9
00:00:32,880 --> 00:00:37,360
大脑中大约有
860亿个神经元。

10
00:00:37,360 --> 00:00:42,075
本质上，
神经元有一个伸出的臂膀，并且

11
00:00:42,075 --> 00:00:46,200
它们在下面有这些腿。

12
00:00:46,200 --> 00:00:53,010
它们正在做的是伸出并
向彼此发送信号。

13
00:00:53,010 --> 00:00:59,970
这些信号跳过神经元之间的
小间隙，将它们连接在一起。

14
00:00:59,970 --> 00:01:03,562
这就是当你学习
某些东西时发生的事情。

15
00:01:03,562 --> 00:01:10,690
你正在大脑的长期记忆中
在神经元之间建立连接。

16
00:01:10,690 --> 00:01:17,131
无论你学习什么都是如此，
无论是如何跳舞步，

17
00:01:17,131 --> 00:01:22,012
一些与生成式AI相关的新想法，
比如ChatGPT，或者

18
00:01:22,012 --> 00:01:27,820
数学问题，或者如何在
外语中变位动词。

19
00:01:27,820 --> 00:01:34,900
无论你学习什么，你只是在
神经元之间建立新的连接。

20
00:01:34,900 --> 00:01:40,300
当你学习时，你实际上
在构建这些连接。

21
00:01:40,300 --> 00:01:43,089
你练习某些东西越多，

22
00:01:43,089 --> 00:01:47,240
神经元之间的这些链接
就能变得越强。

23
00:01:48,300 --> 00:01:54,747
嗯，当然，除非你忘记了或者
没有时间练习或者

24
00:01:54,747 --> 00:01:59,183
反复学习你正在
学习的材料，

25
00:01:59,183 --> 00:02:06,820
那么你的小突触清洁工可以
清除你建立的那些连接。

26
00:02:06,820 --> 00:02:12,759
这就是为什么，如果你不重新学习
材料并且学得很好，

27
00:02:12,759 --> 00:02:19,280
这些连接可能被清除，
就好像你根本没有学过一样。

28
00:02:19,280 --> 00:02:23,240
但假设你确实
练习了这些材料。

29
00:02:23,240 --> 00:02:29,440
当你练习时，这些链接集合
变得更强更丰富。

30
00:02:29,440 --> 00:02:36,240
你真正学好了材料，
可以更容易地回想起来。

31
00:02:36,240 --> 00:02:42,100
这些链接就像项链中的链接，
它们强壮而丰富。

32
00:02:43,280 --> 00:02:46,390
这里有一个有趣的观点。

33
00:02:46,390 --> 00:02:51,373
当你学习某些东西时，
你创建了链接集合，

34
00:02:51,373 --> 00:02:56,910
或者与你一直在学习的
任何东西相关的许多链接集合。

35
00:02:56,910 --> 00:03:01,865
但一旦你学会了那个概念，
你可以将它用作

36
00:03:01,865 --> 00:03:07,990
一个隐喻来帮助桥接到
你正在学习的新事物。

37
00:03:07,990 --> 00:03:16,090
一个简单的例子是理解
水流是如何流动的。

38
00:03:16,090 --> 00:03:18,786
现在对你来说似乎很简单，但是

39
00:03:18,786 --> 00:03:24,074
你在婴儿和幼儿时期
玩过水是如何流动的，

40
00:03:24,074 --> 00:03:29,570
你花了几年时间才
真正理解那个概念。

41
00:03:29,570 --> 00:03:35,412
但一旦你有了那个想法，
关于水是如何流动的，

42
00:03:35,412 --> 00:03:40,898
知道那个概念可以作为
一个简单的心理桥梁

43
00:03:40,898 --> 00:03:47,780
来帮助你掌握电流
如何流动的新想法。

44
00:03:47,780 --> 00:03:52,384
隐喻在学习中
非常有价值。

45
00:03:52,384 --> 00:03:55,740
为什么隐喻有价值？

46
00:03:55,740 --> 00:04:01,260
嗯，我在写一本叫做
《数字思维》的书。

47
00:04:01,260 --> 00:04:06,488
当我在为这本书写手稿时，
我发出了数千封

48
00:04:06,488 --> 00:04:12,348
电子邮件给那些以优质教学
而闻名的顶级教授，

49
00:04:12,348 --> 00:04:18,600
我问他们是否可以
看看这本书的手稿。

50
00:04:18,600 --> 00:04:24,080
令人震惊的是，他们中的很大比例说，
当然，我很乐意。

51
00:04:24,080 --> 00:04:30,320
当他们看了之后，他们给了我很多
关于我们如何学习的见解。

52
00:04:30,320 --> 00:04:35,344
我们如何拥有数字思维或
语言思维或

53
00:04:35,344 --> 00:04:41,220
学习任何东西的思维，
就此而言？

54
00:04:41,220 --> 00:04:45,420
不过，他们告诉我的一件事
真的很令人惊讶。

55
00:04:45,420 --> 00:04:50,571
他们说，我通常不会
与其他教授或

56
00:04:50,571 --> 00:04:58,940
老师谈论我如何教学，因为我使用
一种他们经常不喜欢的方法。

57
00:04:58,940 --> 00:05:02,020
他们使用的方法是什么？

58
00:05:02,020 --> 00:05:07,470
那就是使用隐喻和类比。

59
00:05:07,470 --> 00:05:11,025
他们不喜欢告诉
其他教授或

60
00:05:11,025 --> 00:05:16,735
老师他们的方法，
因为其他人经常会说，

61
00:05:16,735 --> 00:05:20,790
所以这就是为什么你作为
老师如此受欢迎。

62
00:05:20,790 --> 00:05:24,570
你只是让事情对你的
学生来说如此容易。

63
00:05:25,910 --> 00:05:28,982
但这不是老师和

64
00:05:28,982 --> 00:05:33,260
教授的工作吗，让新的
学习变得更容易？

65
00:05:33,260 --> 00:05:40,060
因为学习真的很难，
正如你从这篇论文中可以看到的。

66
00:05:40,060 --> 00:05:44,865
但我们可以通过使用隐喻
让自己更容易，

67
00:05:44,865 --> 00:05:49,940
就像这些杰出的
教学教授所做的那样。

68
00:05:49,940 --> 00:05:55,979
现在，隐喻是帮助你学习
一些新想法的好方法，

69
00:05:55,979 --> 00:05:59,810
但隐喻总是会失效。

70
00:05:59,810 --> 00:06:05,001
例如，
当涉及到电流流动时，

71
00:06:05,001 --> 00:06:11,650
我们知道在量子层面上，
水不是一个很好的隐喻。

72
00:06:11,650 --> 00:06:19,690
但当隐喻失效时，我们可以
直接丢弃它并获得一个新的隐喻。

73
00:06:19,690 --> 00:06:25,090
诀窍是，
我们如何想出隐喻？

74
00:06:25,090 --> 00:06:30,590
过去找到好的隐喻
真的很困难，

75
00:06:30,590 --> 00:06:34,090
除了现在一点也不难。

76
00:06:34,090 --> 00:06:40,170
现在不难了，因为
我们可以使用生成式AI。

77
00:06:40,170 --> 00:06:42,850
让我向你展示我的意思。

78
00:06:42,850 --> 00:06:48,581
假设我们正在学习
Python编程中的

79
00:06:48,581 --> 00:06:52,770
main guard和scope。

80
00:06:52,770 --> 00:06:55,746
我们可以问ChatGPT、Claude或

81
00:06:55,746 --> 00:07:00,570
任何其他主要的
基础模型来解释，

82
00:07:00,570 --> 00:07:04,879
使用隐喻，
这两个概念是什么以及

83
00:07:04,879 --> 00:07:09,950
这两个概念之间的
区别是什么。

84
00:07:09,950 --> 00:07:14,541
令人惊讶的是，这些
生成式AI模型可以

85
00:07:14,541 --> 00:07:20,110
想出精彩而
美丽的隐喻。

86
00:07:20,110 --> 00:07:28,129
例如，你可以将main guard
想象成城堡的守门人，

87
00:07:28,129 --> 00:07:34,110
而scope就像那个
城堡内的各个房间。

88
00:07:34,110 --> 00:07:38,030
然后它给你更多的信息。

89
00:07:38,030 --> 00:07:45,651
使用隐喻是帮助你掌握
新的和困难想法的好方法。

90
00:07:45,651 --> 00:07:50,685
当你试图学习新东西时，
问ChatGPT或

91
00:07:50,685 --> 00:07:55,819
任何生成式AI模型，
什么是好的隐喻

92
00:07:55,819 --> 00:08:01,476
对于这个，你不必接受
它给你的第一个选项。

93
00:08:01,476 --> 00:08:04,328
尝试四个其他不同的隐喻，

94
00:08:04,328 --> 00:08:09,120
你会惊讶于其他那些
可以有多么不同。

95
00:08:09,120 --> 00:08:14,120
朱尔斯将给你一些
沿着这些思路的更多想法。

96
00:08:14,120 --> 00:08:20,796
我喜欢将隐喻想象成
有点像帐篷。

97
00:08:20,796 --> 00:08:27,010
假设你正在搭帐篷，
你有两根杆子。

98
00:08:27,010 --> 00:08:33,539
嗯，帐篷在那些杆子
升起的地方接触布料。

99
00:08:33,539 --> 00:08:40,019
你可以认为隐喻
非常强烈地连接或

100
00:08:40,019 --> 00:08:48,350
与帐篷杆接触布料的地方的
基础想法相关。

101
00:08:48,350 --> 00:08:55,854
在这里你可以看到隐喻可以与
另一个概念有两个共同点。

102
00:08:55,854 --> 00:09:03,830
它可以有三个共同点，
或者可以有更多的共同点。

103
00:09:03,830 --> 00:09:09,170
当我们看这些隐喻时，
我们可以有很好的匹配地方，

104
00:09:09,170 --> 00:09:13,785
糟糕的匹配地方，以及
更多的地方或更多的隐喻，

105
00:09:13,785 --> 00:09:18,850
不同的隐喻有
好的匹配和坏的匹配。

106
00:09:18,850 --> 00:09:23,403
你可以要求有许多
共同点的隐喻，或者

107
00:09:23,403 --> 00:09:28,402
你可以问什么是好的隐喻
来描述，例如，

108
00:09:28,402 --> 00:09:31,330
一个分子以及它如何运作？

109
00:09:31,330 --> 00:09:37,970
任何困惑你的东西，那就是
你想要为之寻求隐喻的。

110
00:09:37,970 --> 00:09:40,938
你可以问ChatGPT、Claude或

111
00:09:40,938 --> 00:09:46,636
任何好的生成式AI
平台来获得好的隐喻。

112
00:09:46,636 --> 00:09:50,340
当然，适当的提示也有帮助。

113
00:09:50,340 --> 00:09:54,420
为了在这里获得更多帮助，
让我们转向朱尔斯。
