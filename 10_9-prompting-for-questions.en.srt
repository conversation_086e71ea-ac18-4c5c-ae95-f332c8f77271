1
00:00:00,680 --> 00:00:05,180
When you're learning new material, like
<PERSON><PERSON> said, it's really important to have

2
00:00:05,180 --> 00:00:08,930
different problems, to work on
different questions that you can go and

3
00:00:08,930 --> 00:00:13,240
try to answer to understand the material
at a deeper level, to practice, to think

4
00:00:13,240 --> 00:00:17,814
through different important issues and
concepts and things that you need to know.

5
00:00:17,814 --> 00:00:21,806
But often those are, you know,
hidden behind textbooks and answer keys.

6
00:00:21,806 --> 00:00:23,278
And how do we get to those things?

7
00:00:23,278 --> 00:00:27,683
And when you're learning a new concept
of or some idea or you're listening to

8
00:00:27,683 --> 00:00:32,444
a presentation for the first time, it can
be hard to formulate those questions.

9
00:00:32,444 --> 00:00:34,140
And so how do we go and fix that?

10
00:00:34,140 --> 00:00:35,351
With generative AI,

11
00:00:35,351 --> 00:00:39,062
one of the things we can use is we
can use it to generate questions.

12
00:00:39,062 --> 00:00:41,099
And this is such a simple concept, but

13
00:00:41,099 --> 00:00:44,660
one that gets missed when we're
running off to get answers.

14
00:00:44,660 --> 00:00:46,948
So let's take a look
at what it looks like.

15
00:00:46,948 --> 00:00:50,563
But I want you to think deeply about how
you get it to generate questions, and

16
00:00:50,563 --> 00:00:51,974
I don't want you to just go and

17
00:00:51,974 --> 00:00:54,708
ask it to generate questions
in a prompt that this big.

18
00:00:54,708 --> 00:00:57,860
I want you to think about
giving it rich source material.

19
00:00:57,860 --> 00:01:02,524
So one of the things you can do in
most of the tools is you can go and

20
00:01:02,524 --> 00:01:04,812
attach files to the prompt.

21
00:01:04,812 --> 00:01:09,376
And so if you go into ChatGPT, you'll
probably see something like a plus or

22
00:01:09,376 --> 00:01:10,250
a paperclip.

23
00:01:10,250 --> 00:01:13,044
If you go into Claude, you'll
probably see something very similar.

24
00:01:13,044 --> 00:01:15,079
And that's your ability to go and
upload and

25
00:01:15,079 --> 00:01:17,756
give it additional information
to go with the prompt.

26
00:01:17,756 --> 00:01:21,464
When you do this, your prompt becomes not
only that file, but also the question or

27
00:01:21,464 --> 00:01:23,686
instructions that you're giving the model.

28
00:01:23,686 --> 00:01:25,854
And this is really, really helpful.

29
00:01:25,854 --> 00:01:30,208
So what I've done here is I've taken
one of my PowerPoint presentations.

30
00:01:30,208 --> 00:01:34,883
You can imagine this is the slides for
a training or a course that you're taking,

31
00:01:34,883 --> 00:01:38,078
and you want to go and
understand it at a deeper level.

32
00:01:38,078 --> 00:01:41,435
Or maybe it's a new proposal that you
received from an outside vendor who's

33
00:01:41,435 --> 00:01:42,956
going to do some services for you,

34
00:01:42,956 --> 00:01:45,454
but it's in an area that
you don't know a lot about.

35
00:01:45,454 --> 00:01:49,173
And so the starting point of helping
ourselves to understand the material,

36
00:01:49,173 --> 00:01:50,509
to practice the material,

37
00:01:50,509 --> 00:01:53,488
is to be able to generate
questions related to the material.

38
00:01:53,488 --> 00:01:58,658
So I've gone and uploaded my presentation,
it's about 100 PowerPoint slides.

39
00:01:58,658 --> 00:02:01,418
And I say,
help me understand this material better.

40
00:02:01,418 --> 00:02:06,010
What are five questions I should ask
about it to deepen my understanding?

41
00:02:06,010 --> 00:02:09,554
And this is my own presentation, so
I think it's quite fun to do this.

42
00:02:09,554 --> 00:02:11,066
And it comes back and it says,

43
00:02:11,066 --> 00:02:14,584
what are the key advantages of using
prop patterns in generative AI?

44
00:02:14,584 --> 00:02:18,614
And how do they improve the quality of
outputs compared to unstructured prompts?

45
00:02:18,614 --> 00:02:21,358
Now, if you've heard me talking
about prompt patterns already,

46
00:02:21,358 --> 00:02:24,158
you know this is my presentation,
and that's a good question.

47
00:02:24,158 --> 00:02:27,449
And being able to answer that
question can be very helpful.

48
00:02:27,449 --> 00:02:31,330
How does the concept of augmented
intelligence differ from traditional

49
00:02:31,330 --> 00:02:32,966
artificial intelligence?

50
00:02:32,966 --> 00:02:36,701
And augmented intelligence is a concept
that I talk about in this presentation.

51
00:02:36,701 --> 00:02:39,995
Not of using artificial
intelligence to replace people, but

52
00:02:39,995 --> 00:02:43,734
people working together with
artificial intelligence to augment and

53
00:02:43,734 --> 00:02:47,421
amplify their own critical thinking and
reasoning and creativity.

54
00:02:47,421 --> 00:02:50,907
Of ways of using it to enhance
what they do, but not replace it.

55
00:02:50,907 --> 00:02:52,658
And it goes on with a number of questions.

56
00:02:52,658 --> 00:02:55,496
And all of these questions are questions
that if somebody can answer

57
00:02:55,496 --> 00:02:58,783
these questions about my presentation and
about the material covered there,

58
00:02:58,783 --> 00:03:00,482
it will deepen their understanding.

59
00:03:00,482 --> 00:03:03,189
They will know it more if they
think about these questions and

60
00:03:03,189 --> 00:03:04,994
they think about the answers to them.

61
00:03:04,994 --> 00:03:06,940
It's very helpful.

62
00:03:06,940 --> 00:03:10,872
And so this simple idea of generating
questions based on material that we're

63
00:03:10,872 --> 00:03:13,471
learning about, but
not just going in and saying,

64
00:03:13,471 --> 00:03:16,876
hallucinate a bunch of questions
that might possibly be related.

65
00:03:16,876 --> 00:03:21,668
But actually taking the materials that we
are working with, the actual slides for

66
00:03:21,668 --> 00:03:26,391
the class, the actual syllabus,
readings, proposals, presentations, and

67
00:03:26,391 --> 00:03:29,109
building questions from
them is the way to go.

68
00:03:29,109 --> 00:03:33,205
Because it helps reduce this hallucination
that we're talking about, but

69
00:03:33,205 --> 00:03:36,853
also helps us to really target
the information that it's analyzing

70
00:03:36,853 --> 00:03:38,783
in order to produce the questions.

71
00:03:38,783 --> 00:03:40,748
Let's go and look at another example.

72
00:03:40,748 --> 00:03:45,432
It's not just questions about
the materials that we're learning in

73
00:03:45,432 --> 00:03:46,876
a class or a course.

74
00:03:46,876 --> 00:03:50,509
It's not just a written proposal, but
often what we have is teams of people that

75
00:03:50,509 --> 00:03:54,469
are working through ideas together, that
are trying to solve some problem together,

76
00:03:54,469 --> 00:03:56,820
and they're learning in the process.

77
00:03:56,820 --> 00:04:01,154
And so one of the things we can do is we
use all of these online meeting tools

78
00:04:01,154 --> 00:04:03,072
like Zoom and Microsoft Teams,

79
00:04:03,072 --> 00:04:07,390
and we can record the meetings in
there and get transcripts out.

80
00:04:07,390 --> 00:04:15,070
So I've taken a transcript that I had from
a discussion with a close collaborator.

81
00:04:15,070 --> 00:04:16,654
It's a company that we're
collaborating with.

82
00:04:16,654 --> 00:04:20,169
I've actually done some courses
with the CEO of this company, and

83
00:04:20,169 --> 00:04:23,303
I took the transcript from
the Zoom call we had, and I said,

84
00:04:23,303 --> 00:04:27,257
without listing any names, what are five
questions our team should ask and

85
00:04:27,257 --> 00:04:29,482
discuss to help think through our plans?

86
00:04:29,482 --> 00:04:32,611
And it comes back and it says, what
are the core objectives and priorities for

87
00:04:32,611 --> 00:04:33,536
this collaboration?

88
00:04:33,536 --> 00:04:37,360
How can we effectively engage and
align all stakeholders involved?

89
00:04:37,360 --> 00:04:42,152
And it's giving me things that we
should learn about and think about.

90
00:04:42,152 --> 00:04:45,228
It goes on, and it says, what
are the most significant challenges or

91
00:04:45,228 --> 00:04:49,320
opportunities within our current
processes that innovation could address?

92
00:04:49,320 --> 00:04:52,813
How should we brand and
communicate what metrics, you know, or

93
00:04:52,813 --> 00:04:56,490
key performance indicators will
be used to measure success?

94
00:04:56,490 --> 00:05:00,195
All of these things are important
questions that we should think about

95
00:05:00,195 --> 00:05:04,023
as a team and answer, because we're
actually going on a journey to learn

96
00:05:04,023 --> 00:05:07,474
together about how to solve this problem,
how to work with them,

97
00:05:07,474 --> 00:05:11,154
to incorporate generative AI in
the space that they're working.

98
00:05:11,154 --> 00:05:12,958
And there's a lot of complexity there.

99
00:05:12,958 --> 00:05:17,034
So this is actually bringing questions in
that the team as a whole can discuss to

100
00:05:17,034 --> 00:05:21,438
help learn together about how we're going
to approach this problem and solve it.

101
00:05:21,438 --> 00:05:25,446
So we don't have to just think about
questions for the materials, the written

102
00:05:25,446 --> 00:05:29,576
materials, but we can also think about
questions that teams as a whole should ask

103
00:05:29,576 --> 00:05:34,096
about how they're coordinating, planning
and communicating and working together.

104
00:05:34,096 --> 00:05:37,919
Or another example is we
just want more practice,

105
00:05:37,919 --> 00:05:40,678
we want to go and practice things.

106
00:05:40,678 --> 00:05:45,286
We've seen the importance of practice, not
giving up and giving over everything, but

107
00:05:45,286 --> 00:05:47,174
we really need to be practicing.

108
00:05:47,174 --> 00:05:51,550
How do we get more problems
to directly practice with?

109
00:05:51,550 --> 00:05:55,250
And so we can take something like the
slides from a course, or slides in this

110
00:05:55,250 --> 00:05:59,008
case from a talk that I'm giving and
say, what are five questions that, if I

111
00:05:59,008 --> 00:06:03,390
can answer them, would show that I have
a broad understanding of this material.

112
00:06:03,390 --> 00:06:05,291
And so when we create this,

113
00:06:05,291 --> 00:06:10,926
we're creating a set of questions that I
can then practice with and think about.

114
00:06:10,926 --> 00:06:18,260
And go and begin answering to practice,
because it's not just about thinking.

115
00:06:18,260 --> 00:06:22,669
We can use questions to help us think
more deeply and learn by going and

116
00:06:22,669 --> 00:06:26,532
directing how we go and
think about a particular problem.

117
00:06:26,532 --> 00:06:30,518
We can use it in sort of questions to
help a group go and learn together and

118
00:06:30,518 --> 00:06:34,700
dissect a problem and think about what
might be important to talk about.

119
00:06:34,700 --> 00:06:38,720
But we can also go and use it for
actual direct practice and get quizzes,

120
00:06:38,720 --> 00:06:42,434
get problems that we can try out,
all kinds of things like that.

121
00:06:42,434 --> 00:06:43,992
And we see some great examples here.

122
00:06:43,992 --> 00:06:47,127
What are the two key revolutions in
computing driven by generative AI?

123
00:06:47,127 --> 00:06:51,010
And how do they differ from previous
computational interfaces and capabilities?

124
00:06:51,010 --> 00:06:54,306
If you were in a class
listening to me lecture,

125
00:06:54,306 --> 00:06:58,037
that would be a fantastic
question to go into a quiz.

126
00:06:58,037 --> 00:07:01,541
But that's exactly the type of
thing that you can directly use and

127
00:07:01,541 --> 00:07:05,183
equip yourself with is take any
material and turn it into practice.

128
00:07:05,183 --> 00:07:09,739
Rather than taking material and turn
it into answers, take the material and

129
00:07:09,739 --> 00:07:14,365
use it for practice to generate as many
different questions as you want to base

130
00:07:14,365 --> 00:07:16,874
those questions in particular domains.

131
00:07:16,874 --> 00:07:22,213
You can stay focused on these aspects
of the presentation, generate questions

132
00:07:22,213 --> 00:07:27,186
related to these topics, or test me and
tell me what topics I'm weak on.

133
00:07:27,186 --> 00:07:31,471
Give me a bunch of questions, tell me how
I did all kinds of fascinating things that

134
00:07:31,471 --> 00:07:35,011
we can use to enhance our own practice,
and generating questions or

135
00:07:35,011 --> 00:07:37,890
problems is one of the most
basic things that we can do.