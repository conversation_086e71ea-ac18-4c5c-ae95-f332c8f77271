# SRT字幕翻译工具

这是一个专门用于将英文SRT字幕文件翻译成中文的工具，能够完整保持原有的时间码格式和字幕结构。

## 功能特点

- ✅ **完整翻译**: 翻译所有字幕条目，不遗漏任何内容
- ✅ **准确翻译**: 使用Google翻译API确保翻译质量
- ✅ **格式保持**: 完全保持SRT文件的原有格式
  - 字幕序号保持不变
  - 时间码格式完全一致 (HH:MM:SS,mmm --> HH:MM:SS,mmm)
  - 字幕块之间的空行保持
- ✅ **批量处理**: 一次性处理目录中所有英文SRT文件
- ✅ **智能命名**: 自动生成中文文件名 (*.en.srt → *.zh.srt)

## 文件说明

- `translate_srt.py` - 在线翻译版本（使用Google翻译API）
- `translate_srt_offline.py` - 离线翻译版本（使用googletrans库）
- `setup_and_translate.bat` - Windows一键安装和运行脚本
- `setup_and_translate.sh` - Linux/Mac一键安装和运行脚本

## 快速开始

### Windows用户

1. 双击运行 `setup_and_translate.bat`
2. 脚本会自动安装依赖并开始翻译
3. 翻译完成后，中文字幕文件会保存为 `*.zh.srt` 格式

### Linux/Mac用户

1. 给脚本添加执行权限：
   ```bash
   chmod +x setup_and_translate.sh
   ```

2. 运行脚本：
   ```bash
   ./setup_and_translate.sh
   ```

### 手动安装和运行

1. 安装依赖：
   ```bash
   pip install googletrans==4.0.0rc1 requests
   ```

2. 翻译所有文件：
   ```bash
   python translate_srt_offline.py --all
   ```

3. 翻译单个文件：
   ```bash
   python translate_srt_offline.py -i input.en.srt -o output.zh.srt
   ```

## 使用方法

### 命令行参数

```bash
python translate_srt_offline.py [选项]

选项:
  -h, --help            显示帮助信息
  -i INPUT, --input INPUT
                        输入SRT文件路径
  -o OUTPUT, --output OUTPUT
                        输出SRT文件路径
  -d DIRECTORY, --directory DIRECTORY
                        处理目录，默认为当前目录
  -a, --all             翻译目录中所有英文SRT文件
```

### 使用示例

1. **翻译所有英文SRT文件**（推荐）：
   ```bash
   python translate_srt_offline.py --all
   ```

2. **翻译指定文件**：
   ```bash
   python translate_srt_offline.py -i "01_0-introduction.en.srt" -o "01_0-introduction.zh.srt"
   ```

3. **翻译指定目录中的所有文件**：
   ```bash
   python translate_srt_offline.py -d "/path/to/subtitles" --all
   ```

## 输入输出格式

### 输入格式
工具会自动识别以 `.en.srt` 结尾的英文字幕文件。

### 输出格式
- 中文字幕文件以 `.zh.srt` 结尾
- 完全保持原有的SRT格式结构
- 示例：
  ```
  1
  00:00:00,000 --> 00:00:05,246
  [音乐]

  2
  00:00:05,246 --> 00:00:06,587
  欢迎来到我们的课程。
  ```

## 特殊处理

- `[MUSIC]`、`[APPLAUSE]`、`[LAUGHTER]` 等标记会被保留或适当翻译
- 空字幕条目会被保持
- 多行字幕内容会被正确处理

## 注意事项

1. **网络连接**: 翻译过程需要网络连接访问Google翻译服务
2. **翻译速度**: 为避免API限制，每条字幕之间有0.5秒延迟
3. **文件编码**: 支持UTF-8、GBK、Latin-1等多种编码格式
4. **错误处理**: 翻译失败的条目会保持原文并显示错误信息

## 故障排除

### 常见问题

1. **"googletrans库未安装"错误**：
   ```bash
   pip install googletrans==4.0.0rc1
   ```

2. **网络连接错误**：
   - 检查网络连接
   - 尝试使用VPN
   - 等待一段时间后重试

3. **编码错误**：
   - 工具会自动尝试多种编码格式
   - 如果仍有问题，请检查原文件编码

4. **翻译质量问题**：
   - 可以手动编辑生成的中文字幕文件
   - 建议对专业术语进行人工校对

## 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.6+
2. 依赖库是否正确安装
3. 网络连接是否正常
4. 输入文件格式是否正确

## 许可证

本工具仅供学习和个人使用，请遵守相关法律法规和服务条款。
