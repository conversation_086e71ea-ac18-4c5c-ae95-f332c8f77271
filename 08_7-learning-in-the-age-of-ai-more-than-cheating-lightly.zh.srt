1
00:00:00,000 --> 00:00:02,500
barbocle , back again 这里 .

2
00:00:02,500 --> 00:00:07,520
我 有 mentioned 那个 生成式 AI 有 更多 problems ,

3
00:00:07,520 --> 00:00:10,480
的 课程 , than just hallucination .

4
00:00:10,480 --> 00:00:12,920
one 的 这个 biggest problems

5
00:00:12,920 --> 00:00:15,840
从 我们的 perspective as professors ,

6
00:00:15,840 --> 00:00:19,660
是 那个 它 makes 它 真的 容易的 到 cheat .

7
00:00:19,660 --> 00:00:22,340
这个 是 indeed 一个 问题 .

8
00:00:22,340 --> 00:00:26,520
但是 我 应该 说 那个 years back , let ' s 说 ,

9
00:00:26,520 --> 00:00:30,000
当 people 是 first starting 到 学习 到 write ,

10
00:00:30,000 --> 00:00:35,765
他们 是 imprinting 在 clay tablets 与 cuneiform .

11
00:00:35,765 --> 00:00:38,020
当 他们 是 doing 那个 , people

12
00:00:38,020 --> 00:00:41,185
是 complaining 因为 他们 said ,

13
00:00:41,185 --> 00:00:44,960
" 你 , hey , 你 ' re going 到 lose 你的 记忆 .

14
00:00:44,960 --> 00:00:46,620
你 ' re 不 going 到 remember

15
00:00:46,620 --> 00:00:49,090
things 如果 你 write things down . "

16
00:00:49,090 --> 00:00:53,730
那里 是 一个 lot 的 well placed criticisms 的 学习 到

17
00:00:53,730 --> 00:00:55,630
write 因为 people wouldn ' t

18
00:00:55,630 --> 00:00:58,610
rely 所以 很多 在 他们的 记忆 .

19
00:00:58,610 --> 00:01:01,130
它 ' s rather similar 现在 .

20
00:01:01,130 --> 00:01:06,810
people 知道 那个 那里 是 坏的 aspects 的 生成式 AI .

21
00:01:06,810 --> 00:01:10,850
但是 这个 trade off 是 这样的 那个 它 能 是 worth 它 .

22
00:01:10,850 --> 00:01:15,210
但是 为了 我们 as professors 和 teachers ,

23
00:01:15,210 --> 00:01:17,510
生成式 AI 有 真的

24
00:01:17,510 --> 00:01:20,650
pulled 这个 rug out 从 under 我们 .

25
00:01:20,650 --> 00:01:24,030
它 有 changed 许多 的 这个 ways 那个 我们

26
00:01:24,030 --> 00:01:27,250
有 used 在 这个 past 到 帮助 students 学习 ,

27
00:01:27,250 --> 00:01:30,630
为了 例子 , writing 一个 essay 或

28
00:01:30,630 --> 00:01:35,120
关于 如何 到 工作 out 他们的 homework problems .

29
00:01:35,120 --> 00:01:36,950
为了 所有 的 这些 things ,

30
00:01:36,950 --> 00:01:40,950
learners went off 和 他们 有 到 做 他们 在 他们的 own .

31
00:01:40,950 --> 00:01:43,090
你 don ' t need 到 做 那个

32
00:01:43,090 --> 00:01:47,105
necessarily 现在 与 生成式 AI .

33
00:01:47,105 --> 00:01:52,220
它 means 那个 不 only 是 它 easier 到 cheat ,

34
00:01:52,220 --> 00:01:56,700
但是 你 能 also end 上 学习 更多 lightly .

35
00:01:56,700 --> 00:01:59,490
为什么 学习 到 write 一个 essay , 为了 例子 ,

36
00:01:59,490 --> 00:02:01,360
如果 你 能 just 去 到

37
00:02:01,360 --> 00:02:05,300
生成式 AI 和 ask 它 到 write essays 为了 你 ?

38
00:02:05,300 --> 00:02:08,840
well , 那个 是 一个 问题 .

39
00:02:08,840 --> 00:02:10,540
as 我们 ' re going along 在

40
00:02:10,540 --> 00:02:13,500
这个 next decade 和 这个 decades 到 来 ,

41
00:02:13,500 --> 00:02:15,880
people 将 是 figuring out

42
00:02:15,880 --> 00:02:19,675
这个 solutions 到 一些 的 这些 challenges .

43
00:02:19,675 --> 00:02:22,720
但是 那里 ' s 另一个 aspect

44
00:02:22,720 --> 00:02:25,260
或 另一个 方式 的 looking 在 这个 那个 能

45
00:02:25,260 --> 00:02:28,020
帮助 你 理解 为什么 它 ' s

46
00:02:28,020 --> 00:02:30,920
still 重要的 到 学习 一些 的 这些 ideas ,

47
00:02:30,920 --> 00:02:33,840
whatever 你 ' re studying , 到 学习

48
00:02:33,840 --> 00:02:37,820
这个 rigor 的 mathematical 思考 , 为了 例子 ,

49
00:02:37,820 --> 00:02:40,960
和 到 become 非常 familiar 与 一些 的

50
00:02:40,960 --> 00:02:43,160
这个 key aspects 的 programming

51
00:02:43,160 --> 00:02:46,645
或 到 学习 到 speak 另一个 语言 .

52
00:02:46,645 --> 00:02:49,830
什么 我 ' m 真的 driving 在 是 related

53
00:02:49,830 --> 00:02:53,030
到 something called 这个 flynn effect .

54
00:02:53,030 --> 00:02:54,890
在 这个 flynn effect ,

55
00:02:54,890 --> 00:03:00,710
researchers found 那个 从 这个 1930s 到 这个 1970s ,

56
00:03:00,710 --> 00:03:05,030
iq scores rose dramatically .

57
00:03:05,030 --> 00:03:09,490
well , 为什么 是 那个 ? finally , 这个 conclusion

58
00:03:09,490 --> 00:03:13,530
是 它 是 due 到 教育 .

59
00:03:13,530 --> 00:03:18,150
people 有 improved access 到 好的 ways 的 学习 和 ,

60
00:03:18,150 --> 00:03:21,010
的 课程 , improved 教育 .

61
00:03:21,010 --> 00:03:25,470
as 一个 consequence , iq scores went 上 .

62
00:03:25,470 --> 00:03:27,710
people got smarter .

63
00:03:27,710 --> 00:03:30,000
但是 这里 ' s 这个 funny thing .

64
00:03:30,000 --> 00:03:33,060
从 这个 1970s onwards ,

65
00:03:33,060 --> 00:03:35,650
那里 ' s plenty 的 research showing 那个

66
00:03:35,650 --> 00:03:38,930
iq scores 是 declining .

67
00:03:38,930 --> 00:03:42,090
这个 是 even within 一个 family .

68
00:03:42,090 --> 00:03:45,325
你 能 看 那个 kids 谁 是 younger ,

69
00:03:45,325 --> 00:03:50,250
born 在...之后 这个 latter 部分 的 这个 1970s ,

70
00:03:50,250 --> 00:03:55,605
有 一个 lower iq than older kids within 这个 相同的 family .

71
00:03:55,605 --> 00:03:58,380
为什么 会 这个 是 ?

72
00:03:58,380 --> 00:04:03,840
在 这个 1970s , calculators came out .

73
00:04:03,840 --> 00:04:06,315
当 calculators came out ,

74
00:04:06,315 --> 00:04:08,220
suddenly 它 became ,

75
00:04:08,220 --> 00:04:11,610
well , 你 don ' t need 到 remember things anymore .

76
00:04:11,610 --> 00:04:14,550
你 能 always just look things 上 .

77
00:04:14,550 --> 00:04:17,410
as 你 ' ll 看 later 在 这个 课程 ,

78
00:04:17,410 --> 00:04:19,830
那个 ' s 一个 real 问题 .

79
00:04:19,830 --> 00:04:22,950
students stopped practicing within

80
00:04:22,950 --> 00:04:26,415
他们的 own 思维 和 remembering things .

81
00:04:26,415 --> 00:04:29,200
什么 这个 真的 tells 我们 是 那个

82
00:04:29,200 --> 00:04:32,920
与 这个 revolution 的 生成式 AI ,

83
00:04:32,920 --> 00:04:34,520
我们 don ' t want 到 制作

84
00:04:34,520 --> 00:04:38,440
这个 相同的 mistake 我们 made 当 calculators came out .

85
00:04:38,440 --> 00:04:42,460
我们 want 到 keep 学习 和 getting 练习

86
00:04:42,460 --> 00:04:44,820
与 一些 的 这个 essential ideas

87
00:04:44,820 --> 00:04:46,380
那个 是 真的 重要的 ,

88
00:04:46,380 --> 00:04:50,300
为了 例子 , 学习 这个 multiplication tables

89
00:04:50,300 --> 00:04:53,675
或 学习 如何 到 put together 一个 essay .

90
00:04:53,675 --> 00:04:56,950
我们 don ' t want 到 是 like 这个 1970s 哪里

91
00:04:56,950 --> 00:05:00,250
everybody got 所有 excited 关于 一个 新的 技术 ,

92
00:05:00,250 --> 00:05:01,970
和 然后 他们 started saying ,

93
00:05:01,970 --> 00:05:04,510
你 don ' t need 到 actually 学习 一些 的

94
00:05:04,510 --> 00:05:06,970
这些 重要的 ideas 因为

95
00:05:06,970 --> 00:05:09,470
你 能 always 去 look 它 上 .

96
00:05:09,470 --> 00:05:15,270
这个 想法 那个 生成式 AI 是 problematic 是 ,

97
00:05:15,270 --> 00:05:19,490
我 想 , 一个 重要的 one 为了 我们 到 keep 在 思维 .

98
00:05:19,490 --> 00:05:22,250
但是 我 also want 到 talk 关于 something 那个 ' s

99
00:05:22,250 --> 00:05:26,455
一个 little bit 在 defense 的 生成式 AI .

100
00:05:26,455 --> 00:05:31,220
我 wasn ' t 好的 在 math 和 science 当 我 是 growing 上 .

101
00:05:31,220 --> 00:05:34,540
我 enlisted 在 这个 army out 的 high school ,

102
00:05:34,540 --> 00:05:38,470
studied 一个 语言 , 我 picked russian 在 random ,

103
00:05:38,470 --> 00:05:40,620
和 became 一个 translator .

104
00:05:40,620 --> 00:05:45,560
但是 然后 我 realized 那个 我 ' d followed 我的 passion ,

105
00:05:45,560 --> 00:05:48,280
just as everyone 有 told 我 到 做 ,

106
00:05:48,280 --> 00:05:51,220
但是 我 hadn ' t broadened 我的 passion

107
00:05:51,220 --> 00:05:54,820
和 started 学习 关于 broader fields .

108
00:05:54,820 --> 00:05:58,200
那个 meant 当 它 came time 到 得到 jobs ,

109
00:05:58,200 --> 00:06:02,440
那个 是 pretty tough 为了 我 到 做 因为 few people

110
00:06:02,440 --> 00:06:05,500
是 interested 在 我的 slavic languages

111
00:06:05,500 --> 00:06:07,740
和 literature skills .

112
00:06:07,740 --> 00:06:13,070
在 age 26 , 我 went 到 这个 大学 和 decided

113
00:06:13,070 --> 00:06:18,650
到 try 到 retrain 我的 大脑 和 学习 在 math 和 science .

114
00:06:18,650 --> 00:06:22,650
我 started 在 这个 lowest possible level 的

115
00:06:22,650 --> 00:06:25,490
remedial high school algebra 和

116
00:06:25,490 --> 00:06:29,610
slowly began climbing 我的 方式 upwards .

117
00:06:29,610 --> 00:06:32,670
现在 , 的 课程 , 我 ' m 一个 教授 的 工程 ,

118
00:06:32,670 --> 00:06:35,720
和 我 love math 和 science .

119
00:06:35,720 --> 00:06:40,050
但是 one challenge 那个 我 有 当 我 是 trying 到 retrain

120
00:06:40,050 --> 00:06:42,710
我的 大脑 是 我 found 那个

121
00:06:42,710 --> 00:06:47,170
professors 和 teachers 会 often keep things hidden .

122
00:06:47,170 --> 00:06:51,210
在 其他 words , 你 可能 得到 一个 lot 的 homework problems ,

123
00:06:51,210 --> 00:06:55,050
但是 你的 teacher didn ' t necessarily want 到 给 你

124
00:06:55,050 --> 00:06:57,830
这个 solutions 到 这个 homework problems

125
00:06:57,830 --> 00:07:00,010
因为 然后 你 ' d 知道 什么 这个 answers 是 ,

126
00:07:00,010 --> 00:07:02,960
和 一些 people 会 使用 那个 到 cheat .

127
00:07:02,960 --> 00:07:07,430
你 couldn ' t 得到 一个 lot 的 problems 到 solve 和 到 工作

128
00:07:07,430 --> 00:07:10,270
与 something 那个 related 到 exactly 什么

129
00:07:10,270 --> 00:07:13,230
这个 教授 是 teaching 在 这个 课程 .

130
00:07:13,230 --> 00:07:16,070
它 是 just difficult 到 找到 lots

131
00:07:16,070 --> 00:07:18,610
的 练习 problems 因为 teachers

132
00:07:18,610 --> 00:07:21,470
certainly weren ' t going 到 是 willing

133
00:07:21,470 --> 00:07:24,960
到 share 他们的 testing 或 练习 materials .

134
00:07:24,960 --> 00:07:28,670
但是 nowadays , 它 ' s 非常 不同的 .

135
00:07:28,670 --> 00:07:31,430
如果 你 ' re 真的 interested 在 学习 ,

136
00:07:31,430 --> 00:07:33,910
你 能 得到 lots 和 lots 的

137
00:07:33,910 --> 00:07:38,170
练习 materials 从 ChatGPT 和 其他 engines .

138
00:07:38,170 --> 00:07:40,890
你 don ' t 有 到 worry 所以 很多

139
00:07:40,890 --> 00:07:44,190
那个 这个 知识 是 something reserved only

140
00:07:44,190 --> 00:07:46,990
为了 那些 谁 worked 真的 困难的

141
00:07:46,990 --> 00:07:51,060
到 create 这个 test materials 和 这样的 like .

142
00:07:51,060 --> 00:07:53,730
这个 world 是 更多 open 为了

143
00:07:53,730 --> 00:07:56,470
learners 谁 truly want 到 学习 ,

144
00:07:56,470 --> 00:08:01,125
和 你 want 到 是 someone 谁 truly wants 到 学习 .

145
00:08:01,125 --> 00:08:05,225
again , 我 refer 到 fei fei li ' s quote ,

146
00:08:05,225 --> 00:08:08,390
AI won ' t replace humans ,

147
00:08:08,390 --> 00:08:15,290
但是 humans 那个 使用 AI 将 replace humans 那个 don ' t .

