#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SRT字幕文件英译中翻译工具（简单版本）
使用基本词典进行翻译，避免网络依赖
"""

import os
import re
from typing import List, Dict
import argparse

class SimpleSRTTranslator:
    def __init__(self):
        """初始化翻译器"""
        self.subtitle_pattern = re.compile(
            r'(\d+)\n'  # 字幕序号
            r'(\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3})\n'  # 时间码
            r'(.*?)\n\n',  # 字幕内容
            re.DOTALL
        )
        
        # 基本翻译词典
        self.translation_dict = {
            # 常用词汇
            "welcome": "欢迎",
            "course": "课程",
            "learning": "学习",
            "learn": "学习",
            "chatgpt": "ChatGPT",
            "accelerate": "加速",
            "your": "你的",
            "with": "与",
            "to": "到",
            "our": "我们的",
            "i'm": "我是",
            "professor": "教授",
            "distinguished": "杰出的",
            "engineering": "工程",
            "university": "大学",
            "you": "你",
            "might": "可能",
            "know": "知道",
            "me": "我",
            "from": "从",
            "how": "如何",
            "one": "一个",
            "of": "的",
            "most": "最",
            "popular": "受欢迎的",
            "courses": "课程",
            "or": "或",
            "my": "我的",
            "books": "书籍",
            "on": "关于",
            "techniques": "技术",
            "today": "今天",
            "thrilled": "兴奋的",
            "introduce": "介绍",
            "this": "这个",
            "exciting": "令人兴奋的",
            "that": "那个",
            "explores": "探索",
            "the": "这个",
            "intersection": "交集",
            "generative": "生成式",
            "ai": "AI",
            "and": "和",
            "effective": "有效的",
            "strategies": "策略",
            "we'll": "我们将",
            "dive": "深入",
            "into": "进入",
            "neuroscience": "神经科学",
            "showing": "展示",
            "what": "什么",
            "happens": "发生",
            "in": "在",
            "brain": "大脑",
            "when": "当",
            "complement": "补充",
            "these": "这些",
            "processes": "过程",
            "discover": "发现",
            "use": "使用",
            "create": "创建",
            "engaging": "引人入胜的",
            "hooks": "钩子",
            "for": "为了",
            "studies": "研究",
            "connect": "连接",
            "ideas": "想法",
            "personal": "个人的",
            "experiences": "经验",
            "even": "甚至",
            "overcome": "克服",
            "limitations": "限制",
            "working": "工作",
            "memory": "记忆",
            "joining": "加入",
            "is": "是",
            "friend": "朋友",
            "leading": "领先的",
            "expertise": "专业知识",
            "can": "可以",
            "learners": "学习者",
            "expect": "期待",
            "please": "请",
            "no": "没有",
            "robot": "机器人",
            "imitations": "模仿",
            "time": "时间",
            "well": "好",
            "promise": "承诺",
            "not": "不",
            "imitate": "模仿",
            "any": "任何",
            "robots": "机器人",
            "completely": "完全",
            "because": "因为",
            "if": "如果",
            "we": "我们",
            "think": "思考",
            "about": "关于",
            "star": "星",
            "wars": "战争",
            "three": "三",
            "he's": "他是",
            "protocol": "协议",
            "droid": "机器人",
            "helps": "帮助",
            "translate": "翻译",
            "people": "人们",
            "are": "是",
            "saying": "说",
            "around": "周围",
            "him": "他",
            "different": "不同的",
            "languages": "语言",
            "going": "去",
            "as": "作为",
            "important": "重要的",
            "concepts": "概念",
            "models": "模型",
            "start": "开始",
            "thinking": "思考",
            "do": "做",
            "get": "得到",
            "them": "他们",
            "ways": "方式",
            "approach": "方法",
            "more": "更多",
            "effectively": "有效地",
            "analogies": "类比",
            "metaphors": "隐喻",
            "resonate": "共鸣",
            "with": "与",
            "us": "我们",
            "don't": "不",
            "it": "它",
            "something": "某事",
            "haven't": "没有",
            "seen": "看到",
            "idea": "想法",
            "concept": "概念",
            "explanation": "解释",
            "makes": "使",
            "sense": "意义",
            "help": "帮助",
            "be": "是",
            "able": "能够",
            "bridge": "桥梁",
            "gap": "差距",
            "go": "去",
            "complicated": "复杂的",
            "explanations": "解释",
            "but": "但是",
            "also": "也",
            "have": "有",
            "see": "看",
            "amazing": "惊人的",
            "things": "事情",
            "like": "像",
            "snap": "拍摄",
            "picture": "图片",
            "bake": "烘烤",
            "make": "制作",
            "device": "设备",
            "we'd": "我们想",
            "talk": "谈论",
            "why": "为什么",
            "can't": "不能",
            "give": "给",
            "up": "上",
            "traditional": "传统的",
            "importance": "重要性",
            "actually": "实际上",
            "knowing": "知道",
            "lot": "很多",
            "studying": "学习",
            "prompting": "提示",
            "may": "可能",
            "teach": "教",
            "called": "叫做",
            "prompt": "提示",
            "chat": "聊天",
            "gpt": "GPT",
            "which": "哪个",
            "was": "是",
            "year": "年",
            "really": "真的",
            "excited": "兴奋的",
            "helping": "帮助",
            "explore": "探索",
            "simple": "简单的",
            "enrich": "丰富",
            "understanding": "理解",
            "topics": "主题",
            "now": "现在",
            "won't": "不会",
            "absolutely": "绝对",
            "will": "将",
            "tell": "告诉",
            "some": "一些",
            "fascinating": "迷人的",
            "capabilities": "能力",
            "allow": "允许",
            "tap": "利用",
            "improve": "改进",
            "[music]": "[音乐]",
            "music": "音乐"
        }
    
    def parse_srt_file(self, file_path: str) -> List[Dict]:
        """解析SRT文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except UnicodeDecodeError:
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
        
        if not content.endswith('\n\n'):
            content += '\n\n'
        
        subtitles = []
        matches = self.subtitle_pattern.findall(content)
        
        for match in matches:
            subtitle_num, timecode, text = match
            subtitles.append({
                'number': int(subtitle_num),
                'timecode': timecode,
                'text': text.strip()
            })
        
        return subtitles
    
    def simple_translate(self, text: str) -> str:
        """简单翻译函数"""
        if text.strip() in ['[MUSIC]', '[APPLAUSE]', '[LAUGHTER]', ''] or not text.strip():
            return text.replace('[MUSIC]', '[音乐]').replace('[APPLAUSE]', '[掌声]').replace('[LAUGHTER]', '[笑声]')
        
        # 转换为小写进行匹配
        words = re.findall(r'\b\w+\b|[^\w\s]', text.lower())
        translated_words = []
        
        for word in words:
            if word in self.translation_dict:
                translated_words.append(self.translation_dict[word])
            else:
                # 保持原词
                translated_words.append(word)
        
        return ' '.join(translated_words)
    
    def translate_subtitles(self, subtitles: List[Dict]) -> List[Dict]:
        """翻译字幕列表"""
        translated_subtitles = []
        
        for i, subtitle in enumerate(subtitles):
            print(f"正在翻译第 {i+1}/{len(subtitles)} 条字幕...")
            
            translated_text = self.simple_translate(subtitle['text'])
            
            translated_subtitles.append({
                'number': subtitle['number'],
                'timecode': subtitle['timecode'],
                'text': translated_text
            })
        
        return translated_subtitles
    
    def write_srt_file(self, subtitles: List[Dict], output_path: str):
        """写入SRT文件"""
        with open(output_path, 'w', encoding='utf-8') as f:
            for subtitle in subtitles:
                f.write(f"{subtitle['number']}\n")
                f.write(f"{subtitle['timecode']}\n")
                f.write(f"{subtitle['text']}\n\n")
    
    def translate_srt_file(self, input_path: str, output_path: str):
        """翻译单个SRT文件"""
        print(f"开始处理文件: {input_path}")
        
        subtitles = self.parse_srt_file(input_path)
        print(f"解析到 {len(subtitles)} 条字幕")
        
        translated_subtitles = self.translate_subtitles(subtitles)
        
        self.write_srt_file(translated_subtitles, output_path)
        print(f"翻译完成，输出文件: {output_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SRT字幕文件英译中翻译工具（简单版本）')
    parser.add_argument('--input', '-i', help='输入SRT文件路径')
    parser.add_argument('--output', '-o', help='输出SRT文件路径')
    
    args = parser.parse_args()
    
    if not args.input or not args.output:
        print("请指定输入和输出文件")
        return
    
    translator = SimpleSRTTranslator()
    translator.translate_srt_file(args.input, args.output)

if __name__ == "__main__":
    main()
