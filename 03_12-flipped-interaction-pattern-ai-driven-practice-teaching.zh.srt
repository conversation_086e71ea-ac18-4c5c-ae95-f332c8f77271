1
00:00:00,000 --> 00:00:03,440
fascinating capability 的 这些 large 语言 models

2
00:00:03,440 --> 00:00:04,940
是 到 帮助 我们 练习 ,

3
00:00:04,940 --> 00:00:06,960
但是 做 它 interactively .

4
00:00:06,960 --> 00:00:09,740
现在 , 最 people probably 得到 这个 sense 现在 的 ,

5
00:00:09,740 --> 00:00:12,020
如何 你 会 去 在 和 generate 一个 quiz 与

6
00:00:12,020 --> 00:00:14,880
ten questions 那个 你 能 去 和 然后 拿 .

7
00:00:14,880 --> 00:00:18,480
但是 什么 如果 你 want 到 去 和 dynamically 练习 ,

8
00:00:18,480 --> 00:00:21,600
哪里 这个 模型 adapts 到 你的 answers ,

9
00:00:21,600 --> 00:00:24,220
和 然后 uses 什么 你 说 到

10
00:00:24,220 --> 00:00:25,720
choose 什么 这个 next thing 是 那个

11
00:00:25,720 --> 00:00:27,940
它 asks 你 或 tells 你 ,

12
00:00:27,940 --> 00:00:29,740
那个 想法 的 一个 sort 的

13
00:00:29,740 --> 00:00:32,180
dynamic interactive 练习 是 something 那个 我们

14
00:00:32,180 --> 00:00:35,145
能 achieve 与 这个 flipped interaction pattern ?

15
00:00:35,145 --> 00:00:38,110
这个 想法 behind 这个 pattern 是 那个 ,

16
00:00:38,110 --> 00:00:41,490
rather than telling one 的 这些 models 什么 到 做 ,

17
00:00:41,490 --> 00:00:43,770
rather than asking 它 questions .

18
00:00:43,770 --> 00:00:46,270
我们 want 到 得到 它 到 ask 我们 questions ,

19
00:00:46,270 --> 00:00:47,970
和 然后 based 在 我们的 responses ,

20
00:00:47,970 --> 00:00:50,170
dynamically adapt 和 flow

21
00:00:50,170 --> 00:00:51,870
通过 whatever 它 是 那个

22
00:00:51,870 --> 00:00:54,080
我们 ' re trying 到 achieve 或 练习 .

23
00:00:54,080 --> 00:00:55,670
let 我 给 你 一个 例子 和

24
00:00:55,670 --> 00:00:57,765
describe 如何 这个 pattern works .

25
00:00:57,765 --> 00:00:59,230
这个 是 一个 提示 那个

26
00:00:59,230 --> 00:01:01,565
employs 这个 flipped interaction pattern .

27
00:01:01,565 --> 00:01:03,510
这个 basic 想法 behind 这个 是 ,

28
00:01:03,510 --> 00:01:05,310
我们 是 going 到 告诉 这个 模型

29
00:01:05,310 --> 00:01:07,850
到 ask 我们 techniques 或 ask 我们

30
00:01:07,850 --> 00:01:10,010
questions 或 告诉 我们 到 做 things

31
00:01:10,010 --> 00:01:12,940
one step 在 一个 time 或 one question 在 一个 time .

32
00:01:12,940 --> 00:01:15,330
然后 我们 ' re going 到 说 , based 在 什么 我们 说 ,

33
00:01:15,330 --> 00:01:17,750
然后 去 和 respond 和

34
00:01:17,750 --> 00:01:20,590
choose 这个 next question 或 给 我 这个 next step .

35
00:01:20,590 --> 00:01:23,110
那个 是 , 我们 want 它 到 去 one step 在 一个 time ,

36
00:01:23,110 --> 00:01:24,430
telling 我们 什么 到 做 .

37
00:01:24,430 --> 00:01:25,790
这个 是 真的 重要的

38
00:01:25,790 --> 00:01:28,470
因为 通过 telling 它 到 去 one step 在 一个 time ,

39
00:01:28,470 --> 00:01:31,310
然后 它 能 看 我们的 answer 或 它 能 看

40
00:01:31,310 --> 00:01:32,890
这个 反馈 那个 我们 给 它

41
00:01:32,890 --> 00:01:34,995
在 这个 step 它 told 我们 到 perform .

42
00:01:34,995 --> 00:01:37,200
然后 它 能 decide 什么 到 做 next .

43
00:01:37,200 --> 00:01:39,050
那个 是 , 它 是 flipping 这个 script .

44
00:01:39,050 --> 00:01:42,035
它 是 getting 到 drive 这个 interaction 与 我们 .

45
00:01:42,035 --> 00:01:44,570
这里 是 这个 flipped interaction prop

46
00:01:44,570 --> 00:01:47,365
那个 我们 ' re going 到 使用 到 build 一个 interactive quiz .

47
00:01:47,365 --> 00:01:49,480
请 quiz beyond phishing

48
00:01:49,480 --> 00:01:51,370
和 techniques 那个 是 used 到 trick people .

49
00:01:51,370 --> 00:01:53,260
phishing 是 这个 技术

50
00:01:53,260 --> 00:01:54,940
哪里 people 去 和 send , 为了 例子 ,

51
00:01:54,940 --> 00:01:57,000
e mails 到 somebody 到 try 到 trick 他们

52
00:01:57,000 --> 00:01:59,560
进入 doing something 那个 是 inappropriate ,

53
00:01:59,560 --> 00:02:01,540
所以 like giving 上 他们的 password .

54
00:02:01,540 --> 00:02:03,080
break 这个 主题 down 进入

55
00:02:03,080 --> 00:02:05,425
individual topics 那个 someone needs 到 学习 .

56
00:02:05,425 --> 00:02:07,410
ask 我 questions one 在

57
00:02:07,410 --> 00:02:09,870
一个 time 到 assess 我的 知识 的 这些 topics .

58
00:02:09,870 --> 00:02:11,670
在 这个 end , suggest topics 那个 我

59
00:02:11,670 --> 00:02:13,745
应该 focus 在 到 improve 我的 理解 .

60
00:02:13,745 --> 00:02:15,260
ask 我 这个 first question .

61
00:02:15,260 --> 00:02:18,070
这个 pattern 的 , ask 我 questions one 在

62
00:02:18,070 --> 00:02:20,690
一个 time 和 到 achieve 一些 particular goals .

63
00:02:20,690 --> 00:02:22,430
在 这个 case , 这个 goal 是 到

64
00:02:22,430 --> 00:02:24,840
test 我的 知识 的 这些 topics .

65
00:02:24,840 --> 00:02:28,010
然后 在 这个 end , based 在 我的 responses ,

66
00:02:28,010 --> 00:02:30,050
去 back 和 suggest

67
00:02:30,050 --> 00:02:31,870
这个 topics 那个 我 应该

68
00:02:31,870 --> 00:02:33,580
focus 在 到 improve 我的 理解 .

69
00:02:33,580 --> 00:02:35,470
然后 我 说 , ask 我 这个 first question .

70
00:02:35,470 --> 00:02:37,950
那个 asked 我 这个 first question 是 一个 little trick ,

71
00:02:37,950 --> 00:02:40,630
一个 提示 工程 trick 到 制作 sure

72
00:02:40,630 --> 00:02:43,945
那个 我们 generate one question 在 一个 time ideally .

73
00:02:43,945 --> 00:02:46,260
现在 , 我们 能 also 做 something like 这个 那个 says ,

74
00:02:46,260 --> 00:02:49,040
去 和 告诉 我 something 到 做 one step 在 一个 time .

75
00:02:49,040 --> 00:02:50,960
如果 我们 是 wanting 到 使用 一个 coffee maker .

76
00:02:50,960 --> 00:02:53,180
我们 能 说 , 告诉 我 one step 在 一个 time ,

77
00:02:53,180 --> 00:02:55,740
什么 到 做 在 order 到 使用 这个 coffee maker .

78
00:02:55,740 --> 00:02:58,280
你 知道 , 我 将 告诉 你 什么 happened ,

79
00:02:58,280 --> 00:03:00,170
和 然后 你 将 告诉 我 什么 到 做

80
00:03:00,170 --> 00:03:02,900
next . . 然后 告诉 我 这个 first thing 到 做 .

81
00:03:02,900 --> 00:03:04,440
similar type 的 thing 那个 是 我们

82
00:03:04,440 --> 00:03:05,940
want 它 到 drive 这个 interaction .

83
00:03:05,940 --> 00:03:07,270
我们 want 到 flip 这个 script .

84
00:03:07,270 --> 00:03:10,140
它 says , sure ,

85
00:03:10,140 --> 00:03:13,080
let ' s break down 这个 主题 的 fishing 进入 key areas ,

86
00:03:13,080 --> 00:03:15,720
definition 和 basics , types 的 fishing attacks ,

87
00:03:15,720 --> 00:03:18,585
techniques used 在 fishing , recognizing 他们 .

88
00:03:18,585 --> 00:03:21,280
然后 它 says , question one ,

89
00:03:21,280 --> 00:03:24,120
什么 是 fishing , 和 什么 是 its primary goal ?

90
00:03:24,120 --> 00:03:28,120
它 ' s asked 我们 这个 first question 和 waits 因为 它

91
00:03:28,120 --> 00:03:29,940
understands 那个 它 needs 到 ask

92
00:03:29,940 --> 00:03:33,025
一个 question 和 然后 wait 为了 我们的 response .

93
00:03:33,025 --> 00:03:34,780
然后 我 去 和 我 说 , 到 trick

94
00:03:34,780 --> 00:03:36,340
一个 user 进入 performing 一个 action ,

95
00:03:36,340 --> 00:03:37,680
often giving 上 信息 ,

96
00:03:37,680 --> 00:03:39,085
和 那个 是 我的 response .

97
00:03:39,085 --> 00:03:40,640
然后 它 says , correct ,

98
00:03:40,640 --> 00:03:42,560
phishing 是 一个 cyber attack 技术 哪里

99
00:03:42,560 --> 00:03:44,320
这个 attackers trick individuals

100
00:03:44,320 --> 00:03:46,125
进入 revealing sensitive 信息 .

101
00:03:46,125 --> 00:03:47,820
然后 它 goes 在 到 question two .

102
00:03:47,820 --> 00:03:50,435
什么 是 一些 common types 的 phishing attacks ?

103
00:03:50,435 --> 00:03:52,760
它 有 looked 在 我的 answer ,

104
00:03:52,760 --> 00:03:54,500
determined 那个 我 got 它 正确的 ,

105
00:03:54,500 --> 00:03:57,150
和 然后 它 ' s moved 在 到 这个 next question .

106
00:03:57,150 --> 00:04:00,020
我 去 和 我 answer , catching bass

107
00:04:00,020 --> 00:04:01,500
在 一个 boat , deep sea fishing ,

108
00:04:01,500 --> 00:04:03,640
和 trawling , 哪个 是 obviously

109
00:04:03,640 --> 00:04:07,230
这个 错误的 type 的 fishing . 如何 做 它 respond ?

110
00:04:07,230 --> 00:04:08,790
它 says , 它 seems like 那里 可能

111
00:04:08,790 --> 00:04:10,550
有 是 一个 mix 上 与 这个 question .

112
00:04:10,550 --> 00:04:12,690
在 这个 context 的 cybersecurity ,

113
00:04:12,690 --> 00:04:14,390
这里 是 common types 的 fishing attacks .

114
00:04:14,390 --> 00:04:15,910
所以 它 sees 那个 我 ' m mixed 上 在

115
00:04:15,910 --> 00:04:18,430
这个 概念 那个 我们 ' re talking 关于 e mail fishing ,

116
00:04:18,430 --> 00:04:21,010
和 我 ' ve started talking 关于 actual phishing ,

117
00:04:21,010 --> 00:04:23,510
catching fish , 哪个 是 一个 不同的 thing ,

118
00:04:23,510 --> 00:04:27,310
ph versus f . 所以 它 says e mail fishing ,

119
00:04:27,310 --> 00:04:29,850
spear fishing , whaling , fishing , smishing .

120
00:04:29,850 --> 00:04:31,550
tells 我 所有 这些 不同的 types ,

121
00:04:31,550 --> 00:04:33,430
和 然后 goes 在 到 这个 next question .

122
00:04:33,430 --> 00:04:35,450
什么 techniques 做 users 做

123
00:04:35,450 --> 00:04:38,880
attackers 使用 到 制作 phishing e mails appear legitimate ?

124
00:04:38,880 --> 00:04:41,270
notice 它 ' s following its original outline

125
00:04:41,270 --> 00:04:44,235
在 这个 过程 的 这个 questions 那个 它 ' s asking 我 .

126
00:04:44,235 --> 00:04:46,110
我 来 back 和 我 说 sending 从

127
00:04:46,110 --> 00:04:48,290
e mail addresses 那个 look like trusted sources ,

128
00:04:48,290 --> 00:04:49,650
using 语言 和 e mail ,

129
00:04:49,650 --> 00:04:51,515
design similar 到 legitimate sources .

130
00:04:51,515 --> 00:04:54,235
然后 它 comes back 和 它 says , 那个 ' s correct .

131
00:04:54,235 --> 00:04:56,830
然后 它 tells 我 所有 的 这个 techniques 那个

132
00:04:56,830 --> 00:04:58,810
它 有 在 its head 的 如何 这个 是 done .

133
00:04:58,810 --> 00:05:00,230
然后 它 says , question four .

134
00:05:00,230 --> 00:05:03,200
如何 能 你 recognize 一个 fishing attempt ?

135
00:05:04,630 --> 00:05:07,530
但是 这个 key 是 那个

136
00:05:07,530 --> 00:05:09,890
它 是 going 和 driving 这个 interaction .

137
00:05:09,890 --> 00:05:12,750
它 是 asking 我 这个 questions one 在 一个 time ,

138
00:05:12,750 --> 00:05:14,650
和 based 在 我的 answer ,

139
00:05:14,650 --> 00:05:17,320
它 ' s deciding 什么 到 ask next .

140
00:05:17,320 --> 00:05:18,630
这个 是 一个 真的 ,

141
00:05:18,630 --> 00:05:22,630
真的 critical 和 amazing capability 是 its ability 到

142
00:05:22,630 --> 00:05:25,550
decide 什么 应该 是 asked next 和

143
00:05:25,550 --> 00:05:28,525
到 respond 和 adapt based 在 我的 理解 .

144
00:05:28,525 --> 00:05:30,470
当 我 didn ' t 理解 那个 它

145
00:05:30,470 --> 00:05:32,610
是 talking 关于 fishing as 一个 e mail fishing ,

146
00:05:32,610 --> 00:05:34,510
我 started talking 关于 actual fishing .

147
00:05:34,510 --> 00:05:36,070
我 said , 不 , 那个 ' s 不 相当 正确的 .

148
00:05:36,070 --> 00:05:37,430
你 ' re ale bit confused .

149
00:05:37,430 --> 00:05:39,345
这里 ' s 什么 我 ' m actually talking 关于 .

150
00:05:39,345 --> 00:05:42,710
现在 , let ' s 去 和

151
00:05:42,710 --> 00:05:43,990
look 在 如何 我们 能 still 使用

152
00:05:43,990 --> 00:05:45,250
flipped interaction 另一个 方式 .

153
00:05:45,250 --> 00:05:46,830
certainly , 我们 能 使用 它 为了 练习

154
00:05:46,830 --> 00:05:47,970
和 interactive 练习 ,

155
00:05:47,970 --> 00:05:49,730
但是 我们 能 also 去 和

156
00:05:49,730 --> 00:05:51,850
使用 它 到 教 我们 one step 在 一个 time .

157
00:05:51,850 --> 00:05:53,550
similar 到 如何 一个 teacher 会 ,

158
00:05:53,550 --> 00:05:55,590
rather than just saying , 解释

159
00:05:55,590 --> 00:05:57,660
everything 到 我 所有 在 once .

160
00:05:57,660 --> 00:06:00,665
我们 能 说 , 教 它 到 我 one step 在 一个 time .

161
00:06:00,665 --> 00:06:03,930
我 ' m going 到 说 , as 一个 expert 在 breaking down

162
00:06:03,930 --> 00:06:05,190
复杂的 topics 和 teaching

163
00:06:05,190 --> 00:06:07,350
通过 简单的 analogies 和 metaphors .

164
00:06:07,350 --> 00:06:11,150
教 我 如何 transformer models 工作 one step 在 一个 time .

165
00:06:11,150 --> 00:06:13,010
在...之后 你 教 我 每个 主题 ,

166
00:06:13,010 --> 00:06:15,610
ask 我 一个 question 到 assess 我的 理解 ,

167
00:06:15,610 --> 00:06:18,010
adapt 什么 你 教 next 或

168
00:06:18,010 --> 00:06:20,785
review based 在 如何 well 我 answer 这个 question .

169
00:06:20,785 --> 00:06:24,410
它 ' s 不 just 去 和 教 我 one step 在 一个 time ,

170
00:06:24,410 --> 00:06:26,670
但是 ask 我 一个 question 在 这个 end 的

171
00:06:26,670 --> 00:06:29,390
teaching 我 something 到 制作 sure 我 理解 它 ,

172
00:06:29,390 --> 00:06:31,130
和 然后 based 在 我的 response ,

173
00:06:31,130 --> 00:06:33,065
decide 什么 到 做 next .

174
00:06:33,065 --> 00:06:34,070
然后 , again , 我 ' m using

175
00:06:34,070 --> 00:06:35,630
这个 little 提示 工程 技术 ,

176
00:06:35,630 --> 00:06:37,005
教 我 这个 first thing .

177
00:06:37,005 --> 00:06:38,805
它 comes back 和 它 says , alright ,

178
00:06:38,805 --> 00:06:40,090
let ' s dive 进入 这个 first 主题 .

179
00:06:40,090 --> 00:06:42,190
理解 words 和 context ,

180
00:06:42,190 --> 00:06:43,990
概念 , word embeddings .

181
00:06:43,990 --> 00:06:45,670
imagine 你 ' re 在 一个 party 与 一个 lot

182
00:06:45,670 --> 00:06:47,530
的 people speaking 不同的 languages .

183
00:06:47,530 --> 00:06:49,390
到 理解 什么 someone 是 saying ,

184
00:06:49,390 --> 00:06:50,990
你 need 更多 than just words ,

185
00:06:50,990 --> 00:06:53,590
你 need 这个 context 在 哪个 他们 是 used .

186
00:06:53,590 --> 00:06:56,155
analogy , 这个 word map .

187
00:06:56,155 --> 00:06:58,840
然后 它 goes 在 和 它 says , question 为了 你 .

188
00:06:58,840 --> 00:07:01,800
如果 cat 和 dog 是 close together 在 这个 word map ,

189
00:07:01,800 --> 00:07:03,600
什么 做 那个 告诉 我们 关于 这些 words

190
00:07:03,600 --> 00:07:05,880
在 terms 的 meaning 或 context ?

191
00:07:05,880 --> 00:07:09,640
它 ' s taken 这个 概念 的 embeddings .

192
00:07:09,640 --> 00:07:15,780
它 ' s translated 那个 进入 这个 analogy 的 word maps .

193
00:07:15,780 --> 00:07:18,040
然后 , 它 ' s asking 我

194
00:07:18,040 --> 00:07:21,430
一个 question based 在 什么 它 just taught 我 .

195
00:07:21,430 --> 00:07:24,010
我 说 , okay ,

196
00:07:24,010 --> 00:07:26,500
那个 会 mean 那个 他们 有

197
00:07:26,500 --> 00:07:28,680
similar semantics 或 similar meaning ,

198
00:07:28,680 --> 00:07:30,260
因为 那个 ' s 这个 想法 的 embeddings .

199
00:07:30,260 --> 00:07:32,740
如果 你 有 two words , 他们 ' re sort 的 close together .

200
00:07:32,740 --> 00:07:34,560
他们 ' re supposed 到 有 similar meaning

201
00:07:34,560 --> 00:07:37,740
和 它 says exactly cat 和 dog 是 close together .

202
00:07:37,740 --> 00:07:39,950
然后 它 goes 在 主题 two , attention mechanism .

203
00:07:39,950 --> 00:07:42,430
它 gives 我 一个 analogy , 这个 spotlight .

204
00:07:42,430 --> 00:07:45,410
它 然后 goes 在 和 它 says , question 为了 你 .

205
00:07:45,410 --> 00:07:47,910
为什么 可能 这个 attention mechanism 是 重要的 当

206
00:07:47,910 --> 00:07:49,070
interpreting 一个 sentence like

207
00:07:49,070 --> 00:07:51,050
这个 cat 哪个 我 adopted last year ,

208
00:07:51,050 --> 00:07:53,440
loves 到 play 与 我的 dog .

209
00:07:53,440 --> 00:07:55,300
然后 我 去 和 respond 和 我 说 , okay ,

210
00:07:55,300 --> 00:07:57,150
不 所有 cats like 到 play 与 dogs .

211
00:07:57,150 --> 00:07:59,410
这个 fact 那个 这个 是 这个 cat 哪个

212
00:07:59,410 --> 00:08:02,670
我 adopted last year 是 重要的 context .

213
00:08:02,670 --> 00:08:06,400
它 goes 在 和 它 says , exactly , 那个 ' s 正确的 .

214
00:08:06,400 --> 00:08:08,370
它 gives 我 另一个 analogy .

215
00:08:08,370 --> 00:08:09,980
然后 它 goes 通过 它

216
00:08:09,980 --> 00:08:12,540
again 和 asks 我 一个 question 关于 它 .

217
00:08:12,540 --> 00:08:16,140
什么 我们 ' re seeing 这里 是 这个 ability 到

218
00:08:16,140 --> 00:08:21,160
有 它 interactively 和 adaptively 工作 与 我们 ,

219
00:08:21,160 --> 00:08:22,900
任一 questioning 我们 为了

220
00:08:22,900 --> 00:08:26,800
练习 或 interactively teaching 我们 something

221
00:08:26,800 --> 00:08:29,940
和 然后 testing 我们的 知识 的 什么 它 just taught

222
00:08:29,940 --> 00:08:33,645
我们 和 adapt based 在 我们的 responses .

223
00:08:33,645 --> 00:08:36,515
我们 做 这个 通过 这个 flipped interaction pattern .

224
00:08:36,515 --> 00:08:40,640
这个 basic structure 的 这个 pattern 是 fairly 简单的 .

225
00:08:40,640 --> 00:08:43,410
什么 我们 说 是 , ask

226
00:08:43,410 --> 00:08:46,875
我 questions one 在 一个 time 在 order 到 achieve 这个 goal .

227
00:08:46,875 --> 00:08:49,340
在 这个 case , 我们 有 asked 我 questions 在 一个 one

228
00:08:49,340 --> 00:08:51,660
在 一个 time 到 assess 我的 知识 的 这些 topics ,

229
00:08:51,660 --> 00:08:53,965
和 这些 topics 是 在 fishing .

230
00:08:53,965 --> 00:08:57,380
然后 我们 说 , 你 知道 ,

231
00:08:57,380 --> 00:08:59,480
ask 我 这个 first question .

232
00:08:59,480 --> 00:09:01,305
教 我 这个 first thing .

233
00:09:01,305 --> 00:09:04,040
这个 key 是 是 我们 说 , ask questions .

234
00:09:04,040 --> 00:09:06,040
现在 , 我们 能 有 它 ask 一个 bunch 在 一个 time ,

235
00:09:06,040 --> 00:09:07,480
但是 我 想 它 ' s 最 有效的

236
00:09:07,480 --> 00:09:08,800
当 我们 做 这个 one 在 一个 time .

237
00:09:08,800 --> 00:09:11,160
ask 我 one 在 一个 time , 在 order 到

238
00:09:11,160 --> 00:09:13,680
achieve 这个 goal 在 order 到 教 我 这个 thing 在

239
00:09:13,680 --> 00:09:15,320
order 到 test 我的 知识 的

240
00:09:15,320 --> 00:09:19,220
这个 概念 在 order 到 whatever 它 是 你 want ,

241
00:09:19,220 --> 00:09:21,260
和 你 ' re basically telling 它 ,

242
00:09:21,260 --> 00:09:23,200
我们 ' re going 到 做 这个 one step 在 一个 time .

243
00:09:23,200 --> 00:09:25,050
你 ' re going 到 ask 我 这个 questions .

244
00:09:25,050 --> 00:09:26,640
你 ' re going 到 look 在 我的 response ,

245
00:09:26,640 --> 00:09:27,460
和 你 ' re going 到 adapt .

246
00:09:27,460 --> 00:09:28,640
我们 ' re going 到 去 one sep 在 一个 time ,

247
00:09:28,640 --> 00:09:30,040
和 你 ' re going 到 教 我 something ,

248
00:09:30,040 --> 00:09:32,180
like 如何 到 使用 那个 coffee maker

249
00:09:32,180 --> 00:09:35,610
或 如何 transformer models 工作 .

250
00:09:35,610 --> 00:09:37,670
然后 你 ' re going 到 adapt based 在

251
00:09:37,670 --> 00:09:39,720
这个 反馈 那个 我 给 你 .

252
00:09:39,720 --> 00:09:40,790
然后 我们 使用 这个 简单的 little

253
00:09:40,790 --> 00:09:42,170
提示 工程 技术 ,

254
00:09:42,170 --> 00:09:44,710
ask 我 这个 first question 到 得到 它 headed

255
00:09:44,710 --> 00:09:45,990
off 在 这个 direction 的 asking

256
00:09:45,990 --> 00:09:47,270
这些 questions one 在 一个 time ,

257
00:09:47,270 --> 00:09:50,290
rather than ten 在 一个 time 和 overwhelming .

