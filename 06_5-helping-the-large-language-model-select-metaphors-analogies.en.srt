1
00:00:00,000 --> 00:00:04,020
This example worked really
well where we translated

2
00:00:04,020 --> 00:00:05,920
this concept of why
we would want to go

3
00:00:05,920 --> 00:00:08,280
ahead and learn
about generative AI,

4
00:00:08,280 --> 00:00:09,520
even though it's changing so

5
00:00:09,520 --> 00:00:12,080
rapidly into the
automotive domain.

6
00:00:12,080 --> 00:00:15,660
But what happens when we

7
00:00:15,660 --> 00:00:17,260
don't know the rocks that it

8
00:00:17,260 --> 00:00:19,360
should use to translate into?

9
00:00:19,360 --> 00:00:23,315
That is, like, I am
expressly telling it

10
00:00:23,315 --> 00:00:25,840
use the automotive domain

11
00:00:25,840 --> 00:00:28,570
and engines or things like that.

12
00:00:28,570 --> 00:00:31,080
What happens when we don't know

13
00:00:31,080 --> 00:00:33,910
explicitly what the
rock should be?

14
00:00:33,910 --> 00:00:35,560
How do we get there?

15
00:00:35,560 --> 00:00:37,860
What is the right approach?

16
00:00:37,860 --> 00:00:40,640
Now, how else might we do this?

17
00:00:40,640 --> 00:00:41,880
Well, we might go and give it

18
00:00:41,880 --> 00:00:43,280
a richer picture of ourselves,

19
00:00:43,280 --> 00:00:45,600
because the truth is maybe
we don't just want to have

20
00:00:45,600 --> 00:00:48,210
it draw from the
automotive domain,

21
00:00:48,210 --> 00:00:49,620
maybe we do many things
where we want it

22
00:00:49,620 --> 00:00:51,360
to vary the examples.

23
00:00:51,360 --> 00:00:54,200
We don't want to always get
the same exact example.

24
00:00:54,200 --> 00:00:56,540
We don't want everything
to be an engine.

25
00:00:56,540 --> 00:00:58,920
We don't want
everything to be a car.

26
00:00:58,920 --> 00:01:01,420
We want to vary

27
00:01:01,420 --> 00:01:04,650
the different things that
we see that hopefully,

28
00:01:04,650 --> 00:01:07,140
we will connect
to something that

29
00:01:07,140 --> 00:01:08,580
is powerful in our brains or

30
00:01:08,580 --> 00:01:10,375
maybe we'll connect to
more than one thing,

31
00:01:10,375 --> 00:01:13,995
and seeing the
example translated

32
00:01:13,995 --> 00:01:19,035
into multiple different
domains is really important.

33
00:01:19,035 --> 00:01:21,190
I'm going to change
the prompt up,

34
00:01:21,190 --> 00:01:23,040
and I'm going to say about me

35
00:01:23,040 --> 00:01:24,790
: I'm a professor

36
00:01:24,790 --> 00:01:26,630
of Computer Science at
Vanderbilt University.

37
00:01:26,630 --> 00:01:28,215
I'm the senior advisor,

38
00:01:28,215 --> 00:01:30,130
I have a typo to
the Chancellor in

39
00:01:30,130 --> 00:01:32,470
Generative AI and
Enterprise and Education.

40
00:01:32,470 --> 00:01:35,050
I love the mountain bike
and ride BMX bikes.

41
00:01:35,050 --> 00:01:36,790
I like cooking and
healthy eating.

42
00:01:36,790 --> 00:01:38,110
Even if I'm not that great

43
00:01:38,110 --> 00:01:39,710
at being disciplined
with my eating,

44
00:01:39,710 --> 00:01:42,350
I like to understand the
science behind nutrition.

45
00:01:42,350 --> 00:01:44,730
Explain how the
transformer model

46
00:01:44,730 --> 00:01:46,530
works using analogies and

47
00:01:46,530 --> 00:01:48,190
metaphors that will
resonate with me.

48
00:01:48,190 --> 00:01:50,030
We're looking at that
transformer model

49
00:01:50,030 --> 00:01:52,150
that underlies all this stuff.

50
00:01:52,150 --> 00:01:55,010
Now, what I'm telling
it is I want you

51
00:01:55,010 --> 00:01:56,170
to use analogies and

52
00:01:56,170 --> 00:01:58,070
metaphors that will
make sense to me,

53
00:01:58,070 --> 00:01:59,655
and I'm telling it a lot about

54
00:01:59,655 --> 00:02:02,480
me and I want it to
go and pull from

55
00:02:02,480 --> 00:02:05,430
that domain of
knowledge to hopefully

56
00:02:05,430 --> 00:02:09,015
identify things that are helpful
to me and it comes back,

57
00:02:09,015 --> 00:02:11,510
transformer model was
a cooking process,

58
00:02:11,510 --> 00:02:14,490
and it talks about
ingredients as tokens,

59
00:02:14,490 --> 00:02:17,230
and the attention mechanism
is the chef's focus,

60
00:02:17,230 --> 00:02:18,750
layers as cooking steps,

61
00:02:18,750 --> 00:02:21,610
and it's coming up with
a whole description of

62
00:02:21,610 --> 00:02:25,020
translation into that domain
but then it goes further,

63
00:02:25,020 --> 00:02:28,110
and it says transformer model is

64
00:02:28,110 --> 00:02:29,415
a mountain bike ride

65
00:02:29,415 --> 00:02:31,480
and the trail map is
the input sequence,

66
00:02:31,480 --> 00:02:34,740
and it's now describing it
using concepts from that.

67
00:02:34,740 --> 00:02:36,780
It now goes on and says

68
00:02:36,780 --> 00:02:39,300
other things if I keep
prompting it but the key

69
00:02:39,300 --> 00:02:44,000
is that it is beginning to
draw from my experience.

70
00:02:44,000 --> 00:02:45,720
I'm telling it about me

71
00:02:45,720 --> 00:02:47,860
and what I understand
and what I know and what

72
00:02:47,860 --> 00:02:50,760
I like with the hope
that it can draw

73
00:02:50,760 --> 00:02:55,940
metaphors or
analogies or examples

74
00:02:55,940 --> 00:02:57,900
that will better
connect with the things

75
00:02:57,900 --> 00:02:59,790
that I understand and it's doing

76
00:02:59,790 --> 00:03:01,510
this through translation
because we're

77
00:03:01,510 --> 00:03:03,610
telling it that the rocks
that you're going to

78
00:03:03,610 --> 00:03:05,250
work with to build
these metaphors

79
00:03:05,250 --> 00:03:06,950
and analogies to communicate

80
00:03:06,950 --> 00:03:09,350
these ideas are going to be

81
00:03:09,350 --> 00:03:12,450
drawn from the information
that I know about,

82
00:03:12,450 --> 00:03:14,050
the things that I understand.

83
00:03:14,050 --> 00:03:15,750
I want you to use rocks that

84
00:03:15,750 --> 00:03:18,050
I understand as your
building blocks.

85
00:03:18,050 --> 00:03:20,690
Now, what if we want
to go and think

86
00:03:20,690 --> 00:03:22,490
about how do we

87
00:03:22,490 --> 00:03:25,370
go and explain something
for somebody else?

88
00:03:25,370 --> 00:03:29,570
Or what if we don't necessarily
know what a good way of

89
00:03:29,570 --> 00:03:31,330
describing it is about we

90
00:03:31,330 --> 00:03:33,370
don't know exactly how
to describe ourselves,

91
00:03:33,370 --> 00:03:36,630
or we're trying to think
about a classroom?

92
00:03:36,630 --> 00:03:38,210
Well, we can use what's called

93
00:03:38,210 --> 00:03:40,235
the audience persona pattern.

94
00:03:40,235 --> 00:03:42,390
The idea behind this pattern is,

95
00:03:42,390 --> 00:03:44,570
we want to explain some topic,

96
00:03:44,570 --> 00:03:46,250
but we want to explain it to

97
00:03:46,250 --> 00:03:48,650
a particular audience
and we're going to

98
00:03:48,650 --> 00:03:51,550
combine this with
that metaphor pattern

99
00:03:51,550 --> 00:03:53,990
to do something really powerful.

100
00:03:54,050 --> 00:03:56,210
This is the basic pattern.

101
00:03:56,210 --> 00:03:57,830
We say, explain some topic

102
00:03:57,830 --> 00:04:00,270
to a professor in
computer science,

103
00:04:00,270 --> 00:04:04,335
explain this topic
to an auto mechanic,

104
00:04:04,335 --> 00:04:06,405
explain this topic to

105
00:04:06,405 --> 00:04:09,750
a speech-language pathologist
and we're going to

106
00:04:09,750 --> 00:04:12,090
then modify it a little bit by

107
00:04:12,090 --> 00:04:15,840
saying using analogies
and metaphors.

108
00:04:15,840 --> 00:04:17,650
What we're going to
say is explain how

109
00:04:17,650 --> 00:04:20,010
the transformer model
works to a fifth grader,

110
00:04:20,010 --> 00:04:22,170
which is our audience, using

111
00:04:22,170 --> 00:04:24,050
analogies and metaphors that

112
00:04:24,050 --> 00:04:26,020
will make sense to the audience.

113
00:04:26,020 --> 00:04:30,105
Now, what happens? It
comes back, and it says,

114
00:04:30,105 --> 00:04:32,070
imagine a giant library,

115
00:04:32,070 --> 00:04:35,110
and bookshelves as
layers, books as words.

116
00:04:35,110 --> 00:04:39,170
You notice we're getting
common concepts described,

117
00:04:39,170 --> 00:04:40,490
but they're being translated

118
00:04:40,490 --> 00:04:42,590
different ways depending
on the audience.

119
00:04:42,590 --> 00:04:44,590
They're being translated
into different metaphors and

120
00:04:44,590 --> 00:04:47,435
analogies that makes
sense to the audience.

121
00:04:47,435 --> 00:04:49,550
We started off at the
very beginning giving it

122
00:04:49,550 --> 00:04:52,130
very specific things
that we wanted to use.

123
00:04:52,130 --> 00:04:55,025
The rocks were very
closely constrained,

124
00:04:55,025 --> 00:04:57,110
it didn't have
choice it had to use

125
00:04:57,110 --> 00:05:00,230
an engine and car analogy
and we gradually worked

126
00:05:00,230 --> 00:05:03,130
our way to describing

127
00:05:03,130 --> 00:05:05,070
the things that we know about

128
00:05:05,070 --> 00:05:06,570
personally and like personally,

129
00:05:06,570 --> 00:05:08,010
and then drawing metaphors and

130
00:05:08,010 --> 00:05:09,910
analogies from that
and now we're actually

131
00:05:09,910 --> 00:05:12,310
looking at how to draw
metaphors analogies that

132
00:05:12,310 --> 00:05:13,410
will make sense to a particular

133
00:05:13,410 --> 00:05:15,150
audience like a fifth grader.

134
00:05:15,150 --> 00:05:18,420
Then comes back with
playing with Lego blocks,

135
00:05:18,420 --> 00:05:22,790
and then detective
solving a mystery.

136
00:05:22,790 --> 00:05:25,650
Now, the key behind
this is that it's all

137
00:05:25,650 --> 00:05:29,290
built on this wider
understanding of translation.

138
00:05:29,290 --> 00:05:31,745
The translation isn't
just word for word,

139
00:05:31,745 --> 00:05:34,540
translation but it
is the concept of

140
00:05:34,540 --> 00:05:36,940
converting something
to representing

141
00:05:36,940 --> 00:05:38,540
meaning in a new form.

142
00:05:38,540 --> 00:05:41,660
Just like it took my
name is Jules White,

143
00:05:41,660 --> 00:05:45,840
and it translated that into
a sequence of rocks stacked.

144
00:05:45,840 --> 00:05:47,520
We're doing the same
thing, but we're

145
00:05:47,520 --> 00:05:49,240
using metaphors and analogies

146
00:05:49,240 --> 00:05:50,740
from a particular domain to

147
00:05:50,740 --> 00:05:53,060
become our rocks that
we're having a stack with,

148
00:05:53,060 --> 00:05:56,415
and that we're using it
to represent meaning

149
00:05:56,415 --> 00:06:00,460
and then if we draw the right
metaphors and analogies,

150
00:06:00,460 --> 00:06:03,300
then we can get it to explain
things in a way that help

151
00:06:03,300 --> 00:06:07,200
us learn and help us to
understand concepts better.