#!/bin/bash

echo "========================================"
echo "SRT字幕翻译工具安装和运行脚本"
echo "========================================"
echo

echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请先安装Python 3.6+"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "Python环境检查通过"
echo

echo "正在安装必要的依赖包..."
echo "安装googletrans翻译库..."
$PYTHON_CMD -m pip install googletrans==4.0.0rc1

echo "安装requests库..."
$PYTHON_CMD -m pip install requests

echo
echo "依赖安装完成！"
echo

echo "开始翻译所有英文SRT文件..."
$PYTHON_CMD translate_srt_offline.py --all

echo
echo "========================================"
echo "翻译完成！"
echo "中文字幕文件已生成，文件名格式为: *.zh.srt"
echo "========================================"
