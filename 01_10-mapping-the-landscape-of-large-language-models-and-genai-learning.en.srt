1
00:00:00,240 --> 00:00:05,190
And we're back in this video,
I'm going to show you how you

2
00:00:05,190 --> 00:00:10,437
can think about the bigger picture
of large language models and

3
00:00:10,437 --> 00:00:13,950
generative AI in your learning.

4
00:00:13,950 --> 00:00:15,521
If you'll remember,

5
00:00:15,521 --> 00:00:21,230
we had talked about some of the different
foundational models earlier.

6
00:00:21,230 --> 00:00:26,736
Now I'll focus in on foundational
models from the west.

7
00:00:26,736 --> 00:00:31,845
Google's Gemini, incidentally,
is interesting because Google

8
00:00:31,845 --> 00:00:37,190
has access to all these YouTube videos for
training its engine.

9
00:00:37,190 --> 00:00:42,924
But anyway, you can see that
these different foundational

10
00:00:42,924 --> 00:00:48,070
models have a lot of
apps that grow from them.

11
00:00:48,070 --> 00:00:52,852
API, incidentally,
is a common term that stands for

12
00:00:52,852 --> 00:00:56,950
application programming interface.

13
00:00:56,950 --> 00:01:01,878
In the computer world,
an API is a set of protocols,

14
00:01:01,878 --> 00:01:08,190
routines and tools for
building software applications.

15
00:01:08,190 --> 00:01:14,396
It specifies how software components
should interact with each other,

16
00:01:14,396 --> 00:01:21,600
allowing different programs or
systems to communicate and share data.

17
00:01:21,600 --> 00:01:26,460
Through the use of APIs,
we have apps and tools for

18
00:01:26,460 --> 00:01:30,628
specific functions many
of the new apps and

19
00:01:30,628 --> 00:01:34,333
tools that have arisen have grown from

20
00:01:34,333 --> 00:01:39,760
ChatGPT because it's been
first out of the gate.

21
00:01:39,760 --> 00:01:46,220
Let's take a quick overview at just
a few of the currently available tools.

22
00:01:47,450 --> 00:01:52,081
Here you see some of
the top writing assistants.

23
00:01:52,081 --> 00:01:59,147
There's Jennie and there's Grammarly,
which is pretty familiar program for

24
00:01:59,147 --> 00:02:04,810
many, but there's many more
like Writesonic and Jasper.

25
00:02:04,810 --> 00:02:08,618
These tools can really help you write.

26
00:02:08,618 --> 00:02:14,139
It's worth your time to go and
look at some of their free access versions

27
00:02:14,139 --> 00:02:20,137
to see what approaches they use that can
help you write all sorts of material.

28
00:02:20,137 --> 00:02:24,977
From podcast scripts to
blog posts to essays and

29
00:02:24,977 --> 00:02:28,990
much more and much more easily.

30
00:02:28,990 --> 00:02:34,990
When it comes to research,
there's a boatload of tools available.

31
00:02:34,990 --> 00:02:41,770
These tools are often trained on
research rather than the web as a whole.

32
00:02:41,770 --> 00:02:47,666
The web is huge, but
it also has a lot of garbage out there.

33
00:02:47,666 --> 00:02:54,158
There are satire and parody websites
which I personally kind of enjoy.

34
00:02:54,158 --> 00:02:59,684
But there's also any number of just
really bizarre things out on the web,

35
00:02:59,684 --> 00:03:06,570
and many large foundational models can't
help but include them in their training.

36
00:03:06,570 --> 00:03:10,120
But research models can be different.

37
00:03:10,120 --> 00:03:19,290
For example, semantic scholar is trained
on over 200 million research papers.

38
00:03:19,290 --> 00:03:23,660
Then there are direct
live to Internet tools,

39
00:03:23,660 --> 00:03:28,712
including copilot,
which you may be familiar with.

40
00:03:28,712 --> 00:03:35,010
There are also PowerPoint generators, but
I'd say to be a little careful with them.

41
00:03:35,010 --> 00:03:39,713
In my experience,
they often result in bland presentations

42
00:03:39,713 --> 00:03:44,080
with lots of bullets and
generic looking pictures.

43
00:03:44,080 --> 00:03:48,020
Also, beware of what are called wrappers.

44
00:03:49,080 --> 00:03:52,698
These aren't wrappers as in the singers,
but

45
00:03:52,698 --> 00:03:56,312
wrappers like a wrapping around a package.

46
00:03:56,312 --> 00:04:00,511
Some of these websites involve
simplistic wrappers that wrap

47
00:04:00,511 --> 00:04:02,698
themselves around the engine.

48
00:04:02,698 --> 00:04:07,521
But don't add much value beyond what
you could do yourself by simply

49
00:04:07,521 --> 00:04:11,120
prompting your engine about what you want.

50
00:04:11,120 --> 00:04:16,120
So you want to play with the free
versions of some of these apps

51
00:04:16,120 --> 00:04:20,940
to see if they are genuinely
worth the subscription fees.

52
00:04:22,160 --> 00:04:25,640
There are tools for
teachers and instructors.

53
00:04:25,640 --> 00:04:30,850
These include magic school,
teach mate and so forth.

54
00:04:30,850 --> 00:04:33,034
Need to write a lesson plan?

55
00:04:33,034 --> 00:04:36,530
These sites can make it a snap.

56
00:04:36,530 --> 00:04:43,330
Then there are creative productivity
platforms like Canva and Gamma.

57
00:04:43,330 --> 00:04:49,360
As far as coding tools,
there are various options available and

58
00:04:49,360 --> 00:04:54,280
when it comes to imagery,
there are many tools.

59
00:04:54,280 --> 00:04:59,247
People are often aware of DALL-E,
which grows from ChatGPT,

60
00:04:59,247 --> 00:05:04,028
but there's also imagery arising
from independent sources

61
00:05:04,028 --> 00:05:07,888
like mid Journey,
Dream Studio and so forth.

62
00:05:07,888 --> 00:05:13,179
Likewise, there's Runway
growing from Gemini,

63
00:05:13,179 --> 00:05:17,768
and there are lots and
lots of video tools.

64
00:05:17,768 --> 00:05:23,065
This is really growing fast and comes
from a number of different sources and

65
00:05:23,065 --> 00:05:25,260
also independent platforms.

66
00:05:26,560 --> 00:05:30,587
All of this provides just
a little hint of some of

67
00:05:30,587 --> 00:05:34,440
the different tools that are out there.

68
00:05:34,440 --> 00:05:38,448
The way I'm presenting
things here grew from

69
00:05:38,448 --> 00:05:43,472
a beautiful description in
the book teaching with AI.

70
00:05:43,472 --> 00:05:49,400
It's a wonderful book by Jose Bowen and
C Edward Watson.

71
00:05:49,400 --> 00:05:50,944
I love this book.

72
00:05:50,944 --> 00:05:57,097
It gives you a lot of good ideas as
far as both teaching and learning.

73
00:05:57,097 --> 00:06:02,898
I also want to bring to your attention
ChatGPT and The Future of AI,

74
00:06:02,898 --> 00:06:07,890
which was written by my
colleague Terry Sejnowski.

75
00:06:07,890 --> 00:06:13,940
It's a terrific book that gives
you a nice solid foundational

76
00:06:13,940 --> 00:06:19,530
knowledge of insight into
how generative AI works.

77
00:06:19,530 --> 00:06:26,090
I also love the book Co-Intelligence
by Ethan Mollick, it's a great book.

78
00:06:26,090 --> 00:06:30,463
All of these are practical kinds
of books that give you insights

79
00:06:30,463 --> 00:06:35,170
that you can take away and
use to help enhance your work.

80
00:06:35,170 --> 00:06:40,330
There are also theoretical
books that can be very helpful.

81
00:06:40,330 --> 00:06:47,210
One of my favorites is
The World's I See by Fei-Fei Li.

82
00:06:47,210 --> 00:06:51,935
It talks about how she came up
with some of the ideas that

83
00:06:51,935 --> 00:06:57,250
helped underpin the development
of generative AI.

84
00:06:57,250 --> 00:07:01,930
The book the Alignment Problem
by Brian Christian is

85
00:07:01,930 --> 00:07:07,898
an enlightening book that gives you
a sense of how we really learn.

86
00:07:07,898 --> 00:07:14,110
And lastly, there's a good Worldwide
Overview book of AI by Kai-Fu Lee.

87
00:07:15,330 --> 00:07:20,005
Good courses on generative AI,
of course, include,

88
00:07:20,005 --> 00:07:23,340
well, quite a few from Coursera.

89
00:07:23,340 --> 00:07:28,451
And I also must point to
the inimitable Jules White,

90
00:07:28,451 --> 00:07:31,780
who has created many courses.

91
00:07:31,780 --> 00:07:36,410
This is just a little bite
sized bit of insight into

92
00:07:36,410 --> 00:07:41,260
the many courses that Jules has created.

93
00:07:41,260 --> 00:07:49,700
Now let's turn to Greg Brockman, who's
the co-founder and president of OpenAI.

94
00:07:49,700 --> 00:07:56,760
He famously wrote out on a little notebook
page the Instructions For a Joke Website.

95
00:07:56,760 --> 00:08:00,904
He just took a picture
of his notebook page and

96
00:08:00,904 --> 00:08:04,940
loaded that onto ChatGPT, lol and behold,

97
00:08:04,940 --> 00:08:10,300
what he got was the coding
needed to create a joke website.

98
00:08:11,440 --> 00:08:16,800
I took a picture of Brockman's picture and
did this myself.

99
00:08:16,800 --> 00:08:22,608
I tried it on ChatGPT, and
ChatGPT was like, sure, here you go.

100
00:08:22,608 --> 00:08:24,980
And it gave me the coding.

101
00:08:24,980 --> 00:08:30,628
Then I went to Claude, which is another
of my favorite large language models.

102
00:08:30,628 --> 00:08:34,692
Claude said, I see this joke.

103
00:08:34,692 --> 00:08:38,380
You want me to create a joke website?

104
00:08:38,380 --> 00:08:40,943
I am not going to do it,
because guess what?

105
00:08:40,943 --> 00:08:44,980
You could hurt people's feelings
with this joke website.

106
00:08:44,980 --> 00:08:49,968
I had to argue with Claude to
get it to finally admit that

107
00:08:49,968 --> 00:08:54,956
we did not want to have
a completely humorless world and

108
00:08:54,956 --> 00:08:59,416
that it could be valuable
to have a joke website.

109
00:08:59,416 --> 00:09:03,330
This brings me to an important idea.

110
00:09:03,330 --> 00:09:09,669
Each generative AI platform has its
own individual characteristics,

111
00:09:09,669 --> 00:09:13,778
the model architecture that you are using.

112
00:09:13,778 --> 00:09:18,911
Incidentally, I should note
that the GPT in GPT-4,

113
00:09:18,911 --> 00:09:25,344
for example, stands for
generative pre-trained transformer.

114
00:09:25,344 --> 00:09:30,456
It can be closed,
meaning we don't know what the weights

115
00:09:30,456 --> 00:09:36,600
of those neural nets underlying
the fundamental model are.

116
00:09:36,600 --> 00:09:42,334
There are actually models where we do
know what the weights are like Llama.

117
00:09:42,334 --> 00:09:47,440
These are called open, or
at least relatively open engines.

118
00:09:48,460 --> 00:09:56,340
The model architecture between different
engines can be really quite different.

119
00:09:56,340 --> 00:09:59,404
The training used can be different.

120
00:09:59,404 --> 00:10:04,168
ChatGPT trained on the massive Internet,
for example,

121
00:10:04,168 --> 00:10:11,270
while semantic scholar, as I mentioned
earlier, trained on research papers.

122
00:10:11,270 --> 00:10:16,101
The training data used can actually
make a difference in what you

123
00:10:16,101 --> 00:10:21,565
experience when you go to some of
these different foundational models.

124
00:10:21,565 --> 00:10:23,890
Tuning can be different.

125
00:10:23,890 --> 00:10:28,118
It can be optimized for,
perhaps conversational

126
00:10:28,118 --> 00:10:32,870
responses versus in depth
research responses.

127
00:10:32,870 --> 00:10:37,400
These engines can also have
different personalities.

128
00:10:37,400 --> 00:10:44,320
Some of them are very formal and
informative, others are friendlier.

129
00:10:44,320 --> 00:10:50,343
I suppose this is why when I'm
arguing with a generative AI engine,

130
00:10:50,343 --> 00:10:57,464
I can find that they can respond quite
differently with all of these insights.

131
00:10:57,464 --> 00:11:03,808
You've got a little bit of a foundation
now yourself to move forward, but

132
00:11:03,808 --> 00:11:10,070
you'll do even better as Jules brings
these ideas even further to life.