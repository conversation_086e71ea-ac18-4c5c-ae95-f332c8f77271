1
00:00:00,000 --> 00:00:03,220
In this video, we're
going to be talking

2
00:00:03,220 --> 00:00:06,980
about large language
models in the brain.

3
00:00:06,980 --> 00:00:09,640
If we look, there are

4
00:00:09,640 --> 00:00:15,620
seven international giants
with large language models.

5
00:00:15,620 --> 00:00:17,880
You may wonder, well,

6
00:00:17,880 --> 00:00:20,600
what's a large language model?

7
00:00:20,600 --> 00:00:22,800
We're going to get to that soon

8
00:00:22,800 --> 00:00:28,055
but again, these giants can
be thought of as engines.

9
00:00:28,055 --> 00:00:31,510
You might first bring
to mind some of

10
00:00:31,510 --> 00:00:34,330
the major Internet-based
companies

11
00:00:34,330 --> 00:00:36,690
that are growing
out of the West,

12
00:00:36,690 --> 00:00:42,470
Google, Amazon, Microsoft,
Facebook, or Meta.

13
00:00:42,470 --> 00:00:45,870
Each of these
companies has or is

14
00:00:45,870 --> 00:00:49,750
affiliated with their
own foundational models,

15
00:00:49,750 --> 00:00:52,090
which are really expensive,

16
00:00:52,090 --> 00:00:54,010
of course, to produce.

17
00:00:54,010 --> 00:00:58,320
So Gemini belongs to Google.

18
00:00:58,320 --> 00:01:02,090
Anthropic produces Claude, one

19
00:01:02,090 --> 00:01:06,290
of my favorites and is
affiliated with Amazon.

20
00:01:06,290 --> 00:01:10,220
Microsoft is affiliated
with OpenAI.

21
00:01:10,220 --> 00:01:12,930
Meta is with LLaMA

22
00:01:12,930 --> 00:01:15,270
but there are, of course,

23
00:01:15,270 --> 00:01:19,520
other foundational
models around the world.

24
00:01:19,520 --> 00:01:22,890
Baidu, Alibaba, and Tencent

25
00:01:22,890 --> 00:01:25,530
are some of the
largest in China,

26
00:01:25,530 --> 00:01:29,110
which also has models
that are relatively

27
00:01:29,110 --> 00:01:33,635
comparable to the large
language models in the West.

28
00:01:33,635 --> 00:01:38,200
I'll just give you a sense
of the amount of input.

29
00:01:38,200 --> 00:01:42,600
In other words, the
number of input tokens,

30
00:01:42,600 --> 00:01:45,940
a foundational
model can take in.

31
00:01:45,940 --> 00:01:49,690
This is called the
context window,

32
00:01:49,690 --> 00:01:54,580
at present, and this will
be changing dramatically.

33
00:01:54,580 --> 00:01:56,840
You can get a sense of

34
00:01:56,840 --> 00:02:00,020
how big or small
the context windows

35
00:02:00,020 --> 00:02:02,710
in the various engines can be.

36
00:02:02,710 --> 00:02:08,435
Tokens are pretty close to
being the same thing as words.

37
00:02:08,435 --> 00:02:10,720
You can think of the size of

38
00:02:10,720 --> 00:02:13,700
the context windows as

39
00:02:13,700 --> 00:02:16,580
how many words at a time can

40
00:02:16,580 --> 00:02:20,765
this particular
foundational model take in.

41
00:02:20,765 --> 00:02:22,980
Metaphorically speaking,

42
00:02:22,980 --> 00:02:24,920
how much gas can

43
00:02:24,920 --> 00:02:28,700
these different engines
guzzle at a time?

44
00:02:28,700 --> 00:02:31,190
Let me give you an example of

45
00:02:31,190 --> 00:02:34,290
how context window
can be important.

46
00:02:34,290 --> 00:02:37,930
There's basically a limit
on how many tokens,

47
00:02:37,930 --> 00:02:41,330
which are basically a
bit shorter than words

48
00:02:41,330 --> 00:02:45,810
that the transformers'
attention can focus on at once.

49
00:02:45,810 --> 00:02:50,930
If you put a PDF that has
roughly 50,000 words,

50
00:02:50,930 --> 00:02:56,050
but the context window of the
engine is only 30,000 words

51
00:02:56,050 --> 00:03:01,800
or 40 or so thousand
tokens, you're in trouble.

52
00:03:01,800 --> 00:03:06,110
But it's not just how many
words you can put in.

53
00:03:06,110 --> 00:03:11,550
It's also how many words the
decoder is spitting out.

54
00:03:11,550 --> 00:03:16,850
If both input and output
are too big, watch out.

55
00:03:16,850 --> 00:03:20,850
The large language model
will stop working properly.

56
00:03:20,850 --> 00:03:25,930
It can't remember information
outside the context window.

57
00:03:25,930 --> 00:03:28,190
How is this important?

58
00:03:28,190 --> 00:03:30,550
Let me give you an example.

59
00:03:30,550 --> 00:03:33,460
I was asked by the
Japanese publisher

60
00:03:33,460 --> 00:03:35,550
of my co-authors and my book

61
00:03:35,550 --> 00:03:38,340
Uncommon Sense Teaching to write

62
00:03:38,340 --> 00:03:41,860
the foreword for the Japanese
edition of the book.

63
00:03:41,860 --> 00:03:44,120
I didn't know anything about

64
00:03:44,120 --> 00:03:48,860
the Japanese educational
system. What could I do?

65
00:03:48,860 --> 00:03:52,540
Well, Claude had a big
enough context window

66
00:03:52,540 --> 00:03:54,920
at the time that I could feed in

67
00:03:54,920 --> 00:03:58,100
a PDF of my entire book and

68
00:03:58,100 --> 00:04:02,630
still have room left for
Claude to give a response.

69
00:04:02,630 --> 00:04:05,360
I told Claude it was

70
00:04:05,360 --> 00:04:10,025
the leading expert on the
Japanese educational system.

71
00:04:10,025 --> 00:04:13,050
It's always good to tell
your engine to become

72
00:04:13,050 --> 00:04:17,230
an expert in whatever subject
is involved in your query.

73
00:04:17,230 --> 00:04:20,470
Then I uploaded a PDF of

74
00:04:20,470 --> 00:04:22,730
my entire book and asked

75
00:04:22,730 --> 00:04:25,130
it to analyze the
book and tell me how

76
00:04:25,130 --> 00:04:28,610
it supported the Japanese
educational system

77
00:04:28,610 --> 00:04:30,890
and how it provided

78
00:04:30,890 --> 00:04:34,350
novel yet well-grounded
insights from

79
00:04:34,350 --> 00:04:36,390
neuroscience that were currently

80
00:04:36,390 --> 00:04:39,025
being implemented in Japan.

81
00:04:39,025 --> 00:04:42,780
Claude thought for a few
seconds and then gave

82
00:04:42,780 --> 00:04:44,360
me everything I needed to

83
00:04:44,360 --> 00:04:46,560
write a good forward
for the book.

84
00:04:46,560 --> 00:04:49,380
Of course, I checked it
with friends who are

85
00:04:49,380 --> 00:04:53,660
genuine experts in the
Japanese education system.

86
00:04:53,660 --> 00:04:58,420
The result, my friends
and my editor were like,

87
00:04:58,420 --> 00:05:01,060
how do you know so much

88
00:05:01,060 --> 00:05:04,380
about the Japanese
educational system?

89
00:05:04,380 --> 00:05:08,700
All of this was possible because
the engine I was working

90
00:05:08,700 --> 00:05:10,520
with was big enough to take

91
00:05:10,520 --> 00:05:13,140
a whole book at one
time to analyze,

92
00:05:13,140 --> 00:05:15,760
even as it was also able to

93
00:05:15,760 --> 00:05:19,125
give me back the
information I needed.

94
00:05:19,125 --> 00:05:21,460
Now, there were

95
00:05:21,460 --> 00:05:28,640
149 foundational models
released in 2023 alone.

96
00:05:28,640 --> 00:05:32,680
If you're overwhelmed by
the many different models

97
00:05:32,680 --> 00:05:37,080
that are coming out, well,
that's understandable

98
00:05:37,080 --> 00:05:39,700
but keep in mind, they're just

99
00:05:39,700 --> 00:05:43,940
car engines, like them anyway.

100
00:05:43,940 --> 00:05:46,900
You're learning to
drive different cars.

101
00:05:46,900 --> 00:05:49,830
It's relatively straightforward

102
00:05:49,830 --> 00:05:53,000
but let's look a little deeper.

103
00:05:53,000 --> 00:05:56,960
We can analyze large
language models and get

104
00:05:56,960 --> 00:05:59,280
an understanding of
what they really

105
00:05:59,280 --> 00:06:02,840
are by looking at
the human brain.

106
00:06:02,840 --> 00:06:05,380
Previously, I had said,

107
00:06:05,380 --> 00:06:08,220
we remember because we have sets

108
00:06:08,220 --> 00:06:11,580
of links that we put
in long-term memory

109
00:06:11,580 --> 00:06:14,800
but one thing I
didn't say was that

110
00:06:14,800 --> 00:06:18,210
there are two pathways,

111
00:06:18,210 --> 00:06:21,190
two ways that those neural links

112
00:06:21,190 --> 00:06:24,355
can be laid into
long-term memory.

113
00:06:24,355 --> 00:06:26,550
You don't need to remember

114
00:06:26,550 --> 00:06:29,465
the names of these
pathways or anything.

115
00:06:29,465 --> 00:06:32,070
I just want to give
you a little sense

116
00:06:32,070 --> 00:06:33,890
that our working memory,

117
00:06:33,890 --> 00:06:38,590
which contains what we're
temporarily holding in mind,

118
00:06:38,590 --> 00:06:41,450
can take in a little bit of

119
00:06:41,450 --> 00:06:45,855
information that it receives
from the outside world.

120
00:06:45,855 --> 00:06:50,680
It then takes those bits
of information and sends

121
00:06:50,680 --> 00:06:55,480
it through the hippocampus
into long-term memory

122
00:06:55,480 --> 00:06:57,980
but there's another way we can

123
00:06:57,980 --> 00:07:00,740
put information into
long-term memory,

124
00:07:00,740 --> 00:07:02,700
and that is to go through

125
00:07:02,700 --> 00:07:06,720
that automatic basal
ganglia system.

126
00:07:06,720 --> 00:07:10,960
These are two very
different ways of learning.

127
00:07:10,960 --> 00:07:14,640
One is more logical,
step by step.

128
00:07:14,640 --> 00:07:17,800
The other is more experiential

129
00:07:17,800 --> 00:07:20,960
but we need both in order to be

130
00:07:20,960 --> 00:07:24,885
able to learn and do
things effectively.

131
00:07:24,885 --> 00:07:29,000
If we look at this
declarative pathway

132
00:07:29,000 --> 00:07:32,470
that I had mentioned that
goes through the hippocampus,

133
00:07:32,470 --> 00:07:37,900
we can model that with
a kind of a blue box,

134
00:07:37,900 --> 00:07:40,720
and that automatic pathway

135
00:07:40,720 --> 00:07:45,210
we can think of it as being
like that orange box.

136
00:07:45,210 --> 00:07:49,550
If we're looking at these
two different boxes,

137
00:07:49,550 --> 00:07:52,810
two different
pathways of learning.

138
00:07:52,810 --> 00:07:54,830
What happens is when

139
00:07:54,830 --> 00:07:57,970
we're thinking with
our working memory,

140
00:07:57,970 --> 00:08:01,390
that little
octopus-looking critter up

141
00:08:01,390 --> 00:08:05,050
there that will meet in
more detail later on,

142
00:08:05,050 --> 00:08:08,870
we are thinking
consciously with it

143
00:08:08,870 --> 00:08:11,310
but what's different is that

144
00:08:11,310 --> 00:08:16,655
the basal ganglia way of
learning is not conscious.

145
00:08:16,655 --> 00:08:21,930
The hippocampus learning is
much more conscious to us.

146
00:08:21,930 --> 00:08:24,710
We're more aware of it.

147
00:08:24,710 --> 00:08:28,270
If we've got a conscious
working memory,

148
00:08:28,270 --> 00:08:31,960
it sends signals through
the hippocampus,

149
00:08:31,960 --> 00:08:35,390
when we're thinking
deliberatively and carefully.

150
00:08:35,390 --> 00:08:38,250
Let's say we're working
on a math problem.

151
00:08:38,250 --> 00:08:40,330
We're thinking consciously about

152
00:08:40,330 --> 00:08:42,970
how we solve that math problem.

153
00:08:42,970 --> 00:08:49,650
This deliberative thinking
that's very conscious is quite

154
00:08:49,650 --> 00:08:54,050
different from the
non-conscious basal ganglia

155
00:08:54,050 --> 00:08:56,590
automatic way of thinking.

156
00:08:56,590 --> 00:09:01,310
To understand this more
automatic way of thinking,

157
00:09:01,310 --> 00:09:04,485
imagine trying to
hit a baseball.

158
00:09:04,485 --> 00:09:07,400
Your conscious working
memory sends out

159
00:09:07,400 --> 00:09:10,280
a signal to the basal
ganglia that says,

160
00:09:10,280 --> 00:09:14,025
essentially, buddy,
hit the ball.

161
00:09:14,025 --> 00:09:18,180
Then somehow, you're
not aware of how

162
00:09:18,180 --> 00:09:20,960
your basal ganglia does

163
00:09:20,960 --> 00:09:24,100
something that helps you
try to hit the ball.

164
00:09:24,100 --> 00:09:27,540
You aren't consciously
aware of this process.

165
00:09:27,540 --> 00:09:29,580
All you know is the outcome.

166
00:09:29,580 --> 00:09:33,240
Did you hit the ball or
not? But wait a minute.

167
00:09:33,240 --> 00:09:36,920
There is some learning machine

168
00:09:36,920 --> 00:09:39,240
going on in that
automatic system,

169
00:09:39,240 --> 00:09:41,020
even if we're not aware of it.

170
00:09:41,020 --> 00:09:44,880
What is the magic going on
in the automatic system?

171
00:09:44,880 --> 00:09:47,520
The magic, as it turns out,

172
00:09:47,520 --> 00:09:50,180
is a deep neural network.

173
00:09:50,180 --> 00:09:53,100
The little circles are neurons,

174
00:09:53,100 --> 00:09:56,740
and the lines between
them are the axons,

175
00:09:56,740 --> 00:10:02,200
dendrites, and dendritic spines
connecting those neurons.

176
00:10:02,200 --> 00:10:05,020
This is a so called
deep network,

177
00:10:05,020 --> 00:10:08,460
because it has many
layers of neurons that

178
00:10:08,460 --> 00:10:12,260
send signals back and
forth between the layers.

179
00:10:12,260 --> 00:10:14,840
Each time you try
to hit the ball,

180
00:10:14,840 --> 00:10:17,880
you send a signal
through the network.

181
00:10:17,880 --> 00:10:21,380
You gradually learn what
movements help you hit

182
00:10:21,380 --> 00:10:25,420
the ball and which movements
make you miss the ball.

183
00:10:25,420 --> 00:10:28,380
Your continued practice,
day after day,

184
00:10:28,380 --> 00:10:30,860
trying to hit the
ball, gradually

185
00:10:30,860 --> 00:10:32,780
trains your neural network.

186
00:10:32,780 --> 00:10:36,290
Some connections
strengthen, some weaken,

187
00:10:36,290 --> 00:10:38,260
but you're not conscious

188
00:10:38,260 --> 00:10:40,800
of how that learning
takes place.

189
00:10:40,800 --> 00:10:43,780
It's not only physical skills

190
00:10:43,780 --> 00:10:47,480
that are trained in
the automatic system.

191
00:10:47,480 --> 00:10:51,620
When you practice lots of
math problems, for example,

192
00:10:51,620 --> 00:10:54,040
you gradually learn through

193
00:10:54,040 --> 00:10:58,180
both your declarative and
your automatic systems.

194
00:10:58,180 --> 00:11:00,560
Your automatic system helps

195
00:11:00,560 --> 00:11:03,320
develop your
mathematical intuition.

196
00:11:03,320 --> 00:11:05,460
When you were a toddler,

197
00:11:05,460 --> 00:11:08,120
you learned your native language

198
00:11:08,120 --> 00:11:10,560
using this automatic system.

199
00:11:10,560 --> 00:11:15,010
That's why you can speak it
so easily and intuitively.

200
00:11:15,010 --> 00:11:17,460
The automatic system becomes

201
00:11:17,460 --> 00:11:20,140
less powerful as we grow older,

202
00:11:20,140 --> 00:11:22,560
which may be why
it becomes harder

203
00:11:22,560 --> 00:11:25,220
to learn a foreign
language as adults

204
00:11:25,220 --> 00:11:27,520
but this automatic system,

205
00:11:27,520 --> 00:11:32,640
even in adults, allows us
to learn experientially.

206
00:11:32,640 --> 00:11:34,600
When we're learning on the job

207
00:11:34,600 --> 00:11:36,640
or in a laboratory setting,

208
00:11:36,640 --> 00:11:38,740
we're often doing our learning

209
00:11:38,740 --> 00:11:40,680
not only through that conscious,

210
00:11:40,680 --> 00:11:43,660
deliberate hippocampus
pathway but

211
00:11:43,660 --> 00:11:47,480
also through the unconscious
automatic pathway.

212
00:11:47,480 --> 00:11:51,040
Again, as we're
learning something,

213
00:11:51,040 --> 00:11:53,250
experiencing something.

214
00:11:53,250 --> 00:11:56,840
What happens is our neural net,

215
00:11:56,840 --> 00:11:59,520
those connected web of

216
00:11:59,520 --> 00:12:03,225
neurons are making
these connections.

217
00:12:03,225 --> 00:12:06,840
Over a number of
learning experiences,

218
00:12:06,840 --> 00:12:11,320
you learn, this is
good. This is wrong.

219
00:12:11,320 --> 00:12:15,740
You form values which
are subliminally

220
00:12:15,740 --> 00:12:21,090
input into your working
memory that's conscious.

221
00:12:21,090 --> 00:12:23,640
As you're learning something,

222
00:12:23,640 --> 00:12:28,665
you may strengthen some
connections and weaken others.

223
00:12:28,665 --> 00:12:30,930
This, as it turns out,

224
00:12:30,930 --> 00:12:34,730
is exceptionally similar to

225
00:12:34,730 --> 00:12:38,150
how large language models work.

226
00:12:38,150 --> 00:12:40,810
In fact, what's going on in

227
00:12:40,810 --> 00:12:43,470
the human brain and
what's going on in

228
00:12:43,470 --> 00:12:46,570
artificial
intelligence have each

229
00:12:46,570 --> 00:12:50,545
informed researchers
in each field.

230
00:12:50,545 --> 00:12:55,040
Many researchers in neuroscience
are also prominent in

231
00:12:55,040 --> 00:12:59,605
AI and in generative
AI, and vice versa.

232
00:12:59,605 --> 00:13:02,960
These two fields intertwine,

233
00:13:02,960 --> 00:13:05,520
and many of the
advances that have

234
00:13:05,520 --> 00:13:08,400
occurred have occurred precisely

235
00:13:08,400 --> 00:13:11,360
because of the new
understanding that

236
00:13:11,360 --> 00:13:15,705
grew from understanding
how the brain works.

237
00:13:15,705 --> 00:13:18,670
From our perspective, it's

238
00:13:18,670 --> 00:13:22,350
quite interesting to know
that within our brain,

239
00:13:22,350 --> 00:13:27,475
we have something like
a generative AI model.

240
00:13:27,475 --> 00:13:31,990
It's this nicely
trained neural net.

241
00:13:31,990 --> 00:13:35,790
What can happen
when we're using AI

242
00:13:35,790 --> 00:13:40,510
is that we can sometimes
notice something really weird,

243
00:13:40,510 --> 00:13:46,780
and that is that AI confabulates
or hallucinates things.

244
00:13:46,780 --> 00:13:50,170
Guess what? AI does not

245
00:13:50,170 --> 00:13:55,190
have this conscious
hippocampal way of learning.

246
00:13:55,190 --> 00:14:00,430
This confabulation of
AI is surprisingly

247
00:14:00,430 --> 00:14:03,330
similar to the hallucination

248
00:14:03,330 --> 00:14:07,410
or confabulation that
we see in human brains.

249
00:14:07,410 --> 00:14:09,890
It's simply like
a human they got

250
00:14:09,890 --> 00:14:12,430
their prefrontal
cortex knocked out,

251
00:14:12,430 --> 00:14:14,310
and so they can't think

252
00:14:14,310 --> 00:14:17,850
consciously about
what's going on.

253
00:14:17,850 --> 00:14:22,570
This confabulation or
a hallucination is

254
00:14:22,570 --> 00:14:28,210
responsible for some really
odd outputs that occur in AI.

255
00:14:28,210 --> 00:14:30,470
You may ask a question and get

256
00:14:30,470 --> 00:14:33,550
some very unusual unexpected

257
00:14:33,550 --> 00:14:37,290
or just plain bizarre answers

258
00:14:37,290 --> 00:14:40,430
but AI has more problems,

259
00:14:40,430 --> 00:14:43,290
of course, than
just hallucination.

260
00:14:43,290 --> 00:14:46,490
We'll cover that coming up.

