1
00:00:00,000 --> 00:00:03,140
你好，芭芭·奥克利，
又在这里和大家见面了。

2
00:00:03,140 --> 00:00:05,400
在这个视频中，我们将

3
00:00:05,400 --> 00:00:08,700
深入探索神经系统的内部，
可以这么说，

4
00:00:08,700 --> 00:00:11,260
来帮助你理解如何

5
00:00:11,260 --> 00:00:14,940
提升你的
生成式AI技能。

6
00:00:14,940 --> 00:00:17,440
在我们进行这个过程时，我们应该

7
00:00:17,440 --> 00:00:20,010
牢记李飞飞的话，

8
00:00:20,010 --> 00:00:23,020
她是AI领域的杰出研究者，

9
00:00:23,020 --> 00:00:26,760
她说："AI不会
取代人类，

10
00:00:26,760 --> 00:00:33,180
但是使用AI的人类会
取代不使用AI的人类。"

11
00:00:33,180 --> 00:00:36,140
我想给你们简单介绍一下

12
00:00:36,140 --> 00:00:37,930
我自己的背景。

13
00:00:37,930 --> 00:00:40,735
我来自奥克兰大学，

14
00:00:40,735 --> 00:00:43,255
人们经常听到奥克兰，

15
00:00:43,255 --> 00:00:44,685
他们会想，嗯，

16
00:00:44,685 --> 00:00:47,700
她一定是来自
加利福尼亚的那个城市。

17
00:00:47,700 --> 00:00:51,420
棕榈树，温暖的天气，美好的事物。

18
00:00:51,420 --> 00:00:56,100
嗯，不，实际上，我来自奥克兰大学，

19
00:00:56,100 --> 00:00:57,530
它位于密歇根州的

20
00:00:57,530 --> 00:00:59,520
罗切斯特，这是我在

21
00:00:59,520 --> 00:01:02,670
典型的二月天
去上班的样子。

22
00:01:02,670 --> 00:01:05,740
我创建了这门《学会如何学习》课程

23
00:01:05,740 --> 00:01:08,565
与特里·塞伊诺夫斯基合作，

24
00:01:08,565 --> 00:01:12,040
他是索尔克研究所的
弗朗西斯·克里克教授，

25
00:01:12,040 --> 00:01:14,500
他同时也是

26
00:01:14,500 --> 00:01:18,525
计算神经科学实验室的主任。

27
00:01:18,525 --> 00:01:22,280
这是特里，当他

28
00:01:22,280 --> 00:01:26,320
在加利福尼亚的日常工作中
稍作休息时。

29
00:01:26,320 --> 00:01:30,155
这是在线教学的
美好之处之一。

30
00:01:30,155 --> 00:01:32,460
不仅学生可以

31
00:01:32,460 --> 00:01:35,260
来自非常不同的地区聚集在一起，

32
00:01:35,260 --> 00:01:37,800
而且教授们也可以来自

33
00:01:37,800 --> 00:01:41,225
非常不同的背景。

34
00:01:41,225 --> 00:01:44,110
有一件事让我感到惊讶，

35
00:01:44,110 --> 00:01:47,850
那就是我被邀请
在哈佛大学演讲。

36
00:01:47,850 --> 00:01:53,130
我相当紧张，
因为那是哈佛。

37
00:01:53,130 --> 00:01:55,770
令我惊讶的是，我走进

38
00:01:55,770 --> 00:01:59,850
房间，发现座无虚席，
只有站着的位置了。

39
00:01:59,850 --> 00:02:01,410
我想，嗯，

40
00:02:01,410 --> 00:02:04,080
为什么这里有
这么多人？

41
00:02:04,080 --> 00:02:07,250
结果发现我们这门小课程

42
00:02:07,250 --> 00:02:09,480
几乎没花什么钱制作，

43
00:02:09,480 --> 00:02:11,330
主要在我的地下室里完成，

44
00:02:11,330 --> 00:02:16,870
学生数量大致相当于

45
00:02:16,870 --> 00:02:20,090
哈佛所有的大规模
开放在线课程

46
00:02:20,090 --> 00:02:23,290
和在线课程的总和，

47
00:02:23,290 --> 00:02:27,930
而那些课程花费了数百万美元
和数百人的努力。

48
00:02:27,930 --> 00:02:30,950
这告诉你的是

49
00:02:30,950 --> 00:02:34,270
人们对我们如何学习
有很大的兴趣，

50
00:02:34,270 --> 00:02:37,470
特别是，对

51
00:02:37,470 --> 00:02:41,350
我们的大脑在学习时
实际如何工作的兴趣。

52
00:02:41,350 --> 00:02:45,610
我们创建了许多课程。

53
00:02:45,610 --> 00:02:49,210
有一些关于教学的
非常好的课程，

54
00:02:49,210 --> 00:02:54,010
儿童学习，以及
改变你的工作或职业。

55
00:02:54,010 --> 00:02:56,870
它们都源于一个基础，

56
00:02:56,870 --> 00:03:00,215
那就是了解大脑
实际如何工作。

57
00:03:00,215 --> 00:03:04,595
事实证明，这与

58
00:03:04,595 --> 00:03:06,570
人工智能世界中

59
00:03:06,570 --> 00:03:09,270
正在发生的事情
密切相关。

60
00:03:09,270 --> 00:03:11,730
让我们看看这是如何运作的。

61
00:03:11,730 --> 00:03:16,900
首先，ChatGPT做什么？

62
00:03:16,900 --> 00:03:20,570
你们中的许多人可能知道
它正在做的是

63
00:03:20,570 --> 00:03:25,010
经常预测句子中的下一个词。

64
00:03:25,010 --> 00:03:26,810
这就是它的训练方式，

65
00:03:26,810 --> 00:03:29,630
或者至少是

66
00:03:29,630 --> 00:03:34,130
基础大语言模型
训练的一个方面。

67
00:03:34,130 --> 00:03:36,530
它还在做的是

68
00:03:36,530 --> 00:03:40,930
将句子中的词汇
相互关联。

69
00:03:40,930 --> 00:03:42,990
如果你想想，

70
00:03:42,990 --> 00:03:45,270
我希望你能原谅我

71
00:03:45,270 --> 00:03:48,590
稍微拟人化一点。

72
00:03:48,590 --> 00:03:53,810
换句话说，将生成式AI
想象成类似人类。

73
00:03:53,810 --> 00:03:56,490
我们知道它也
非常不同。

74
00:03:56,490 --> 00:03:59,930
但如果我们考虑
生成式AI和

75
00:03:59,930 --> 00:04:04,690
它专注于句子的能力，
例如，

76
00:04:04,690 --> 00:04:08,530
这解释了为什么
几年前，

77
00:04:08,530 --> 00:04:11,030
如果你去谷歌或其他

78
00:04:11,030 --> 00:04:13,230
翻译设备或

79
00:04:13,230 --> 00:04:17,150
应用程序，要求它
翻译一个句子，

80
00:04:17,150 --> 00:04:20,330
它会做得很糟糕。

81
00:04:20,330 --> 00:04:24,530
你几乎无法理解
翻译的内容。

82
00:04:24,530 --> 00:04:29,110
这样的原因是
因为算法，

83
00:04:29,110 --> 00:04:32,190
当时计算机的智能

84
00:04:32,190 --> 00:04:37,010
使得它们只能关注，
可以这么说，

85
00:04:37,010 --> 00:04:38,530
它们面前的

86
00:04:38,530 --> 00:04:42,980
一个小方面。

87
00:04:42,980 --> 00:04:47,030
当计算机到达句子末尾时，

88
00:04:47,030 --> 00:04:49,310
它会忘记

89
00:04:49,310 --> 00:04:51,830
句子开头的内容。

90
00:04:51,830 --> 00:04:56,850
但是使用一些创新的
新技术，现在，

91
00:04:56,850 --> 00:05:01,070
计算机不仅可以专注于句子，

92
00:05:01,070 --> 00:05:05,870
还可以专注于整篇文章，
不仅是整篇文章，

93
00:05:05,870 --> 00:05:09,255
实际上还可以专注于整个网络。

94
00:05:09,255 --> 00:05:12,180
它是如何将这些整合在一起的？

95
00:05:12,180 --> 00:05:15,145
它是如何连接事物的？

96
00:05:15,145 --> 00:05:18,580
嗯，有一篇论文发表于

97
00:05:18,580 --> 00:05:23,935
2017年，叫做《注意力就是你所需要的一切》。

98
00:05:23,935 --> 00:05:26,260
这篇论文刚发表时，

99
00:05:26,260 --> 00:05:30,080
嗯，没有人真的
认为它很重要。

100
00:05:30,080 --> 00:05:32,980
事实上，特里·塞伊诺夫斯基是

101
00:05:32,980 --> 00:05:36,955
神经信息处理系统学会的主席，

102
00:05:36,955 --> 00:05:38,965
论文就是在那里发表的。

103
00:05:38,965 --> 00:05:42,380
这个演示基本上被安排在

104
00:05:42,380 --> 00:05:47,990
一个小角落，没有人
太关注。

105
00:05:47,990 --> 00:05:51,990
但现在，人们开始认识到

106
00:05:51,990 --> 00:05:54,970
这一篇论文实际上包含了

107
00:05:54,970 --> 00:05:58,870
如此重要的新概念的种子，

108
00:05:58,870 --> 00:06:04,490
这篇论文已经被引用
超过10万次，

109
00:06:04,490 --> 00:06:09,365
它成为了像Suno这样的
应用的基础，

110
00:06:09,365 --> 00:06:15,530
例如，这是一个
音乐创作应用或工具。

111
00:06:18,830 --> 00:06:21,430
这是如何发生的？

112
00:06:21,430 --> 00:06:24,610
事实证明，这篇论文

113
00:06:24,610 --> 00:06:28,450
发展了变换器的概念。

114
00:06:28,450 --> 00:06:33,465
变换器有两个
主要组件。

115
00:06:33,465 --> 00:06:36,900
一个是编码器。

116
00:06:36,900 --> 00:06:41,210
编码器是我们
接收信息的地方。

117
00:06:41,210 --> 00:06:43,595
例如，一个提示。

118
00:06:43,595 --> 00:06:46,440
然后我们有解码器，

119
00:06:46,440 --> 00:06:50,500
它输出你的
任何输出。

120
00:06:50,500 --> 00:06:55,800
这个编码器解码器
及其信息流，

121
00:06:55,800 --> 00:06:58,660
事实证明，与

122
00:06:58,660 --> 00:07:04,380
人类大脑中的信息流
非常相似。

123
00:07:04,380 --> 00:07:08,940
这就是为什么我们有必要
学习一点

124
00:07:08,940 --> 00:07:11,360
关于大脑的知识，这样我们就可以使用

125
00:07:11,360 --> 00:07:14,160
变换器的一些见解，

126
00:07:14,160 --> 00:07:17,040
大语言模型，以及

127
00:07:17,040 --> 00:07:20,560
像ChatGPT这样的工具
更有效地使用。

128
00:07:20,560 --> 00:07:23,105
将大语言模型想象成

129
00:07:23,105 --> 00:07:26,900
像ChatGPT这样的工具
就像不同的汽车引擎。

130
00:07:26,900 --> 00:07:29,280
一旦你知道如何驾驶，

131
00:07:29,280 --> 00:07:32,300
在汽车之间切换
是轻而易举的，

132
00:07:32,300 --> 00:07:35,380
即使它们在引擎盖下
有不同的引擎，

133
00:07:35,380 --> 00:07:38,965
生成式AI平台也是如此，比如

134
00:07:38,965 --> 00:07:42,920
ChatGPT、Gemini和Claude。

135
00:07:42,920 --> 00:07:45,020
它们只是稍微
不同的模型，

136
00:07:45,020 --> 00:07:46,980
驱动着相似的工具。

137
00:07:46,980 --> 00:07:49,740
掌握了一个，你会发现

138
00:07:49,740 --> 00:07:52,585
其他的也感觉
相当熟悉。

139
00:07:52,585 --> 00:07:54,890
基础保持不变，

140
00:07:54,890 --> 00:07:58,870
即使引擎盖下的细节
有些不同。

141
00:07:58,870 --> 00:08:01,610
现在，让我们转向朱尔斯，

142
00:08:01,610 --> 00:08:04,470
他会给你更多的见解

143
00:08:04,470 --> 00:08:08,790
关于这些非常重要的想法。
