1
00:00:00,520 --> 00:00:05,401
在 这个 视频 , 我们 ' re going 到 是 talking 关于 specific techniques 你

2
00:00:05,401 --> 00:00:08,390
能 使用 到 帮助 advance 你的 学习 .

3
00:00:08,390 --> 00:00:12,070
first off , 我 有 一个 question 为了 你 .

4
00:00:12,070 --> 00:00:16,972
什么 是 这个 最 有效的 技术 你 ' ve found 那个

5
00:00:16,972 --> 00:00:20,110
helps 你 学习 最 efficiently ?

6
00:00:20,110 --> 00:00:25,970
是 它 rereading 或 maybe highlighting 或 underlining ?

7
00:00:25,970 --> 00:00:31,373
是 它 retrieval 练习 或 recall 那个 是 like using 一个 flashcard

8
00:00:31,373 --> 00:00:35,986
到 看 如果 你 知道 什么 ' s 在 这个 其他 side 的 这个 card ?

9
00:00:35,986 --> 00:00:39,666
或 是 它 creating 一个 概念 map ?

10
00:00:39,666 --> 00:00:41,666
哪个 one 是 它 ?

11
00:00:41,666 --> 00:00:46,458
let ' s keep 你 在 suspense 为了 一个 second .

12
00:00:46,458 --> 00:00:50,730
actually , 它 是 retrieval 练习 .

13
00:00:50,730 --> 00:00:57,610
using 一个 flashcard 或 even seeing 如果 something 是 在 你的 own 思维 .

14
00:00:57,610 --> 00:01:02,106
retrieval 练习 是 很多 better than 这个 其他 approaches .

15
00:01:02,106 --> 00:01:08,186
为什么 它 是 helping 你 到 学习 这个 initial material .

16
00:01:08,186 --> 00:01:12,983
你 initially put 那些 weak links 在 long term 记忆

17
00:01:12,983 --> 00:01:17,034
当 你 ' re first 学习 something , 但是

18
00:01:17,034 --> 00:01:22,682
然后 每个 time 你 retrieve 那个 something 从 你的 own 思维 ,

19
00:01:22,682 --> 00:01:28,360
你 是 actually strengthening 那些 sets 的 links .

20
00:01:28,360 --> 00:01:34,360
retrieval 练习 , as 它 turns out , 是 incredibly 强大的 .

21
00:01:34,360 --> 00:01:39,758
如果 你 ' re just reading 一个 page 和 然后 你 reread 它 , actually ,

22
00:01:39,758 --> 00:01:46,336
你的 eyes 是 moving over 这个 page , 但是 你 ' re 不 真的 思考 所以 很多 .

23
00:01:46,336 --> 00:01:49,317
它 ' s 真的 容易的 到 let 你的 eyes 去 over

24
00:01:49,317 --> 00:01:54,440
这个 page without ensuring 你 ' ve got 那个 在 你的 大脑 .

25
00:01:54,440 --> 00:02:00,480
people 将 sometimes 说 , well , 什么 关于 teaching 其他 people ?

26
00:02:00,480 --> 00:02:05,376
那个 ' s actually probably 这个 最 有效的 方式 到 学习 .

27
00:02:05,376 --> 00:02:09,261
和 它 ' s true , teaching others 是 真的 有效的 .

28
00:02:09,261 --> 00:02:14,391
但是 teaching 其他 people 是 actually retrieval 练习 .

29
00:02:14,391 --> 00:02:20,451
你 ' re retrieving 你的 知识 从 你的 own 大脑 , strengthening 那些 links .

30
00:02:20,451 --> 00:02:24,137
那个 ' s 如何 你 学习 effectively .

31
00:02:24,137 --> 00:02:29,645
retrieval 练习 能 是 harder than just rereading something 或

32
00:02:29,645 --> 00:02:32,310
moving 一个 highlighter 在 一个 page .

33
00:02:32,310 --> 00:02:36,376
但是 那个 ' s 什么 ' s 所以 valuable 关于 retrieval .

34
00:02:36,376 --> 00:02:42,320
它 ' s 什么 makes 那些 sets 的 links 真的 solid 和 strong .

35
00:02:42,320 --> 00:02:49,050
这个 有 是 shown 在 hundreds 和 hundreds 的 research papers .

36
00:02:49,050 --> 00:02:53,826
它 ' s 非常 重要的 到 try 到 使用 retrieval 练习 as

37
00:02:53,826 --> 00:02:56,070
很多 as 你 possibly 能 .

38
00:02:57,570 --> 00:03:04,390
所以 let ' s 说 你 有 electronic documents 那个 relate 到 whatever 你 ' re 学习 .

39
00:03:04,390 --> 00:03:11,030
什么 你 want 到 做 是 upload 那些 documents 进入 , 说 , ChatGPT .

40
00:03:12,050 --> 00:03:18,302
as 你 ' re loading things 在 , 你 just 得到 你的 electronic document ,

41
00:03:18,302 --> 00:03:24,445
得到 它 loaded 在 , 和 然后 once 它 ' s 上 , 你 能 ask 生成式 AI 为了

42
00:03:24,445 --> 00:03:28,870
练习 questions , retrieval questions .

43
00:03:28,870 --> 00:03:30,510
但是 那里 ' s 更多 你 能 做 .

44
00:03:30,510 --> 00:03:36,790
和 那个 是 , 为了 例子 , asking 为了 pretesting questions .

45
00:03:36,790 --> 00:03:38,630
什么 做 那个 mean ?

46
00:03:38,630 --> 00:03:43,183
well , research 有 shown something 非常 interesting .

47
00:03:43,183 --> 00:03:46,922
那个 是 , 如果 你 ' re going 到 是 学习 something ,

48
00:03:46,922 --> 00:03:52,970
你 often just don ' t 知道 什么 ' s 重要的 关于 什么 你 ' re trying 到 学习 .

49
00:03:52,970 --> 00:03:59,248
你 struggle , 你 ' re reading 和 它 seems like 那里 ' s lots 的 不同的 points 和

50
00:03:59,248 --> 00:04:03,465
他们 ' re 所有 重要的 和 你的 思维 feels overwhelmed .

51
00:04:03,465 --> 00:04:07,910
但是 如果 你 create 一个 pretest 在 这个 material ,

52
00:04:07,910 --> 00:04:14,533
你 可能 知道 nothing 和 你 知道 你 ' re going 到 flunk 这个 test .

53
00:04:14,533 --> 00:04:19,967
它 turns out though , 那个 如果 你 有 一些 真的 好的 pretest questions ,

54
00:04:19,967 --> 00:04:24,366
那个 将 actually feed 你的 大脑 insight 进入 什么 这个 最

55
00:04:24,366 --> 00:04:28,920
重要的 material 是 那个 你 ' re 关于 到 学习 .

56
00:04:28,920 --> 00:04:34,033
pretesting questions , 那个 是 , taking 一个 little pretesting

57
00:04:34,033 --> 00:04:39,244
quiz 那个 你 给 yourself 在...之前 你 你 begin 到 学习 things

58
00:04:39,244 --> 00:04:44,848
能 给 你 insight 你的 大脑 将 使用 happily 到 知道 什么 这个 最

59
00:04:44,848 --> 00:04:49,893
重要的 things 是 那个 你 ' re supposed 到 是 学习 .

60
00:04:49,893 --> 00:04:53,612
它 将 also start promoting curiosity ,

61
00:04:53,612 --> 00:04:58,081
哪个 , as 我们 ' ll 看 , 是 helpful 为了 学习 .

62
00:04:58,081 --> 00:05:04,100
sometimes 它 ' s valuable 到 ask 为了 key ideas related 到 什么 你 ' re 学习 .

63
00:05:04,100 --> 00:05:10,346
这个 能 帮助 如果 你 ' re struggling 到 pick out 这个 main ideas 从 一个 text 或

64
00:05:10,346 --> 00:05:16,694
视频 当 它 comes 到 一个 repetitive question 那个 你 可能 often ask chat ,

65
00:05:16,694 --> 00:05:22,134
GPT 或 其他 engines , 那里 ' s 一个 trick 我 want 到 显示 你 因为

66
00:05:22,134 --> 00:05:27,776
这个 reality 是 那个 你 能 去 到 一些 的 这些 不同的 engines 和

67
00:05:27,776 --> 00:05:32,410
apps , 但是 真的 你 能 only pay 为了 所以 许多 不同的

68
00:05:32,410 --> 00:05:38,308
specialized websites 如果 你 能 even afford 到 pay 为了 任何 的 他们 .

69
00:05:38,308 --> 00:05:43,138
它 能 sometimes 是 beneficial 然后 到 找到 little tricks

70
00:05:43,138 --> 00:05:48,080
到 使用 engines 和 apps 一个 bit 更多 easily 和 effectively .

71
00:05:49,180 --> 00:05:55,220
along 这些 lines , 我 like 到 使用 something called abbreviate .

72
00:05:55,220 --> 00:05:59,420
我 don ' t 得到 任何 money 从 他们 , 我 just like 他们 .

73
00:05:59,420 --> 00:06:04,660
这个 app 是 什么 ' s called 一个 text expander .

74
00:06:04,660 --> 00:06:09,680
你 能 拿 这个 little tool 和 load 它 在 你的 机器 .

75
00:06:09,680 --> 00:06:11,777
watch 什么 它 做 .

76
00:06:11,777 --> 00:06:17,940
看 一个 long sentence like 这个 哪个 says , 能 你 provide 一个 three - page

77
00:06:17,940 --> 00:06:23,090
synopsis 在 layperson ' s terms 的 这个 attached document ?

78
00:06:23,090 --> 00:06:28,359
所有 我 有 到 做 instead 的 那个 sentence 是 type . . c ,

79
00:06:28,359 --> 00:06:33,286
和 using 我的 text expander , 它 将 automatically

80
00:06:33,286 --> 00:06:38,328
expand 那个 . . c 进入 这个 long complete sentence 或

81
00:06:38,328 --> 00:06:44,290
several sentences 那个 我 可能 frequently type 在 .

82
00:06:44,290 --> 00:06:49,071
我 like 到 使用 一个 text expander 那个 says 请 给 我

83
00:06:49,071 --> 00:06:53,751
一个 synopsis 的 along 这个 lines 的 什么 你 看 这里 .

84
00:06:53,751 --> 00:07:00,090
这个 text expander 将 做 一个 非常 nice job 的 typing 那个 entire sentence 在 .

85
00:07:00,090 --> 00:07:04,547
什么 我 做 是 拿 这个 paperclip , 我 load 一个 pdf 在 它 ,

86
00:07:04,547 --> 00:07:09,005
我 type c , 和 然后 我 得到 一个 nice little discussion 或

87
00:07:09,005 --> 00:07:14,710
summary 的 什么 那个 paper 可能 有 是 discussing .

88
00:07:14,710 --> 00:07:19,661
我 generally 找到 那个 claude seems 到 做 一个 真的 好的 job 与 这个

89
00:07:19,661 --> 00:07:23,150
sort 的 synthesizing 的 key ideas .

90
00:07:23,150 --> 00:07:28,457
什么 你 ' ll 找到 是 那个 certain foundational models 将 有

91
00:07:28,457 --> 00:07:35,690
advantages 为了 certain tasks , others 将 有 advantages 为了 其他 tasks .

92
00:07:35,690 --> 00:07:38,130
just 一个 quick insight .

93
00:07:38,130 --> 00:07:42,970
我 做 like 到 使用 一些 的 这个 flashcard apps 那个 是 available .

94
00:07:42,970 --> 00:07:48,146
生成式 AI 是 going 进入 许多 的 这些 apps .

95
00:07:48,146 --> 00:07:50,435
所以 这里 ' s 一个 例子 .

96
00:07:50,435 --> 00:07:54,680
它 ' s 一个 flashcard 系统 called idorecall .

97
00:07:54,680 --> 00:08:01,389
在 这个 系统 , 什么 你 能 做 是 upload 一个 electronic document .

98
00:08:01,389 --> 00:08:05,520
once 你 ' ve got 它 uploaded , 你 能 tag 它 和 save 它 .

99
00:08:05,520 --> 00:08:10,368
然后 你 ' ve got 你的 document online 和

100
00:08:10,368 --> 00:08:13,977
然后 你 能 拿 AI 和 使用 它 .

101
00:08:13,977 --> 00:08:18,770
generate retrieval 练习 flashcards 从 那个 page .

102
00:08:18,770 --> 00:08:23,040
你 能 also 使用 这些 为了 pretesting .

103
00:08:23,040 --> 00:08:29,062
如果 你 want 到 , 你 能 , 在 这个 其他 hand , select text 和

104
00:08:29,062 --> 00:08:34,129
然后 generate 一个 flashcard using AI 为了 那个 text .

105
00:08:34,129 --> 00:08:40,441
这个 one says , 什么 做 transformer networks 学习 到 predict ?

106
00:08:40,441 --> 00:08:45,450
然后 , 的 课程 , 你 能 save 那个 recall 如果 你 like 它 , 或

107
00:08:45,450 --> 00:08:51,140
tweak 它 as 你 可能 desire , 然后 你 能 练习 与 它 .

108
00:08:51,140 --> 00:08:55,473
什么 做 transformer networks 学习 到 predict ?

109
00:08:55,473 --> 00:08:59,240
他们 predict long - term dependencies .

110
00:08:59,240 --> 00:09:03,570
got 那个 100 % correct .

111
00:09:03,570 --> 00:09:10,172
的 课程 , 那里 ' s far 更多 than 简单的 electronic documents 你 能 工作 与 .

112
00:09:10,172 --> 00:09:16,318
你 能 去 到 youtube , create 一个 profile over time 的 你的 recalls .

113
00:09:16,318 --> 00:09:21,228
它 ' s 所以 很多 那个 它 ' s too 很多 到 describe 这里 ,

114
00:09:21,228 --> 00:09:24,889
但是 这些 systems 是 非常 强大的 .

115
00:09:24,889 --> 00:09:29,281
also , 我 应该 mention 那里 ' s 相当 一个 few 不同的

116
00:09:29,281 --> 00:09:33,205
flashcard apps out 那里 alongside idorecall .

117
00:09:33,205 --> 00:09:37,678
那里 ' s 的 课程 , 这个 ever popular quizlet ,

118
00:09:37,678 --> 00:09:43,210
kahoot , 和 sort 的 这个 老的 grand master , anki .

119
00:09:43,210 --> 00:09:48,733
这个 gives 一个 little insight 那个 我 hope gives 你 一些 帮助 当 它 comes 到

120
00:09:48,733 --> 00:09:55,290
真的 keying 在 在 这个 重要的 issues related 到 什么 你 want 到 是 学习 .

121
00:09:55,290 --> 00:09:58,410
现在 , 朱尔斯 将 是 going even deeper .

