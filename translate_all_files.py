#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量翻译所有SRT文件
"""

import os
import subprocess
import sys

def get_all_english_srt_files():
    """获取所有英文SRT文件"""
    files = []
    for filename in os.listdir('.'):
        if filename.endswith('.en.srt'):
            files.append(filename)
    return sorted(files)

def translate_file(input_file, output_file):
    """翻译单个文件"""
    print(f"\n正在翻译: {input_file}")
    print(f"输出到: {output_file}")
    
    try:
        result = subprocess.run([
            sys.executable, 'translate_srt_real.py', 
            '-i', input_file, 
            '-o', output_file
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print(f"✓ 成功翻译: {input_file}")
            return True
        else:
            print(f"✗ 翻译失败: {input_file}")
            print(f"错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ 翻译出错: {input_file}, 错误: {e}")
        return False

def main():
    """主函数"""
    print("开始批量翻译SRT文件...")
    
    # 获取所有英文SRT文件
    english_files = get_all_english_srt_files()
    
    if not english_files:
        print("未找到英文SRT文件")
        return
    
    print(f"找到 {len(english_files)} 个英文SRT文件")
    
    success_count = 0
    total_count = len(english_files)
    
    for i, english_file in enumerate(english_files, 1):
        print(f"\n[{i}/{total_count}] 处理文件: {english_file}")
        
        # 生成中文文件名
        chinese_file = english_file.replace('.en.srt', '.zh.srt')
        
        # 翻译文件
        if translate_file(english_file, chinese_file):
            success_count += 1
        
        print("-" * 60)
    
    print(f"\n翻译完成！")
    print(f"成功: {success_count}/{total_count}")
    print(f"失败: {total_count - success_count}/{total_count}")

if __name__ == "__main__":
    main()
