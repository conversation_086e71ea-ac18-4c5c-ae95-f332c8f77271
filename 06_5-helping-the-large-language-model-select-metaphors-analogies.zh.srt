1
00:00:00,000 --> 00:00:04,020
这个例子效果很好，
我们翻译了

2
00:00:04,020 --> 00:00:05,920
为什么我们想要

3
00:00:05,920 --> 00:00:08,280
继续学习
生成式AI的概念，

4
00:00:08,280 --> 00:00:09,520
即使它变化如此

5
00:00:09,520 --> 00:00:12,080
迅速，翻译到了
汽车领域。

6
00:00:12,080 --> 00:00:15,660
但是当我们

7
00:00:15,660 --> 00:00:17,260
不知道它

8
00:00:17,260 --> 00:00:19,360
应该用什么石头
来翻译时会发生什么？

9
00:00:19,360 --> 00:00:23,315
也就是说，我明确地
告诉它

10
00:00:23,315 --> 00:00:25,840
使用汽车领域

11
00:00:25,840 --> 00:00:28,570
和引擎或类似的东西。

12
00:00:28,570 --> 00:00:31,080
当我们不明确知道

13
00:00:31,080 --> 00:00:33,910
石头应该是什么时
会发生什么？

14
00:00:33,910 --> 00:00:35,560
我们如何到达那里？

15
00:00:35,560 --> 00:00:37,860
什么是正确的方法？

16
00:00:37,860 --> 00:00:40,640
现在，我们还能
如何做到这一点？

17
00:00:40,640 --> 00:00:41,880
嗯，我们可能会

18
00:00:41,880 --> 00:00:43,280
给它一个更丰富的
自我画像，

19
00:00:43,280 --> 00:00:45,600
因为事实是也许
我们不只是想让

20
00:00:45,600 --> 00:00:48,210
它从汽车领域
汲取，

21
00:00:48,210 --> 00:00:49,620
也许我们做很多事情，
我们希望它

22
00:00:49,620 --> 00:00:51,360
变化例子。

23
00:00:51,360 --> 00:00:54,200
我们不想总是得到

24
00:00:54,200 --> 00:00:56,540
完全相同的例子。

25
00:00:56,540 --> 00:00:58,920
我们不希望
一切都是引擎。

26
00:00:58,920 --> 00:01:01,420
我们不希望
一切都是汽车。

27
00:01:01,420 --> 00:01:04,650
我们想要变化
我们看到的不同事物，

28
00:01:04,650 --> 00:01:07,140
希望我们能连接到

29
00:01:07,140 --> 00:01:08,580
我们大脑中
强大的东西，或者

30
00:01:08,580 --> 00:01:10,375
也许我们会连接到
不止一个东西，

31
00:01:10,375 --> 00:01:13,995
看到例子被翻译

32
00:01:13,995 --> 00:01:19,035
成多个不同的
领域真的很重要。

33
00:01:19,035 --> 00:01:21,190
我要改变
提示，

34
00:01:21,190 --> 00:01:23,040
我要说关于我

35
00:01:23,040 --> 00:01:24,790
：我是

36
00:01:24,790 --> 00:01:26,630
范德堡大学的
计算机科学教授。

37
00:01:26,630 --> 00:01:28,215
我是高级顾问，

38
00:01:28,215 --> 00:01:30,130
我有一个错字，
校长在

39
00:01:30,130 --> 00:01:32,470
生成式AI和
企业与教育方面。

40
00:01:32,470 --> 00:01:35,050
我喜欢山地自行车
和骑BMX自行车。

41
00:01:35,050 --> 00:01:36,790
我喜欢烹饪和
健康饮食。

42
00:01:36,790 --> 00:01:38,110
即使我在

43
00:01:38,110 --> 00:01:39,710
饮食自律方面
不是那么好，

44
00:01:39,710 --> 00:01:42,350
我喜欢理解
营养背后的科学。

45
00:01:42,350 --> 00:01:44,730
解释变换器模型

46
00:01:44,730 --> 00:01:46,530
如何工作，使用

47
00:01:46,530 --> 00:01:48,190
与我产生共鸣的
类比和隐喻。

48
00:01:48,190 --> 00:01:50,030
我们正在看那个

49
00:01:50,030 --> 00:01:52,150
支撑所有这些东西的
变换器模型。

50
00:01:52,150 --> 00:01:55,010
现在，我告诉它的是
我希望你

51
00:01:55,010 --> 00:01:56,170
使用类比和

52
00:01:56,170 --> 00:01:58,070
对我有意义的
隐喻，

53
00:01:58,070 --> 00:01:59,655
我告诉它很多
关于我的信息，

54
00:01:59,655 --> 00:02:02,480
我希望它去从

55
00:02:02,480 --> 00:02:05,430
那个知识领域
汲取，希望

56
00:02:05,430 --> 00:02:09,015
识别对我有帮助的东西，
它回来说，

57
00:02:09,015 --> 00:02:11,510
变换器模型就像
一个烹饪过程，

58
00:02:11,510 --> 00:02:14,490
它谈论
成分作为标记，

59
00:02:14,490 --> 00:02:17,230
注意力机制
是厨师的专注，

60
00:02:17,230 --> 00:02:18,750
层作为烹饪步骤，

61
00:02:18,750 --> 00:02:21,610
它想出了
一个完整的描述

62
00:02:21,610 --> 00:02:25,020
翻译到那个领域，
但然后它走得更远，

63
00:02:25,020 --> 00:02:28,110
它说变换器模型是

64
00:02:28,110 --> 00:02:29,415
一次山地自行车骑行

65
00:02:29,415 --> 00:02:31,480
路径图是
输入序列，

66
00:02:31,480 --> 00:02:34,740
它现在使用
那个概念来描述它。

67
00:02:34,740 --> 00:02:36,780
它现在继续说

68
00:02:36,780 --> 00:02:39,300
其他事情，如果我继续
提示它，但关键

69
00:02:39,300 --> 00:02:44,000
是它开始从
我的经验中汲取。

70
00:02:44,000 --> 00:02:45,720
我告诉它关于我

71
00:02:45,720 --> 00:02:47,860
以及我理解什么
和我知道什么以及

72
00:02:47,860 --> 00:02:50,760
我喜欢什么，希望
它能汲取

73
00:02:50,760 --> 00:02:55,940
隐喻或
类比或例子

74
00:02:55,940 --> 00:02:57,900
这些将更好地
与我理解的事物连接

75
00:02:57,900 --> 00:02:59,790
它正在通过翻译
做到这一点，因为我们

76
00:02:59,790 --> 00:03:01,510
告诉它你要

77
00:03:01,510 --> 00:03:03,610
用来构建
这些隐喻

78
00:03:03,610 --> 00:03:05,250
和类比来交流

79
00:03:05,250 --> 00:03:06,950
这些想法的石头

80
00:03:06,950 --> 00:03:09,350
将从

81
00:03:09,350 --> 00:03:12,450
我了解的信息中
汲取，

82
00:03:12,450 --> 00:03:14,050
我理解的事物。

83
00:03:14,050 --> 00:03:15,750
我希望你使用我

84
00:03:15,750 --> 00:03:18,050
理解的石头作为你的
构建块。

85
00:03:18,050 --> 00:03:20,690
现在，如果我们想要
去思考

86
00:03:20,690 --> 00:03:22,490
我们如何

87
00:03:22,490 --> 00:03:25,370
去为别人
解释某些东西？

88
00:03:25,370 --> 00:03:29,570
或者如果我们不一定
知道描述它的好方法

89
00:03:29,570 --> 00:03:31,330
我们不确切知道

90
00:03:31,330 --> 00:03:33,370
如何描述我们自己，

91
00:03:33,370 --> 00:03:36,630
或者我们在考虑
一个教室？

92
00:03:36,630 --> 00:03:38,210
嗯，我们可以使用
所谓的

93
00:03:38,210 --> 00:03:40,235
受众角色模式。

94
00:03:40,235 --> 00:03:42,390
这个模式背后的想法是，

95
00:03:42,390 --> 00:03:44,570
我们想要解释某个主题，

96
00:03:44,570 --> 00:03:46,250
但我们想要向

97
00:03:46,250 --> 00:03:48,650
特定受众解释它，
我们将

98
00:03:48,650 --> 00:03:51,550
将这与
那个隐喻模式结合

99
00:03:51,550 --> 00:03:53,990
来做一些真正强大的事情。

100
00:03:54,050 --> 00:03:56,210
这是基本模式。

101
00:03:56,210 --> 00:03:57,830
我们说，向

102
00:03:57,830 --> 00:04:00,270
计算机科学教授
解释某个主题，

103
00:04:00,270 --> 00:04:04,335
向汽车修理工
解释这个主题，

104
00:04:04,335 --> 00:04:06,405
向

105
00:04:06,405 --> 00:04:09,750
语言病理学家
解释这个主题，我们将

106
00:04:09,750 --> 00:04:12,090
然后通过

107
00:04:12,090 --> 00:04:15,840
说使用类比
和隐喻来稍微修改它。

108
00:04:15,840 --> 00:04:17,650
我们要说的是
解释

109
00:04:17,650 --> 00:04:20,010
变换器模型如何
向五年级学生工作，

110
00:04:20,010 --> 00:04:22,170
这是我们的受众，使用

111
00:04:22,170 --> 00:04:24,050
类比和隐喻

112
00:04:24,050 --> 00:04:26,020
对受众有意义。

113
00:04:26,020 --> 00:04:30,105
现在，会发生什么？它
回来说，

114
00:04:30,105 --> 00:04:32,070
想象一个巨大的图书馆，

115
00:04:32,070 --> 00:04:35,110
书架作为层，
书作为词汇。

116
00:04:35,110 --> 00:04:39,170
你注意到我们得到了
描述的常见概念，

117
00:04:39,170 --> 00:04:40,490
但它们被翻译成

118
00:04:40,490 --> 00:04:42,590
不同的方式，取决于
受众。

119
00:04:42,590 --> 00:04:44,590
它们被翻译成
不同的隐喻和

120
00:04:44,590 --> 00:04:47,435
对受众有意义的
类比。

121
00:04:47,435 --> 00:04:49,550
我们在最开始时
给了它

122
00:04:49,550 --> 00:04:52,130
我们想要使用的
非常具体的东西。

123
00:04:52,130 --> 00:04:55,025
石头被非常
紧密地约束，

124
00:04:55,025 --> 00:04:57,110
它没有选择，
它必须使用

125
00:04:57,110 --> 00:05:00,230
引擎和汽车类比，
我们逐渐工作

126
00:05:00,230 --> 00:05:03,130
我们的方式来描述

127
00:05:03,130 --> 00:05:05,070
我们个人了解的

128
00:05:05,070 --> 00:05:06,570
和个人喜欢的事物，

129
00:05:06,570 --> 00:05:08,010
然后从中汲取隐喻和

130
00:05:08,010 --> 00:05:09,910
类比，现在我们实际上

131
00:05:09,910 --> 00:05:12,310
在看如何汲取
对特定

132
00:05:12,310 --> 00:05:13,410
受众有意义的隐喻类比

133
00:05:13,410 --> 00:05:15,150
比如五年级学生。

134
00:05:15,150 --> 00:05:18,420
然后回来说
玩乐高积木，

135
00:05:18,420 --> 00:05:22,790
然后是侦探
解决谜题。

136
00:05:22,790 --> 00:05:25,650
现在，这背后的关键
是它全部

137
00:05:25,650 --> 00:05:29,290
建立在这种更广泛的
翻译理解之上。

138
00:05:29,290 --> 00:05:31,745
翻译不仅仅是
逐词翻译，

139
00:05:31,745 --> 00:05:34,540
而是将某些东西

140
00:05:34,540 --> 00:05:36,940
转换为以新形式

141
00:05:36,940 --> 00:05:38,540
表示意义的概念。

142
00:05:38,540 --> 00:05:41,660
就像它取了
"我的名字是朱尔斯·怀特"，

143
00:05:41,660 --> 00:05:45,840
并将其翻译成
一系列堆叠的石头。

144
00:05:45,840 --> 00:05:47,520
我们在做同样的
事情，但我们

145
00:05:47,520 --> 00:05:49,240
使用来自特定领域的
隐喻和类比

146
00:05:49,240 --> 00:05:50,740
成为我们的石头

147
00:05:50,740 --> 00:05:53,060
我们用来堆叠，

148
00:05:53,060 --> 00:05:56,415
我们用它来
表示意义

149
00:05:56,415 --> 00:06:00,460
然后如果我们汲取
正确的隐喻和类比，

150
00:06:00,460 --> 00:06:03,300
那么我们可以让它
以帮助我们学习的方式解释事情

151
00:06:03,300 --> 00:06:07,200
并帮助我们更好地
理解概念。
