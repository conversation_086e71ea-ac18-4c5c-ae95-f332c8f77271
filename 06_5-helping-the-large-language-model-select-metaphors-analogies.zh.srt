1
00:00:00,000 --> 00:00:04,020
这个 例子 worked 真的 well 哪里 我们 translated

2
00:00:04,020 --> 00:00:05,920
这个 概念 的 为什么 我们 会 want 到 去

3
00:00:05,920 --> 00:00:08,280
ahead 和 学习 关于 生成式 AI ,

4
00:00:08,280 --> 00:00:09,520
even though 它 ' s changing 所以

5
00:00:09,520 --> 00:00:12,080
rapidly 进入 这个 automotive domain .

6
00:00:12,080 --> 00:00:15,660
但是 什么 happens 当 我们

7
00:00:15,660 --> 00:00:17,260
don ' t 知道 这个 rocks 那个 它

8
00:00:17,260 --> 00:00:19,360
应该 使用 到 translate 进入 ?

9
00:00:19,360 --> 00:00:23,315
那个 是 , like , 我 am expressly telling 它

10
00:00:23,315 --> 00:00:25,840
使用 这个 automotive domain

11
00:00:25,840 --> 00:00:28,570
和 engines 或 things like 那个 .

12
00:00:28,570 --> 00:00:31,080
什么 happens 当 我们 don ' t 知道

13
00:00:31,080 --> 00:00:33,910
explicitly 什么 这个 rock 应该 是 ?

14
00:00:33,910 --> 00:00:35,560
如何 做 我们 得到 那里 ?

15
00:00:35,560 --> 00:00:37,860
什么 是 这个 正确的 方法 ?

16
00:00:37,860 --> 00:00:40,640
现在 , 如何 else 可能 我们 做 这个 ?

17
00:00:40,640 --> 00:00:41,880
well , 我们 可能 去 和 给 它

18
00:00:41,880 --> 00:00:43,280
一个 richer picture 的 ourselves ,

19
00:00:43,280 --> 00:00:45,600
因为 这个 truth 是 maybe 我们 don ' t just want 到 有

20
00:00:45,600 --> 00:00:48,210
它 draw 从 这个 automotive domain ,

21
00:00:48,210 --> 00:00:49,620
maybe 我们 做 许多 things 哪里 我们 want 它

22
00:00:49,620 --> 00:00:51,360
到 vary 这个 examples .

23
00:00:51,360 --> 00:00:54,200
我们 don ' t want 到 always 得到 这个 相同的 exact 例子 .

24
00:00:54,200 --> 00:00:56,540
我们 don ' t want everything 到 是 一个 engine .

25
00:00:56,540 --> 00:00:58,920
我们 don ' t want everything 到 是 一个 car .

26
00:00:58,920 --> 00:01:01,420
我们 want 到 vary

27
00:01:01,420 --> 00:01:04,650
这个 不同的 things 那个 我们 看 那个 hopefully ,

28
00:01:04,650 --> 00:01:07,140
我们 将 connect 到 something 那个

29
00:01:07,140 --> 00:01:08,580
是 强大的 在 我们的 brains 或

30
00:01:08,580 --> 00:01:10,375
maybe 我们 ' ll connect 到 更多 than one thing ,

31
00:01:10,375 --> 00:01:13,995
和 seeing 这个 例子 translated

32
00:01:13,995 --> 00:01:19,035
进入 multiple 不同的 domains 是 真的 重要的 .

33
00:01:19,035 --> 00:01:21,190
我 ' m going 到 change 这个 提示 上 ,

34
00:01:21,190 --> 00:01:23,040
和 我 ' m going 到 说 关于 我

35
00:01:23,040 --> 00:01:24,790
: 我 ' m 一个 教授

36
00:01:24,790 --> 00:01:26,630
的 计算机 science 在 vanderbilt 大学 .

37
00:01:26,630 --> 00:01:28,215
我 ' m 这个 senior advisor ,

38
00:01:28,215 --> 00:01:30,130
我 有 一个 typo 到 这个 chancellor 在

39
00:01:30,130 --> 00:01:32,470
生成式 AI 和 enterprise 和 教育 .

40
00:01:32,470 --> 00:01:35,050
我 love 这个 mountain bike 和 ride bmx bikes .

41
00:01:35,050 --> 00:01:36,790
我 like cooking 和 healthy eating .

42
00:01:36,790 --> 00:01:38,110
even 如果 我 ' m 不 那个 great

43
00:01:38,110 --> 00:01:39,710
在 是 disciplined 与 我的 eating ,

44
00:01:39,710 --> 00:01:42,350
我 like 到 理解 这个 science behind nutrition .

45
00:01:42,350 --> 00:01:44,730
解释 如何 这个 transformer 模型

46
00:01:44,730 --> 00:01:46,530
works using analogies 和

47
00:01:46,530 --> 00:01:48,190
metaphors 那个 将 resonate 与 我 .

48
00:01:48,190 --> 00:01:50,030
我们 ' re looking 在 那个 transformer 模型

49
00:01:50,030 --> 00:01:52,150
那个 underlies 所有 这个 stuff .

50
00:01:52,150 --> 00:01:55,010
现在 , 什么 我 ' m telling 它 是 我 want 你

51
00:01:55,010 --> 00:01:56,170
到 使用 analogies 和

52
00:01:56,170 --> 00:01:58,070
metaphors 那个 将 制作 sense 到 我 ,

53
00:01:58,070 --> 00:01:59,655
和 我 ' m telling 它 一个 lot 关于

54
00:01:59,655 --> 00:02:02,480
我 和 我 want 它 到 去 和 pull 从

55
00:02:02,480 --> 00:02:05,430
那个 domain 的 知识 到 hopefully

56
00:02:05,430 --> 00:02:09,015
identify things 那个 是 helpful 到 我 和 它 comes back ,

57
00:02:09,015 --> 00:02:11,510
transformer 模型 是 一个 cooking 过程 ,

58
00:02:11,510 --> 00:02:14,490
和 它 talks 关于 ingredients as tokens ,

59
00:02:14,490 --> 00:02:17,230
和 这个 attention mechanism 是 这个 chef ' s focus ,

60
00:02:17,230 --> 00:02:18,750
layers as cooking steps ,

61
00:02:18,750 --> 00:02:21,610
和 它 ' s coming 上 与 一个 whole description 的

62
00:02:21,610 --> 00:02:25,020
translation 进入 那个 domain 但是 然后 它 goes further ,

63
00:02:25,020 --> 00:02:28,110
和 它 says transformer 模型 是

64
00:02:28,110 --> 00:02:29,415
一个 mountain bike ride

65
00:02:29,415 --> 00:02:31,480
和 这个 trail map 是 这个 input sequence ,

66
00:02:31,480 --> 00:02:34,740
和 它 ' s 现在 describing 它 using concepts 从 那个 .

67
00:02:34,740 --> 00:02:36,780
它 现在 goes 在 和 says

68
00:02:36,780 --> 00:02:39,300
其他 things 如果 我 keep 提示 它 但是 这个 key

69
00:02:39,300 --> 00:02:44,000
是 那个 它 是 beginning 到 draw 从 我的 经验 .

70
00:02:44,000 --> 00:02:45,720
我 ' m telling 它 关于 我

71
00:02:45,720 --> 00:02:47,860
和 什么 我 理解 和 什么 我 知道 和 什么

72
00:02:47,860 --> 00:02:50,760
我 like 与 这个 hope 那个 它 能 draw

73
00:02:50,760 --> 00:02:55,940
metaphors 或 analogies 或 examples

74
00:02:55,940 --> 00:02:57,900
那个 将 better connect 与 这个 things

75
00:02:57,900 --> 00:02:59,790
那个 我 理解 和 它 ' s doing

76
00:02:59,790 --> 00:03:01,510
这个 通过 translation 因为 我们 ' re

77
00:03:01,510 --> 00:03:03,610
telling 它 那个 这个 rocks 那个 你 ' re going 到

78
00:03:03,610 --> 00:03:05,250
工作 与 到 build 这些 metaphors

79
00:03:05,250 --> 00:03:06,950
和 analogies 到 communicate

80
00:03:06,950 --> 00:03:09,350
这些 ideas 是 going 到 是

81
00:03:09,350 --> 00:03:12,450
drawn 从 这个 信息 那个 我 知道 关于 ,

82
00:03:12,450 --> 00:03:14,050
这个 things 那个 我 理解 .

83
00:03:14,050 --> 00:03:15,750
我 want 你 到 使用 rocks 那个

84
00:03:15,750 --> 00:03:18,050
我 理解 as 你的 building blocks .

85
00:03:18,050 --> 00:03:20,690
现在 , 什么 如果 我们 want 到 去 和 想

86
00:03:20,690 --> 00:03:22,490
关于 如何 做 我们

87
00:03:22,490 --> 00:03:25,370
去 和 解释 something 为了 somebody else ?

88
00:03:25,370 --> 00:03:29,570
或 什么 如果 我们 don ' t necessarily 知道 什么 一个 好的 方式 的

89
00:03:29,570 --> 00:03:31,330
describing 它 是 关于 我们

90
00:03:31,330 --> 00:03:33,370
don ' t 知道 exactly 如何 到 describe ourselves ,

91
00:03:33,370 --> 00:03:36,630
或 我们 ' re trying 到 想 关于 一个 classroom ?

92
00:03:36,630 --> 00:03:38,210
well , 我们 能 使用 什么 ' s called

93
00:03:38,210 --> 00:03:40,235
这个 audience persona pattern .

94
00:03:40,235 --> 00:03:42,390
这个 想法 behind 这个 pattern 是 ,

95
00:03:42,390 --> 00:03:44,570
我们 want 到 解释 一些 主题 ,

96
00:03:44,570 --> 00:03:46,250
但是 我们 want 到 解释 它 到

97
00:03:46,250 --> 00:03:48,650
一个 particular audience 和 我们 ' re going 到

98
00:03:48,650 --> 00:03:51,550
combine 这个 与 那个 metaphor pattern

99
00:03:51,550 --> 00:03:53,990
到 做 something 真的 强大的 .

100
00:03:54,050 --> 00:03:56,210
这个 是 这个 basic pattern .

101
00:03:56,210 --> 00:03:57,830
我们 说 , 解释 一些 主题

102
00:03:57,830 --> 00:04:00,270
到 一个 教授 在 计算机 science ,

103
00:04:00,270 --> 00:04:04,335
解释 这个 主题 到 一个 auto mechanic ,

104
00:04:04,335 --> 00:04:06,405
解释 这个 主题 到

105
00:04:06,405 --> 00:04:09,750
一个 speech - 语言 pathologist 和 我们 ' re going 到

106
00:04:09,750 --> 00:04:12,090
然后 modify 它 一个 little bit 通过

107
00:04:12,090 --> 00:04:15,840
saying using analogies 和 metaphors .

108
00:04:15,840 --> 00:04:17,650
什么 我们 ' re going 到 说 是 解释 如何

109
00:04:17,650 --> 00:04:20,010
这个 transformer 模型 works 到 一个 fifth grader ,

110
00:04:20,010 --> 00:04:22,170
哪个 是 我们的 audience , using

111
00:04:22,170 --> 00:04:24,050
analogies 和 metaphors 那个

112
00:04:24,050 --> 00:04:26,020
将 制作 sense 到 这个 audience .

113
00:04:26,020 --> 00:04:30,105
现在 , 什么 happens ? 它 comes back , 和 它 says ,

114
00:04:30,105 --> 00:04:32,070
imagine 一个 giant library ,

115
00:04:32,070 --> 00:04:35,110
和 bookshelves as layers , books as words .

116
00:04:35,110 --> 00:04:39,170
你 notice 我们 ' re getting common concepts described ,

117
00:04:39,170 --> 00:04:40,490
但是 他们 ' re 是 translated

118
00:04:40,490 --> 00:04:42,590
不同的 ways depending 在 这个 audience .

119
00:04:42,590 --> 00:04:44,590
他们 ' re 是 translated 进入 不同的 metaphors 和

120
00:04:44,590 --> 00:04:47,435
analogies 那个 makes sense 到 这个 audience .

121
00:04:47,435 --> 00:04:49,550
我们 started off 在 这个 非常 beginning giving 它

122
00:04:49,550 --> 00:04:52,130
非常 specific things 那个 我们 wanted 到 使用 .

123
00:04:52,130 --> 00:04:55,025
这个 rocks 是 非常 closely constrained ,

124
00:04:55,025 --> 00:04:57,110
它 didn ' t 有 choice 它 有 到 使用

125
00:04:57,110 --> 00:05:00,230
一个 engine 和 car analogy 和 我们 gradually worked

126
00:05:00,230 --> 00:05:03,130
我们的 方式 到 describing

127
00:05:03,130 --> 00:05:05,070
这个 things 那个 我们 知道 关于

128
00:05:05,070 --> 00:05:06,570
personally 和 like personally ,

129
00:05:06,570 --> 00:05:08,010
和 然后 drawing metaphors 和

130
00:05:08,010 --> 00:05:09,910
analogies 从 那个 和 现在 我们 ' re actually

131
00:05:09,910 --> 00:05:12,310
looking 在 如何 到 draw metaphors analogies 那个

132
00:05:12,310 --> 00:05:13,410
将 制作 sense 到 一个 particular

133
00:05:13,410 --> 00:05:15,150
audience like 一个 fifth grader .

134
00:05:15,150 --> 00:05:18,420
然后 comes back 与 playing 与 lego blocks ,

135
00:05:18,420 --> 00:05:22,790
和 然后 detective solving 一个 mystery .

136
00:05:22,790 --> 00:05:25,650
现在 , 这个 key behind 这个 是 那个 它 ' s 所有

137
00:05:25,650 --> 00:05:29,290
built 在 这个 wider 理解 的 translation .

138
00:05:29,290 --> 00:05:31,745
这个 translation isn ' t just word 为了 word ,

139
00:05:31,745 --> 00:05:34,540
translation 但是 它 是 这个 概念 的

140
00:05:34,540 --> 00:05:36,940
converting something 到 representing

141
00:05:36,940 --> 00:05:38,540
meaning 在 一个 新的 form .

142
00:05:38,540 --> 00:05:41,660
just like 它 took 我的 name 是 朱尔斯 怀特 ,

143
00:05:41,660 --> 00:05:45,840
和 它 translated 那个 进入 一个 sequence 的 rocks stacked .

144
00:05:45,840 --> 00:05:47,520
我们 ' re doing 这个 相同的 thing , 但是 我们 ' re

145
00:05:47,520 --> 00:05:49,240
using metaphors 和 analogies

146
00:05:49,240 --> 00:05:50,740
从 一个 particular domain 到

147
00:05:50,740 --> 00:05:53,060
become 我们的 rocks 那个 我们 ' re having 一个 stack 与 ,

148
00:05:53,060 --> 00:05:56,415
和 那个 我们 ' re using 它 到 represent meaning

149
00:05:56,415 --> 00:06:00,460
和 然后 如果 我们 draw 这个 正确的 metaphors 和 analogies ,

150
00:06:00,460 --> 00:06:03,300
然后 我们 能 得到 它 到 解释 things 在 一个 方式 那个 帮助

151
00:06:03,300 --> 00:06:07,200
我们 学习 和 帮助 我们 到 理解 concepts better .

