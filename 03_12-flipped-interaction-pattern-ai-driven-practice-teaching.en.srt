1
00:00:00,000 --> 00:00:03,440
Fascinating capability of
these large language models

2
00:00:03,440 --> 00:00:04,940
is to help us practice,

3
00:00:04,940 --> 00:00:06,960
but do it interactively.

4
00:00:06,960 --> 00:00:09,740
Now, most people probably
get the sense now of,

5
00:00:09,740 --> 00:00:12,020
how you would go in and
generate a quiz with

6
00:00:12,020 --> 00:00:14,880
ten questions that you
could go and then take.

7
00:00:14,880 --> 00:00:18,480
But what if you want to go
and dynamically practice,

8
00:00:18,480 --> 00:00:21,600
where the model adapts
to your answers,

9
00:00:21,600 --> 00:00:24,220
and then uses what you say to

10
00:00:24,220 --> 00:00:25,720
choose what the
next thing is that

11
00:00:25,720 --> 00:00:27,940
it asks you or tells you,

12
00:00:27,940 --> 00:00:29,740
that idea of a sort of

13
00:00:29,740 --> 00:00:32,180
dynamic interactive practice
is something that we

14
00:00:32,180 --> 00:00:35,145
can achieve with the flipped
interaction pattern?

15
00:00:35,145 --> 00:00:38,110
The idea behind this
pattern is that,

16
00:00:38,110 --> 00:00:41,490
rather than telling one of
these models what to do,

17
00:00:41,490 --> 00:00:43,770
rather than asking it questions.

18
00:00:43,770 --> 00:00:46,270
We want to get it to
ask us questions,

19
00:00:46,270 --> 00:00:47,970
and then based on our responses,

20
00:00:47,970 --> 00:00:50,170
dynamically adapt and flow

21
00:00:50,170 --> 00:00:51,870
through whatever it is that

22
00:00:51,870 --> 00:00:54,080
we're trying to
achieve or practice.

23
00:00:54,080 --> 00:00:55,670
Let me give you an example and

24
00:00:55,670 --> 00:00:57,765
describe how this pattern works.

25
00:00:57,765 --> 00:00:59,230
This is a prompt that

26
00:00:59,230 --> 00:01:01,565
employs the flipped
interaction pattern.

27
00:01:01,565 --> 00:01:03,510
The basic idea behind this is,

28
00:01:03,510 --> 00:01:05,310
we are going to tell the model

29
00:01:05,310 --> 00:01:07,850
to ask us techniques or ask us

30
00:01:07,850 --> 00:01:10,010
questions or tell
us to do things

31
00:01:10,010 --> 00:01:12,940
one step at a time or
one question at a time.

32
00:01:12,940 --> 00:01:15,330
Then we're going to say,
based on what we say,

33
00:01:15,330 --> 00:01:17,750
then go and respond and

34
00:01:17,750 --> 00:01:20,590
choose the next question
or give me the next step.

35
00:01:20,590 --> 00:01:23,110
That is, we want it to
go one step at a time,

36
00:01:23,110 --> 00:01:24,430
telling us what to do.

37
00:01:24,430 --> 00:01:25,790
This is really important

38
00:01:25,790 --> 00:01:28,470
because by telling it to
go one step at a time,

39
00:01:28,470 --> 00:01:31,310
then it can see our
answer or it can see

40
00:01:31,310 --> 00:01:32,890
the feedback that we give it

41
00:01:32,890 --> 00:01:34,995
on the step it told
us to perform.

42
00:01:34,995 --> 00:01:37,200
Then it can decide
what to do next.

43
00:01:37,200 --> 00:01:39,050
That is, it is
flipping the script.

44
00:01:39,050 --> 00:01:42,035
It is getting to drive
the interaction with us.

45
00:01:42,035 --> 00:01:44,570
Here is the flipped
interaction prop

46
00:01:44,570 --> 00:01:47,365
that we're going to use to
build an interactive quiz.

47
00:01:47,365 --> 00:01:49,480
Please quiz beyond phishing

48
00:01:49,480 --> 00:01:51,370
and techniques that are
used to trick people.

49
00:01:51,370 --> 00:01:53,260
Phishing is the technique

50
00:01:53,260 --> 00:01:54,940
where people go and
send, for example,

51
00:01:54,940 --> 00:01:57,000
e mails to somebody
to try to trick them

52
00:01:57,000 --> 00:01:59,560
into doing something
that is inappropriate,

53
00:01:59,560 --> 00:02:01,540
so like giving up
their password.

54
00:02:01,540 --> 00:02:03,080
Break the topic down into

55
00:02:03,080 --> 00:02:05,425
individual topics that
someone needs to learn.

56
00:02:05,425 --> 00:02:07,410
Ask me questions one at

57
00:02:07,410 --> 00:02:09,870
a time to assess my
knowledge of these topics.

58
00:02:09,870 --> 00:02:11,670
At the end, suggest
topics that I

59
00:02:11,670 --> 00:02:13,745
should focus on to
improve my understanding.

60
00:02:13,745 --> 00:02:15,260
Ask me the first question.

61
00:02:15,260 --> 00:02:18,070
This pattern of, ask
me questions one at

62
00:02:18,070 --> 00:02:20,690
a time and to achieve
some particular goals.

63
00:02:20,690 --> 00:02:22,430
In this case, the goal is to

64
00:02:22,430 --> 00:02:24,840
test my knowledge
of these topics.

65
00:02:24,840 --> 00:02:28,010
Then at the end, based
on my responses,

66
00:02:28,010 --> 00:02:30,050
go back and suggest

67
00:02:30,050 --> 00:02:31,870
the topics that I should

68
00:02:31,870 --> 00:02:33,580
focus on to improve
my understanding.

69
00:02:33,580 --> 00:02:35,470
Then I say, ask me
the first question.

70
00:02:35,470 --> 00:02:37,950
That asked me the first
question is a little trick,

71
00:02:37,950 --> 00:02:40,630
a prompt engineering
trick to make sure

72
00:02:40,630 --> 00:02:43,945
that we generate one
question at a time ideally.

73
00:02:43,945 --> 00:02:46,260
Now, we can also do something
like this that says,

74
00:02:46,260 --> 00:02:49,040
go and tell me something
to do one step at a time.

75
00:02:49,040 --> 00:02:50,960
If we were wanting to
use a coffee maker.

76
00:02:50,960 --> 00:02:53,180
We could say, tell me
one step at a time,

77
00:02:53,180 --> 00:02:55,740
what to do in order to
use the coffee maker.

78
00:02:55,740 --> 00:02:58,280
You know, I will tell
you what happened,

79
00:02:58,280 --> 00:03:00,170
and then you will
tell me what to do

80
00:03:00,170 --> 00:03:02,900
next.. Then tell me
the first thing to do.

81
00:03:02,900 --> 00:03:04,440
Similar type of thing that is we

82
00:03:04,440 --> 00:03:05,940
want it to drive
the interaction.

83
00:03:05,940 --> 00:03:07,270
We want to flip the script.

84
00:03:07,270 --> 00:03:10,140
It says, sure,

85
00:03:10,140 --> 00:03:13,080
let's break down the topic
of fishing into key areas,

86
00:03:13,080 --> 00:03:15,720
definition and basics,
types of fishing attacks,

87
00:03:15,720 --> 00:03:18,585
techniques used in
fishing, recognizing them.

88
00:03:18,585 --> 00:03:21,280
Then it says, question one,

89
00:03:21,280 --> 00:03:24,120
what is fishing, and what
is its primary goal?

90
00:03:24,120 --> 00:03:28,120
It's asked us the first
question and waits because it

91
00:03:28,120 --> 00:03:29,940
understands that it needs to ask

92
00:03:29,940 --> 00:03:33,025
a question and then
wait for our response.

93
00:03:33,025 --> 00:03:34,780
Then I go and I say, to trick

94
00:03:34,780 --> 00:03:36,340
a user into
performing an action,

95
00:03:36,340 --> 00:03:37,680
often giving up information,

96
00:03:37,680 --> 00:03:39,085
and that is my response.

97
00:03:39,085 --> 00:03:40,640
Then it says, Correct,

98
00:03:40,640 --> 00:03:42,560
phishing is a cyber
attack technique where

99
00:03:42,560 --> 00:03:44,320
the attackers trick individuals

100
00:03:44,320 --> 00:03:46,125
into revealing
sensitive information.

101
00:03:46,125 --> 00:03:47,820
Then it goes on to question two.

102
00:03:47,820 --> 00:03:50,435
What are some common types
of phishing attacks?

103
00:03:50,435 --> 00:03:52,760
It has looked at my answer,

104
00:03:52,760 --> 00:03:54,500
determined that I got it right,

105
00:03:54,500 --> 00:03:57,150
and then it's moved on
to the next question.

106
00:03:57,150 --> 00:04:00,020
I go and I answer, catching bass

107
00:04:00,020 --> 00:04:01,500
in a boat, deep sea fishing,

108
00:04:01,500 --> 00:04:03,640
and trawling, which is obviously

109
00:04:03,640 --> 00:04:07,230
the wrong type of fishing.
How does it respond?

110
00:04:07,230 --> 00:04:08,790
It says, it seems
like there might

111
00:04:08,790 --> 00:04:10,550
have been a mix up
with the question.

112
00:04:10,550 --> 00:04:12,690
In the context of cybersecurity,

113
00:04:12,690 --> 00:04:14,390
here are common types
of fishing attacks.

114
00:04:14,390 --> 00:04:15,910
So it sees that I'm mixed up on

115
00:04:15,910 --> 00:04:18,430
the concept that we're
talking about e mail fishing,

116
00:04:18,430 --> 00:04:21,010
and I've started talking
about actual phishing,

117
00:04:21,010 --> 00:04:23,510
catching fish, which
is a different thing,

118
00:04:23,510 --> 00:04:27,310
PH versus F. So it
says e mail fishing,

119
00:04:27,310 --> 00:04:29,850
spear fishing, whaling,
fishing, smishing.

120
00:04:29,850 --> 00:04:31,550
Tells me all these
different types,

121
00:04:31,550 --> 00:04:33,430
and then goes on to
the next question.

122
00:04:33,430 --> 00:04:35,450
What techniques do users do

123
00:04:35,450 --> 00:04:38,880
attackers use to make phishing
e mails appear legitimate?

124
00:04:38,880 --> 00:04:41,270
Notice it's following
its original outline

125
00:04:41,270 --> 00:04:44,235
in the process of the
questions that it's asking me.

126
00:04:44,235 --> 00:04:46,110
I come back and I
say sending from

127
00:04:46,110 --> 00:04:48,290
e mail addresses that look
like trusted sources,

128
00:04:48,290 --> 00:04:49,650
using language and e mail,

129
00:04:49,650 --> 00:04:51,515
design similar to
legitimate sources.

130
00:04:51,515 --> 00:04:54,235
Then it comes back and
it says, That's correct.

131
00:04:54,235 --> 00:04:56,830
Then it tells me all
of the techniques that

132
00:04:56,830 --> 00:04:58,810
it had in its head
of how this is done.

133
00:04:58,810 --> 00:05:00,230
Then it says, Question four.

134
00:05:00,230 --> 00:05:03,200
How can you recognize
a fishing attempt?

135
00:05:04,630 --> 00:05:07,530
But the key is that

136
00:05:07,530 --> 00:05:09,890
it is going and driving
the interaction.

137
00:05:09,890 --> 00:05:12,750
It is asking me the
questions one at a time,

138
00:05:12,750 --> 00:05:14,650
and based on my answer,

139
00:05:14,650 --> 00:05:17,320
it's deciding what to ask next.

140
00:05:17,320 --> 00:05:18,630
This is a really,

141
00:05:18,630 --> 00:05:22,630
really critical and amazing
capability is its ability to

142
00:05:22,630 --> 00:05:25,550
decide what should
be asked next and

143
00:05:25,550 --> 00:05:28,525
to respond and adapt based
on my understanding.

144
00:05:28,525 --> 00:05:30,470
When I didn't understand that it

145
00:05:30,470 --> 00:05:32,610
was talking about fishing
as an e mail fishing,

146
00:05:32,610 --> 00:05:34,510
I started talking
about actual fishing.

147
00:05:34,510 --> 00:05:36,070
I said, No, that's
not quite right.

148
00:05:36,070 --> 00:05:37,430
You're ale bit confused.

149
00:05:37,430 --> 00:05:39,345
Here's what I'm
actually talking about.

150
00:05:39,345 --> 00:05:42,710
Now, let's go and

151
00:05:42,710 --> 00:05:43,990
look at how we can still use

152
00:05:43,990 --> 00:05:45,250
flipped interaction another way.

153
00:05:45,250 --> 00:05:46,830
Certainly, we can
use it for practice

154
00:05:46,830 --> 00:05:47,970
and interactive practice,

155
00:05:47,970 --> 00:05:49,730
but we can also go and

156
00:05:49,730 --> 00:05:51,850
use it to teach us
one step at a time.

157
00:05:51,850 --> 00:05:53,550
Similar to how a teacher would,

158
00:05:53,550 --> 00:05:55,590
rather than just saying, explain

159
00:05:55,590 --> 00:05:57,660
everything to me all at once.

160
00:05:57,660 --> 00:06:00,665
We can say, teach it to
me one step at a time.

161
00:06:00,665 --> 00:06:03,930
I'm going to say, as an
expert in breaking down

162
00:06:03,930 --> 00:06:05,190
complex topics and teaching

163
00:06:05,190 --> 00:06:07,350
through simple analogies
and metaphors.

164
00:06:07,350 --> 00:06:11,150
Teach me how transformer models
work one step at a time.

165
00:06:11,150 --> 00:06:13,010
After you teach me each topic,

166
00:06:13,010 --> 00:06:15,610
ask me a question to
assess my understanding,

167
00:06:15,610 --> 00:06:18,010
adapt what you teach next or

168
00:06:18,010 --> 00:06:20,785
review based on how well
I answer the question.

169
00:06:20,785 --> 00:06:24,410
It's not just go and teach
me one step at a time,

170
00:06:24,410 --> 00:06:26,670
but ask me a question
at the end of

171
00:06:26,670 --> 00:06:29,390
teaching me something to
make sure I understand it,

172
00:06:29,390 --> 00:06:31,130
and then based on my response,

173
00:06:31,130 --> 00:06:33,065
decide what to do next.

174
00:06:33,065 --> 00:06:34,070
Then, again, I'm using

175
00:06:34,070 --> 00:06:35,630
this little prompt
engineering technique,

176
00:06:35,630 --> 00:06:37,005
teach me the first thing.

177
00:06:37,005 --> 00:06:38,805
It comes back and
it says, alright,

178
00:06:38,805 --> 00:06:40,090
let's dive into the first topic.

179
00:06:40,090 --> 00:06:42,190
Understanding words and context,

180
00:06:42,190 --> 00:06:43,990
concept, word embeddings.

181
00:06:43,990 --> 00:06:45,670
Imagine you're at
a party with a lot

182
00:06:45,670 --> 00:06:47,530
of people speaking
different languages.

183
00:06:47,530 --> 00:06:49,390
To understand what
someone is saying,

184
00:06:49,390 --> 00:06:50,990
you need more than just words,

185
00:06:50,990 --> 00:06:53,590
you need the context in
which they are used.

186
00:06:53,590 --> 00:06:56,155
Analogy, the word map.

187
00:06:56,155 --> 00:06:58,840
Then it goes on and it
says, question for you.

188
00:06:58,840 --> 00:07:01,800
If cat and dog are close
together on the word map,

189
00:07:01,800 --> 00:07:03,600
what does that tell
us about these words

190
00:07:03,600 --> 00:07:05,880
in terms of meaning or context?

191
00:07:05,880 --> 00:07:09,640
It's taken the concept
of embeddings.

192
00:07:09,640 --> 00:07:15,780
It's translated that into
this analogy of word maps.

193
00:07:15,780 --> 00:07:18,040
Then, it's asking me

194
00:07:18,040 --> 00:07:21,430
a question based on
what it just taught me.

195
00:07:21,430 --> 00:07:24,010
I say, Okay,

196
00:07:24,010 --> 00:07:26,500
that would mean that they have

197
00:07:26,500 --> 00:07:28,680
similar semantics
or similar meaning,

198
00:07:28,680 --> 00:07:30,260
because that's the
idea of embeddings.

199
00:07:30,260 --> 00:07:32,740
If you have two words, they're
sort of close together.

200
00:07:32,740 --> 00:07:34,560
They're supposed to
have similar meaning

201
00:07:34,560 --> 00:07:37,740
and it says exactly cat and
dog being close together.

202
00:07:37,740 --> 00:07:39,950
Then it goes on topic
two, attention mechanism.

203
00:07:39,950 --> 00:07:42,430
It gives me an analogy,
the spotlight.

204
00:07:42,430 --> 00:07:45,410
It then goes on and it
says, Question for you.

205
00:07:45,410 --> 00:07:47,910
Why might the attention
mechanism be important when

206
00:07:47,910 --> 00:07:49,070
interpreting a sentence like

207
00:07:49,070 --> 00:07:51,050
the cat which I
adopted last year,

208
00:07:51,050 --> 00:07:53,440
loves to play with my dog.

209
00:07:53,440 --> 00:07:55,300
Then I go and respond
and I say, okay,

210
00:07:55,300 --> 00:07:57,150
not all cats like
to play with dogs.

211
00:07:57,150 --> 00:07:59,410
The fact that this
is the cat which

212
00:07:59,410 --> 00:08:02,670
I adopted last year
is important context.

213
00:08:02,670 --> 00:08:06,400
It goes on and it says,
exactly, that's right.

214
00:08:06,400 --> 00:08:08,370
It gives me another analogy.

215
00:08:08,370 --> 00:08:09,980
Then it goes through it

216
00:08:09,980 --> 00:08:12,540
again and asks me a
question about it.

217
00:08:12,540 --> 00:08:16,140
What we're seeing here
is the ability to

218
00:08:16,140 --> 00:08:21,160
have it interactively and
adaptively work with us,

219
00:08:21,160 --> 00:08:22,900
either questioning us for

220
00:08:22,900 --> 00:08:26,800
practice or interactively
teaching us something

221
00:08:26,800 --> 00:08:29,940
and then testing our knowledge
of what it just taught

222
00:08:29,940 --> 00:08:33,645
us and adapt based
on our responses.

223
00:08:33,645 --> 00:08:36,515
We do this through the
flipped interaction pattern.

224
00:08:36,515 --> 00:08:40,640
The basic structure of this
pattern is fairly simple.

225
00:08:40,640 --> 00:08:43,410
What we say is, ask

226
00:08:43,410 --> 00:08:46,875
me questions one at a time in
order to achieve this goal.

227
00:08:46,875 --> 00:08:49,340
In this case, we had asked
me questions at a one

228
00:08:49,340 --> 00:08:51,660
at a time to assess my
knowledge of these topics,

229
00:08:51,660 --> 00:08:53,965
and these topics
were on fishing.

230
00:08:53,965 --> 00:08:57,380
Then we say, you know,

231
00:08:57,380 --> 00:08:59,480
ask me the first question.

232
00:08:59,480 --> 00:09:01,305
Teach me the first thing.

233
00:09:01,305 --> 00:09:04,040
The key is is we
say, ask questions.

234
00:09:04,040 --> 00:09:06,040
Now, we could have it
ask a bunch at a time,

235
00:09:06,040 --> 00:09:07,480
but I think it's most effective

236
00:09:07,480 --> 00:09:08,800
when we do this one at a time.

237
00:09:08,800 --> 00:09:11,160
Ask me one at a
time, in order to

238
00:09:11,160 --> 00:09:13,680
achieve this goal in order
to teach me this thing in

239
00:09:13,680 --> 00:09:15,320
order to test my knowledge of

240
00:09:15,320 --> 00:09:19,220
this concept in order to
whatever it is you want,

241
00:09:19,220 --> 00:09:21,260
and you're basically telling it,

242
00:09:21,260 --> 00:09:23,200
we're going to do this
one step at a time.

243
00:09:23,200 --> 00:09:25,050
You're going to ask
me the questions.

244
00:09:25,050 --> 00:09:26,640
You're going to look
at my response,

245
00:09:26,640 --> 00:09:27,460
and you're going to adapt.

246
00:09:27,460 --> 00:09:28,640
We're going to go
one sep at a time,

247
00:09:28,640 --> 00:09:30,040
and you're going to
teach me something,

248
00:09:30,040 --> 00:09:32,180
like how to use
that coffee maker

249
00:09:32,180 --> 00:09:35,610
or how transformer models work.

250
00:09:35,610 --> 00:09:37,670
Then you're going
to adapt based on

251
00:09:37,670 --> 00:09:39,720
the feedback that I give you.

252
00:09:39,720 --> 00:09:40,790
Then we use this simple little

253
00:09:40,790 --> 00:09:42,170
prompt engineering technique,

254
00:09:42,170 --> 00:09:44,710
ask me the first question
to get it headed

255
00:09:44,710 --> 00:09:45,990
off in the direction of asking

256
00:09:45,990 --> 00:09:47,270
these questions one at a time,

257
00:09:47,270 --> 00:09:50,290
rather than ten at a
time and overwhelming.