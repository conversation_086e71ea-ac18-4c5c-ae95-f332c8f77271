1
00:00:00,000 --> 00:00:02,480
Let's go back to something
that <PERSON><PERSON> talked about,

2
00:00:02,480 --> 00:00:04,540
that's really
important, which is,

3
00:00:04,540 --> 00:00:06,560
if when we're learning
a new concept,

4
00:00:06,560 --> 00:00:08,180
we can connect it to links

5
00:00:08,180 --> 00:00:09,420
that we already
have in the brain,

6
00:00:09,420 --> 00:00:12,560
it can improve how we learn.

7
00:00:13,850 --> 00:00:19,995
How do we use this idea of
translation to tap into that?

8
00:00:19,995 --> 00:00:21,700
Barb talked about this,

9
00:00:21,700 --> 00:00:23,460
this idea that we
use it to give us

10
00:00:23,460 --> 00:00:25,180
different metaphors analogies to

11
00:00:25,180 --> 00:00:26,920
help us understand something.

12
00:00:26,920 --> 00:00:28,880
Now, one of the things
that you'll hear me talk

13
00:00:28,880 --> 00:00:31,180
a lot about is the
concept of pattern.

14
00:00:31,180 --> 00:00:33,040
When we go and write a prompt,

15
00:00:33,040 --> 00:00:35,300
prompting is like
text messaging.

16
00:00:35,300 --> 00:00:36,600
You type in a message,

17
00:00:36,600 --> 00:00:38,680
and the message is
like the text message

18
00:00:38,680 --> 00:00:39,760
you would send to a friend,

19
00:00:39,760 --> 00:00:41,160
and your friend
then would go and

20
00:00:41,160 --> 00:00:43,520
respond to your text
message and text you back,

21
00:00:43,520 --> 00:00:45,320
except in this case, we send

22
00:00:45,320 --> 00:00:47,420
something called a prompt,
which is our message.

23
00:00:47,420 --> 00:00:48,620
Then the AI,

24
00:00:48,620 --> 00:00:50,200
the generative AI model goes and

25
00:00:50,200 --> 00:00:52,100
texts us back the response.

26
00:00:52,100 --> 00:00:53,800
We call that output,

27
00:00:53,800 --> 00:00:55,555
or we call it a response.

28
00:00:55,555 --> 00:00:59,230
The key is that just
like with humans,

29
00:00:59,230 --> 00:01:00,670
we have certain structures and

30
00:01:00,670 --> 00:01:03,950
patterns that we use for
certain types of communication.

31
00:01:03,950 --> 00:01:06,050
We have the same type of
things with these models.

32
00:01:06,050 --> 00:01:08,790
For example, if I'm going
to write a formal letter,

33
00:01:08,790 --> 00:01:10,670
I will go and say,
dear, so and so,

34
00:01:10,670 --> 00:01:13,850
comma, and there's a reason
behind that pattern.

35
00:01:13,850 --> 00:01:16,190
The deer indicates respect,

36
00:01:16,190 --> 00:01:17,850
then we have the
person's name so

37
00:01:17,850 --> 00:01:19,950
that we know who the
letter is addressed to,

38
00:01:19,950 --> 00:01:21,290
and then we have a comma to

39
00:01:21,290 --> 00:01:23,650
separate that from the
rest of the letter.

40
00:01:23,650 --> 00:01:25,930
That pattern has a
purpose behind it,

41
00:01:25,930 --> 00:01:28,170
and we're going to see
different patterns

42
00:01:28,170 --> 00:01:29,650
that we can put into
our prompts that have

43
00:01:29,650 --> 00:01:32,030
a specific purpose
to help us achieve

44
00:01:32,030 --> 00:01:35,125
a particular effect with
the large language model.

45
00:01:35,125 --> 00:01:37,190
That's what we're going
to be looking at,

46
00:01:37,190 --> 00:01:38,010
is the first one of

47
00:01:38,010 --> 00:01:39,350
these patterns that
we're going to use,

48
00:01:39,350 --> 00:01:41,125
which is the metaphor pattern.

49
00:01:41,125 --> 00:01:45,310
What we want to do is we
want to get the model to

50
00:01:45,310 --> 00:01:47,050
explain things to us

51
00:01:47,050 --> 00:01:50,360
using metaphors and analogies
that we understand well.

52
00:01:50,360 --> 00:01:52,450
Now, we've already seen

53
00:01:52,450 --> 00:01:54,115
that it can go and
take concepts,

54
00:01:54,115 --> 00:01:55,540
like my name is Jules White,

55
00:01:55,540 --> 00:01:57,150
and translate it into a stack of

56
00:01:57,150 --> 00:01:59,750
rocks so it seems
natural that we should

57
00:01:59,750 --> 00:02:01,670
assume that we can go and say

58
00:02:01,670 --> 00:02:04,610
translate this concept into
metaphors and analogies.

59
00:02:04,610 --> 00:02:07,310
The metaphors and analogies
are our rocks that we

60
00:02:07,310 --> 00:02:10,270
want it to now stack
and explain things,

61
00:02:10,270 --> 00:02:12,390
and build ideas out
of these rocks,

62
00:02:12,390 --> 00:02:13,270
which are now going to be

63
00:02:13,270 --> 00:02:15,360
metaphors and analogies
that we know.

64
00:02:15,360 --> 00:02:18,515
Let's look at an
example of this.

65
00:02:18,515 --> 00:02:21,170
This pattern is explain

66
00:02:21,170 --> 00:02:24,190
some topic using
metaphors and analogies,

67
00:02:24,190 --> 00:02:25,630
and then we give it
some information

68
00:02:25,630 --> 00:02:29,110
about what metaphors
and analogies to use.

69
00:02:29,690 --> 00:02:32,475
Let's take a first example.

70
00:02:32,475 --> 00:02:34,550
Barb talked about this idea of

71
00:02:34,550 --> 00:02:35,850
thinking about large language

72
00:02:35,850 --> 00:02:36,890
models in their development,

73
00:02:36,890 --> 00:02:38,640
kind of like car engines.

74
00:02:38,640 --> 00:02:41,290
Even though they're
advancing so rapidly,

75
00:02:41,290 --> 00:02:42,630
we still go and learn
about it because

76
00:02:42,630 --> 00:02:44,430
once you learn to drive,

77
00:02:44,430 --> 00:02:46,010
you have the basic concepts.

78
00:02:46,010 --> 00:02:48,405
Even if there's a V8 that
comes out next month,

79
00:02:48,405 --> 00:02:50,030
or a V10 or something

80
00:02:50,030 --> 00:02:51,870
else or we get to
an electric engine,

81
00:02:51,870 --> 00:02:55,065
we're still driving a car.
That's really important.

82
00:02:55,065 --> 00:02:56,670
We're still driving

83
00:02:56,670 --> 00:02:59,000
generative AI when we're
learning about it.

84
00:02:59,000 --> 00:03:01,465
So we're going to
go back to this

85
00:03:01,465 --> 00:03:03,510
and we're going to look at,

86
00:03:03,510 --> 00:03:10,095
can ChatGPT explain this using
a car and engine analogy?

87
00:03:10,095 --> 00:03:12,800
Explain why we should
learn about generative AI,

88
00:03:12,800 --> 00:03:15,520
even though the models
are changing so rapidly.

89
00:03:15,520 --> 00:03:17,800
Use a car and engine analogy.

90
00:03:17,800 --> 00:03:19,660
I'm setting it up to

91
00:03:19,660 --> 00:03:22,860
explain things very similarly
to the way I just did.

92
00:03:22,860 --> 00:03:24,950
What does ChatGPT say?

93
00:03:24,950 --> 00:03:27,460
It comes back and it says,
learning about generative AI,

94
00:03:27,460 --> 00:03:28,740
even though models are changing

95
00:03:28,740 --> 00:03:31,380
rapidly is essential
for understanding

96
00:03:31,380 --> 00:03:33,340
the underlying principles and

97
00:03:33,340 --> 00:03:35,700
potential applications
of this technology.

98
00:03:35,700 --> 00:03:38,420
Using a car analogy
can help illustrate.

99
00:03:38,420 --> 00:03:40,520
Car analogy, learning to drive

100
00:03:40,520 --> 00:03:41,660
involves understanding

101
00:03:41,660 --> 00:03:43,300
fundamental principles
like steering,

102
00:03:43,300 --> 00:03:44,660
acceleration, and braking,

103
00:03:44,660 --> 00:03:46,880
these principles
apply regardless

104
00:03:46,880 --> 00:03:48,100
of the car model or brand.

105
00:03:48,100 --> 00:03:49,640
It's basically telling you

106
00:03:49,640 --> 00:03:52,480
exactly what Barb told
you and what I told you,

107
00:03:52,480 --> 00:03:53,760
which is that, yes,

108
00:03:53,760 --> 00:03:56,615
these things are
changing very rapidly,

109
00:03:56,615 --> 00:03:59,700
but once we understand those
fundamental core principles,

110
00:03:59,700 --> 00:04:01,620
we can now go and apply them,

111
00:04:01,620 --> 00:04:03,465
even as the car changes.

112
00:04:03,465 --> 00:04:05,220
Car models change over time,

113
00:04:05,220 --> 00:04:07,100
introducing new
technologies like

114
00:04:07,100 --> 00:04:09,500
hybrid engines or
advanced safety features.

115
00:04:09,500 --> 00:04:12,500
If you understand how engines
and car systems work,

116
00:04:12,500 --> 00:04:14,480
adapting these changes becomes

117
00:04:14,480 --> 00:04:16,660
easier. Now, what has it done?

118
00:04:16,660 --> 00:04:18,920
It's basically
stacked rocks again,

119
00:04:18,920 --> 00:04:25,000
except the rocks it stacked
are concepts from cars.

120
00:04:25,000 --> 00:04:27,480
It's stacked up the
ideas related to

121
00:04:27,480 --> 00:04:30,180
engines and driving and
acceleration and braking,

122
00:04:30,180 --> 00:04:33,820
and it's organized them
into different examples and

123
00:04:33,820 --> 00:04:35,920
analogies and metaphors that

124
00:04:35,920 --> 00:04:37,580
help me to understand
the concept,

125
00:04:37,580 --> 00:04:39,640
that it has done translation

126
00:04:39,640 --> 00:04:43,020
into a language that I've
given it rules around.

127
00:04:43,020 --> 00:04:45,400
The language that I've
given it, as I've told it,

128
00:04:45,400 --> 00:04:47,080
go and translate it
into the language of

129
00:04:47,080 --> 00:04:49,420
metaphors and analogies
related to something I know.

130
00:04:49,420 --> 00:04:52,540
This is what the metaphor
pattern is doing is

131
00:04:52,540 --> 00:04:57,190
it's allowing us to essentially
control that translation.

132
00:04:57,220 --> 00:05:01,220
Now, in this example, I
led it very strongly.

133
00:05:01,220 --> 00:05:03,260
I already knew the analogy.

134
00:05:03,260 --> 00:05:05,300
I already knew the metaphor.

135
00:05:05,300 --> 00:05:07,040
I already knew the domain that I

136
00:05:07,040 --> 00:05:08,700
wanted it to draw examples from.

137
00:05:08,700 --> 00:05:10,960
It was already
highly restricted.

138
00:05:10,960 --> 00:05:15,040
What happens when we don't
know what the target is?

139
00:05:15,040 --> 00:05:17,480
Well, what we want
to do is we want to

140
00:05:17,480 --> 00:05:20,220
step back and think
about what the goal is.

141
00:05:20,220 --> 00:05:23,900
The goal at the end of the
day is to have it explain

142
00:05:23,900 --> 00:05:25,580
things using metaphors
and analogies

143
00:05:25,580 --> 00:05:27,730
that relate to what we
know about already.

144
00:05:27,730 --> 00:05:29,420
And so one of the
things that we can

145
00:05:29,420 --> 00:05:32,080
do is rather than
explicitly say,

146
00:05:32,080 --> 00:05:33,680
these are the metaphors or

147
00:05:33,680 --> 00:05:36,080
analogies or examples
that I want you to use,

148
00:05:36,080 --> 00:05:38,485
we then tell it
what we know well.

149
00:05:38,485 --> 00:05:40,380
This is an example

150
00:05:40,380 --> 00:05:42,840
where when we're filling in
that bracket at the bottom,

151
00:05:42,840 --> 00:05:44,400
where we're telling
it what rocks

152
00:05:44,400 --> 00:05:46,715
it has to work with
and it stacks,

153
00:05:46,715 --> 00:05:48,750
what we can instead
do is tell it,

154
00:05:48,750 --> 00:05:50,790
here's what I know
about and draw

155
00:05:50,790 --> 00:05:53,510
from that area of knowledge
that I already have.

156
00:05:53,510 --> 00:05:55,550
Whenever you explain
things to me,

157
00:05:55,550 --> 00:05:57,050
relate it to analogies and

158
00:05:57,050 --> 00:05:58,610
metaphors that I
will understand.

159
00:05:58,610 --> 00:06:00,330
I know a lot about cars.

160
00:06:00,330 --> 00:06:02,790
Explain why we should
learn about generative AI,

161
00:06:02,790 --> 00:06:05,045
even though the models
are changing so rapidly.

162
00:06:05,045 --> 00:06:07,030
i have removed the engine.

163
00:06:07,030 --> 00:06:08,770
I've removed these other

164
00:06:08,770 --> 00:06:10,950
direct references
from the prompt,

165
00:06:10,950 --> 00:06:13,130
and instead, I've told
it, this is what I know.

166
00:06:13,130 --> 00:06:17,515
Make sure it is something
that I know about.

167
00:06:17,515 --> 00:06:21,140
It comes back and it says,
fundamentals are key,

168
00:06:21,140 --> 00:06:23,580
as knowing the basics of
how a combustion engine

169
00:06:23,580 --> 00:06:25,990
works helps you
understand any car,

170
00:06:25,990 --> 00:06:27,760
learning the foundational
principles of

171
00:06:27,760 --> 00:06:30,680
generative AI equips you
to grasp future models.

172
00:06:30,680 --> 00:06:32,720
The core concepts
like machine learning

173
00:06:32,720 --> 00:06:34,360
and neural networks
remain consistent,

174
00:06:34,360 --> 00:06:37,230
even as specific
implementations change.

175
00:06:37,230 --> 00:06:39,520
It's still giving
me car analogies,

176
00:06:39,520 --> 00:06:41,040
but now it's actually changed

177
00:06:41,040 --> 00:06:42,960
the examples that
it's giving me,

178
00:06:42,960 --> 00:06:46,360
and it's talking
about now combustion.

179
00:06:46,360 --> 00:06:48,820
It's talking about
how understanding

180
00:06:48,820 --> 00:06:50,140
how cars function allows you

181
00:06:50,140 --> 00:06:51,680
to maintain them better and make

182
00:06:51,680 --> 00:06:53,320
informed decisions
about upgrades,

183
00:06:53,320 --> 00:06:55,300
and it's telling me why,
but it's using it from

184
00:06:55,300 --> 00:06:57,720
the domain of cars.

185
00:06:57,720 --> 00:07:00,680
I didn't have to tell it what
to pull from that domain.

186
00:07:00,680 --> 00:07:03,780
I'm just telling it this
is the raw material,

187
00:07:03,780 --> 00:07:06,315
the types of concepts
I understand well.

188
00:07:06,315 --> 00:07:08,480
Those concepts that I understand

189
00:07:08,480 --> 00:07:10,680
well are the rocks that's
going to stack with.

190
00:07:10,680 --> 00:07:12,780
Those are the rocks
that's going to translate

191
00:07:12,780 --> 00:07:15,460
my name is Jules White
into, or in this case,

192
00:07:15,460 --> 00:07:17,980
it's going to
translate, you know,

193
00:07:17,980 --> 00:07:20,380
all about generative AI and

194
00:07:20,380 --> 00:07:21,960
why it's changing and how

195
00:07:21,960 --> 00:07:23,460
we still want to go
and learn about it.

196
00:07:23,460 --> 00:07:25,640
But now it's
translated into rocks

197
00:07:25,640 --> 00:07:29,560
drawn from the idea of
the automotive domain.