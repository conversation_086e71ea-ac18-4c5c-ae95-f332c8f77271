1
00:00:00,000 --> 00:00:02,480
let ' s 去 back 到 something 那个 芭芭 talked 关于 ,

2
00:00:02,480 --> 00:00:04,540
那个 ' s 真的 重要的 , 哪个 是 ,

3
00:00:04,540 --> 00:00:06,560
如果 当 我们 ' re 学习 一个 新的 概念 ,

4
00:00:06,560 --> 00:00:08,180
我们 能 connect 它 到 links

5
00:00:08,180 --> 00:00:09,420
那个 我们 already 有 在 这个 大脑 ,

6
00:00:09,420 --> 00:00:12,560
它 能 improve 如何 我们 学习 .

7
00:00:13,850 --> 00:00:19,995
如何 做 我们 使用 这个 想法 的 translation 到 tap 进入 那个 ?

8
00:00:19,995 --> 00:00:21,700
芭芭 talked 关于 这个 ,

9
00:00:21,700 --> 00:00:23,460
这个 想法 那个 我们 使用 它 到 给 我们

10
00:00:23,460 --> 00:00:25,180
不同的 metaphors analogies 到

11
00:00:25,180 --> 00:00:26,920
帮助 我们 理解 something .

12
00:00:26,920 --> 00:00:28,880
现在 , one 的 这个 things 那个 你 ' ll hear 我 talk

13
00:00:28,880 --> 00:00:31,180
一个 lot 关于 是 这个 概念 的 pattern .

14
00:00:31,180 --> 00:00:33,040
当 我们 去 和 write 一个 提示 ,

15
00:00:33,040 --> 00:00:35,300
提示 是 like text messaging .

16
00:00:35,300 --> 00:00:36,600
你 type 在 一个 message ,

17
00:00:36,600 --> 00:00:38,680
和 这个 message 是 like 这个 text message

18
00:00:38,680 --> 00:00:39,760
你 会 send 到 一个 friend ,

19
00:00:39,760 --> 00:00:41,160
和 你的 friend 然后 会 去 和

20
00:00:41,160 --> 00:00:43,520
respond 到 你的 text message 和 text 你 back ,

21
00:00:43,520 --> 00:00:45,320
except 在 这个 case , 我们 send

22
00:00:45,320 --> 00:00:47,420
something called 一个 提示 , 哪个 是 我们的 message .

23
00:00:47,420 --> 00:00:48,620
然后 这个 AI ,

24
00:00:48,620 --> 00:00:50,200
这个 生成式 AI 模型 goes 和

25
00:00:50,200 --> 00:00:52,100
texts 我们 back 这个 response .

26
00:00:52,100 --> 00:00:53,800
我们 call 那个 output ,

27
00:00:53,800 --> 00:00:55,555
或 我们 call 它 一个 response .

28
00:00:55,555 --> 00:00:59,230
这个 key 是 那个 just like 与 humans ,

29
00:00:59,230 --> 00:01:00,670
我们 有 certain structures 和

30
00:01:00,670 --> 00:01:03,950
patterns 那个 我们 使用 为了 certain types 的 communication .

31
00:01:03,950 --> 00:01:06,050
我们 有 这个 相同的 type 的 things 与 这些 models .

32
00:01:06,050 --> 00:01:08,790
为了 例子 , 如果 我 ' m going 到 write 一个 formal letter ,

33
00:01:08,790 --> 00:01:10,670
我 将 去 和 说 , dear , 所以 和 所以 ,

34
00:01:10,670 --> 00:01:13,850
comma , 和 那里 ' s 一个 reason behind 那个 pattern .

35
00:01:13,850 --> 00:01:16,190
这个 deer indicates respect ,

36
00:01:16,190 --> 00:01:17,850
然后 我们 有 这个 person ' s name 所以

37
00:01:17,850 --> 00:01:19,950
那个 我们 知道 谁 这个 letter 是 addressed 到 ,

38
00:01:19,950 --> 00:01:21,290
和 然后 我们 有 一个 comma 到

39
00:01:21,290 --> 00:01:23,650
separate 那个 从 这个 rest 的 这个 letter .

40
00:01:23,650 --> 00:01:25,930
那个 pattern 有 一个 purpose behind 它 ,

41
00:01:25,930 --> 00:01:28,170
和 我们 ' re going 到 看 不同的 patterns

42
00:01:28,170 --> 00:01:29,650
那个 我们 能 put 进入 我们的 prompts 那个 有

43
00:01:29,650 --> 00:01:32,030
一个 specific purpose 到 帮助 我们 achieve

44
00:01:32,030 --> 00:01:35,125
一个 particular effect 与 这个 large 语言 模型 .

45
00:01:35,125 --> 00:01:37,190
那个 ' s 什么 我们 ' re going 到 是 looking 在 ,

46
00:01:37,190 --> 00:01:38,010
是 这个 first one 的

47
00:01:38,010 --> 00:01:39,350
这些 patterns 那个 我们 ' re going 到 使用 ,

48
00:01:39,350 --> 00:01:41,125
哪个 是 这个 metaphor pattern .

49
00:01:41,125 --> 00:01:45,310
什么 我们 want 到 做 是 我们 want 到 得到 这个 模型 到

50
00:01:45,310 --> 00:01:47,050
解释 things 到 我们

51
00:01:47,050 --> 00:01:50,360
using metaphors 和 analogies 那个 我们 理解 well .

52
00:01:50,360 --> 00:01:52,450
现在 , 我们 ' ve already seen

53
00:01:52,450 --> 00:01:54,115
那个 它 能 去 和 拿 concepts ,

54
00:01:54,115 --> 00:01:55,540
like 我的 name 是 朱尔斯 怀特 ,

55
00:01:55,540 --> 00:01:57,150
和 translate 它 进入 一个 stack 的

56
00:01:57,150 --> 00:01:59,750
rocks 所以 它 seems natural 那个 我们 应该

57
00:01:59,750 --> 00:02:01,670
assume 那个 我们 能 去 和 说

58
00:02:01,670 --> 00:02:04,610
translate 这个 概念 进入 metaphors 和 analogies .

59
00:02:04,610 --> 00:02:07,310
这个 metaphors 和 analogies 是 我们的 rocks 那个 我们

60
00:02:07,310 --> 00:02:10,270
want 它 到 现在 stack 和 解释 things ,

61
00:02:10,270 --> 00:02:12,390
和 build ideas out 的 这些 rocks ,

62
00:02:12,390 --> 00:02:13,270
哪个 是 现在 going 到 是

63
00:02:13,270 --> 00:02:15,360
metaphors 和 analogies 那个 我们 知道 .

64
00:02:15,360 --> 00:02:18,515
let ' s look 在 一个 例子 的 这个 .

65
00:02:18,515 --> 00:02:21,170
这个 pattern 是 解释

66
00:02:21,170 --> 00:02:24,190
一些 主题 using metaphors 和 analogies ,

67
00:02:24,190 --> 00:02:25,630
和 然后 我们 给 它 一些 信息

68
00:02:25,630 --> 00:02:29,110
关于 什么 metaphors 和 analogies 到 使用 .

69
00:02:29,690 --> 00:02:32,475
let ' s 拿 一个 first 例子 .

70
00:02:32,475 --> 00:02:34,550
芭芭 talked 关于 这个 想法 的

71
00:02:34,550 --> 00:02:35,850
思考 关于 large 语言

72
00:02:35,850 --> 00:02:36,890
models 在 他们的 development ,

73
00:02:36,890 --> 00:02:38,640
kind 的 like car engines .

74
00:02:38,640 --> 00:02:41,290
even though 他们 ' re advancing 所以 rapidly ,

75
00:02:41,290 --> 00:02:42,630
我们 still 去 和 学习 关于 它 因为

76
00:02:42,630 --> 00:02:44,430
once 你 学习 到 drive ,

77
00:02:44,430 --> 00:02:46,010
你 有 这个 basic concepts .

78
00:02:46,010 --> 00:02:48,405
even 如果 那里 ' s 一个 v8 那个 comes out next month ,

79
00:02:48,405 --> 00:02:50,030
或 一个 v10 或 something

80
00:02:50,030 --> 00:02:51,870
else 或 我们 得到 到 一个 electric engine ,

81
00:02:51,870 --> 00:02:55,065
我们 ' re still driving 一个 car . 那个 ' s 真的 重要的 .

82
00:02:55,065 --> 00:02:56,670
我们 ' re still driving

83
00:02:56,670 --> 00:02:59,000
生成式 AI 当 我们 ' re 学习 关于 它 .

84
00:02:59,000 --> 00:03:01,465
所以 我们 ' re going 到 去 back 到 这个

85
00:03:01,465 --> 00:03:03,510
和 我们 ' re going 到 look 在 ,

86
00:03:03,510 --> 00:03:10,095
能 ChatGPT 解释 这个 using 一个 car 和 engine analogy ?

87
00:03:10,095 --> 00:03:12,800
解释 为什么 我们 应该 学习 关于 生成式 AI ,

88
00:03:12,800 --> 00:03:15,520
even though 这个 models 是 changing 所以 rapidly .

89
00:03:15,520 --> 00:03:17,800
使用 一个 car 和 engine analogy .

90
00:03:17,800 --> 00:03:19,660
我 ' m setting 它 上 到

91
00:03:19,660 --> 00:03:22,860
解释 things 非常 similarly 到 这个 方式 我 just 做 .

92
00:03:22,860 --> 00:03:24,950
什么 做 ChatGPT 说 ?

93
00:03:24,950 --> 00:03:27,460
它 comes back 和 它 says , 学习 关于 生成式 AI ,

94
00:03:27,460 --> 00:03:28,740
even though models 是 changing

95
00:03:28,740 --> 00:03:31,380
rapidly 是 essential 为了 理解

96
00:03:31,380 --> 00:03:33,340
这个 underlying principles 和

97
00:03:33,340 --> 00:03:35,700
potential applications 的 这个 技术 .

98
00:03:35,700 --> 00:03:38,420
using 一个 car analogy 能 帮助 illustrate .

99
00:03:38,420 --> 00:03:40,520
car analogy , 学习 到 drive

100
00:03:40,520 --> 00:03:41,660
involves 理解

101
00:03:41,660 --> 00:03:43,300
fundamental principles like steering ,

102
00:03:43,300 --> 00:03:44,660
acceleration , 和 braking ,

103
00:03:44,660 --> 00:03:46,880
这些 principles apply regardless

104
00:03:46,880 --> 00:03:48,100
的 这个 car 模型 或 brand .

105
00:03:48,100 --> 00:03:49,640
它 ' s basically telling 你

106
00:03:49,640 --> 00:03:52,480
exactly 什么 芭芭 told 你 和 什么 我 told 你 ,

107
00:03:52,480 --> 00:03:53,760
哪个 是 那个 , 是的 ,

108
00:03:53,760 --> 00:03:56,615
这些 things 是 changing 非常 rapidly ,

109
00:03:56,615 --> 00:03:59,700
但是 once 我们 理解 那些 fundamental core principles ,

110
00:03:59,700 --> 00:04:01,620
我们 能 现在 去 和 apply 他们 ,

111
00:04:01,620 --> 00:04:03,465
even as 这个 car changes .

112
00:04:03,465 --> 00:04:05,220
car models change over time ,

113
00:04:05,220 --> 00:04:07,100
introducing 新的 technologies like

114
00:04:07,100 --> 00:04:09,500
hybrid engines 或 advanced safety features .

115
00:04:09,500 --> 00:04:12,500
如果 你 理解 如何 engines 和 car systems 工作 ,

116
00:04:12,500 --> 00:04:14,480
adapting 这些 changes becomes

117
00:04:14,480 --> 00:04:16,660
easier . 现在 , 什么 有 它 done ?

118
00:04:16,660 --> 00:04:18,920
它 ' s basically stacked rocks again ,

119
00:04:18,920 --> 00:04:25,000
except 这个 rocks 它 stacked 是 concepts 从 cars .

120
00:04:25,000 --> 00:04:27,480
它 ' s stacked 上 这个 ideas related 到

121
00:04:27,480 --> 00:04:30,180
engines 和 driving 和 acceleration 和 braking ,

122
00:04:30,180 --> 00:04:33,820
和 它 ' s organized 他们 进入 不同的 examples 和

123
00:04:33,820 --> 00:04:35,920
analogies 和 metaphors 那个

124
00:04:35,920 --> 00:04:37,580
帮助 我 到 理解 这个 概念 ,

125
00:04:37,580 --> 00:04:39,640
那个 它 有 done translation

126
00:04:39,640 --> 00:04:43,020
进入 一个 语言 那个 我 ' ve given 它 rules around .

127
00:04:43,020 --> 00:04:45,400
这个 语言 那个 我 ' ve given 它 , as 我 ' ve told 它 ,

128
00:04:45,400 --> 00:04:47,080
去 和 translate 它 进入 这个 语言 的

129
00:04:47,080 --> 00:04:49,420
metaphors 和 analogies related 到 something 我 知道 .

130
00:04:49,420 --> 00:04:52,540
这个 是 什么 这个 metaphor pattern 是 doing 是

131
00:04:52,540 --> 00:04:57,190
它 ' s allowing 我们 到 essentially control 那个 translation .

132
00:04:57,220 --> 00:05:01,220
现在 , 在 这个 例子 , 我 led 它 非常 strongly .

133
00:05:01,220 --> 00:05:03,260
我 already knew 这个 analogy .

134
00:05:03,260 --> 00:05:05,300
我 already knew 这个 metaphor .

135
00:05:05,300 --> 00:05:07,040
我 already knew 这个 domain 那个 我

136
00:05:07,040 --> 00:05:08,700
wanted 它 到 draw examples 从 .

137
00:05:08,700 --> 00:05:10,960
它 是 already highly restricted .

138
00:05:10,960 --> 00:05:15,040
什么 happens 当 我们 don ' t 知道 什么 这个 target 是 ?

139
00:05:15,040 --> 00:05:17,480
well , 什么 我们 want 到 做 是 我们 want 到

140
00:05:17,480 --> 00:05:20,220
step back 和 想 关于 什么 这个 goal 是 .

141
00:05:20,220 --> 00:05:23,900
这个 goal 在 这个 end 的 这个 day 是 到 有 它 解释

142
00:05:23,900 --> 00:05:25,580
things using metaphors 和 analogies

143
00:05:25,580 --> 00:05:27,730
那个 relate 到 什么 我们 知道 关于 already .

144
00:05:27,730 --> 00:05:29,420
和 所以 one 的 这个 things 那个 我们 能

145
00:05:29,420 --> 00:05:32,080
做 是 rather than explicitly 说 ,

146
00:05:32,080 --> 00:05:33,680
这些 是 这个 metaphors 或

147
00:05:33,680 --> 00:05:36,080
analogies 或 examples 那个 我 want 你 到 使用 ,

148
00:05:36,080 --> 00:05:38,485
我们 然后 告诉 它 什么 我们 知道 well .

149
00:05:38,485 --> 00:05:40,380
这个 是 一个 例子

150
00:05:40,380 --> 00:05:42,840
哪里 当 我们 ' re filling 在 那个 bracket 在 这个 bottom ,

151
00:05:42,840 --> 00:05:44,400
哪里 我们 ' re telling 它 什么 rocks

152
00:05:44,400 --> 00:05:46,715
它 有 到 工作 与 和 它 stacks ,

153
00:05:46,715 --> 00:05:48,750
什么 我们 能 instead 做 是 告诉 它 ,

154
00:05:48,750 --> 00:05:50,790
这里 ' s 什么 我 知道 关于 和 draw

155
00:05:50,790 --> 00:05:53,510
从 那个 area 的 知识 那个 我 already 有 .

156
00:05:53,510 --> 00:05:55,550
whenever 你 解释 things 到 我 ,

157
00:05:55,550 --> 00:05:57,050
relate 它 到 analogies 和

158
00:05:57,050 --> 00:05:58,610
metaphors 那个 我 将 理解 .

159
00:05:58,610 --> 00:06:00,330
我 知道 一个 lot 关于 cars .

160
00:06:00,330 --> 00:06:02,790
解释 为什么 我们 应该 学习 关于 生成式 AI ,

161
00:06:02,790 --> 00:06:05,045
even though 这个 models 是 changing 所以 rapidly .

162
00:06:05,045 --> 00:06:07,030
我 有 removed 这个 engine .

163
00:06:07,030 --> 00:06:08,770
我 ' ve removed 这些 其他

164
00:06:08,770 --> 00:06:10,950
direct references 从 这个 提示 ,

165
00:06:10,950 --> 00:06:13,130
和 instead , 我 ' ve told 它 , 这个 是 什么 我 知道 .

166
00:06:13,130 --> 00:06:17,515
制作 sure 它 是 something 那个 我 知道 关于 .

167
00:06:17,515 --> 00:06:21,140
它 comes back 和 它 says , fundamentals 是 key ,

168
00:06:21,140 --> 00:06:23,580
as knowing 这个 basics 的 如何 一个 combustion engine

169
00:06:23,580 --> 00:06:25,990
works helps 你 理解 任何 car ,

170
00:06:25,990 --> 00:06:27,760
学习 这个 foundational principles 的

171
00:06:27,760 --> 00:06:30,680
生成式 AI equips 你 到 grasp future models .

172
00:06:30,680 --> 00:06:32,720
这个 core concepts like 机器 学习

173
00:06:32,720 --> 00:06:34,360
和 神经的 networks remain consistent ,

174
00:06:34,360 --> 00:06:37,230
even as specific implementations change .

175
00:06:37,230 --> 00:06:39,520
它 ' s still giving 我 car analogies ,

176
00:06:39,520 --> 00:06:41,040
但是 现在 它 ' s actually changed

177
00:06:41,040 --> 00:06:42,960
这个 examples 那个 它 ' s giving 我 ,

178
00:06:42,960 --> 00:06:46,360
和 它 ' s talking 关于 现在 combustion .

179
00:06:46,360 --> 00:06:48,820
它 ' s talking 关于 如何 理解

180
00:06:48,820 --> 00:06:50,140
如何 cars function allows 你

181
00:06:50,140 --> 00:06:51,680
到 maintain 他们 better 和 制作

182
00:06:51,680 --> 00:06:53,320
informed decisions 关于 upgrades ,

183
00:06:53,320 --> 00:06:55,300
和 它 ' s telling 我 为什么 , 但是 它 ' s using 它 从

184
00:06:55,300 --> 00:06:57,720
这个 domain 的 cars .

185
00:06:57,720 --> 00:07:00,680
我 didn ' t 有 到 告诉 它 什么 到 pull 从 那个 domain .

186
00:07:00,680 --> 00:07:03,780
我 ' m just telling 它 这个 是 这个 raw material ,

187
00:07:03,780 --> 00:07:06,315
这个 types 的 concepts 我 理解 well .

188
00:07:06,315 --> 00:07:08,480
那些 concepts 那个 我 理解

189
00:07:08,480 --> 00:07:10,680
well 是 这个 rocks 那个 ' s going 到 stack 与 .

190
00:07:10,680 --> 00:07:12,780
那些 是 这个 rocks 那个 ' s going 到 translate

191
00:07:12,780 --> 00:07:15,460
我的 name 是 朱尔斯 怀特 进入 , 或 在 这个 case ,

192
00:07:15,460 --> 00:07:17,980
它 ' s going 到 translate , 你 知道 ,

193
00:07:17,980 --> 00:07:20,380
所有 关于 生成式 AI 和

194
00:07:20,380 --> 00:07:21,960
为什么 它 ' s changing 和 如何

195
00:07:21,960 --> 00:07:23,460
我们 still want 到 去 和 学习 关于 它 .

196
00:07:23,460 --> 00:07:25,640
但是 现在 它 ' s translated 进入 rocks

197
00:07:25,640 --> 00:07:29,560
drawn 从 这个 想法 的 这个 automotive domain .

