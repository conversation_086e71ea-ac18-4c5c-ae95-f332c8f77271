1
00:00:00,000 --> 00:00:02,480
让我们回到芭芭谈到的
一些内容，

2
00:00:02,480 --> 00:00:04,540
这真的很重要，
也就是，

3
00:00:04,540 --> 00:00:06,560
如果当我们学习
一个新概念时，

4
00:00:06,560 --> 00:00:08,180
我们可以将它连接到

5
00:00:08,180 --> 00:00:09,420
我们大脑中已经
有的链接，

6
00:00:09,420 --> 00:00:12,560
它可以改善我们的学习方式。

7
00:00:13,850 --> 00:00:19,995
我们如何使用这个翻译的想法
来利用这一点？

8
00:00:19,995 --> 00:00:21,700
芭芭谈到了这个，

9
00:00:21,700 --> 00:00:23,460
这个想法是我们
用它来给我们

10
00:00:23,460 --> 00:00:25,180
不同的隐喻类比来

11
00:00:25,180 --> 00:00:26,920
帮助我们理解某些东西。

12
00:00:26,920 --> 00:00:28,880
现在，你会听到我经常
谈论的一件事

13
00:00:28,880 --> 00:00:31,180
是模式的概念。

14
00:00:31,180 --> 00:00:33,040
当我们去写提示时，

15
00:00:33,040 --> 00:00:35,300
提示就像发短信。

16
00:00:35,300 --> 00:00:36,600
你输入一条消息，

17
00:00:36,600 --> 00:00:38,680
这条消息就像
你会发给朋友的短信，

18
00:00:38,680 --> 00:00:39,760
你会发给朋友的短信，

19
00:00:39,760 --> 00:00:41,160
然后你的朋友
会去

20
00:00:41,160 --> 00:00:43,520
回复你的短信
并给你回短信，

21
00:00:43,520 --> 00:00:45,320
除了在这种情况下，我们发送

22
00:00:45,320 --> 00:00:47,420
一个叫做提示的东西，

23
00:00:47,420 --> 00:00:48,620
这是我们的消息。

24
00:00:48,620 --> 00:00:50,200
然后AI，
生成式AI模型去

25
00:00:50,200 --> 00:00:52,100
给我们回短信响应。

26
00:00:52,100 --> 00:00:53,800
我们称之为输出，

27
00:00:53,800 --> 00:00:55,555
或者我们称之为响应。

28
00:00:55,555 --> 00:00:59,230
关键是就像与人类一样，

29
00:00:59,230 --> 00:01:00,670
我们有某些结构和

30
00:01:00,670 --> 00:01:03,950
我们用于某些类型
沟通的模式。

31
00:01:03,950 --> 00:01:06,050
我们与这些模型
有同样类型的东西。

32
00:01:06,050 --> 00:01:08,790
例如，如果我要
写一封正式信件，

33
00:01:08,790 --> 00:01:10,670
我会去说，
亲爱的某某，

34
00:01:10,670 --> 00:01:13,850
逗号，这个模式
背后有原因。

35
00:01:13,850 --> 00:01:16,190
"亲爱的"表示尊重，

36
00:01:16,190 --> 00:01:17,850
然后我们有
这个人的名字，这样

37
00:01:17,850 --> 00:01:19,950
我们知道信件
是写给谁的，

38
00:01:19,950 --> 00:01:21,290
然后我们有一个逗号来

39
00:01:21,290 --> 00:01:23,650
将其与信件的
其余部分分开。

40
00:01:23,650 --> 00:01:25,930
这个模式背后
有目的，

41
00:01:25,930 --> 00:01:28,170
我们将看到
不同的模式

42
00:01:28,170 --> 00:01:29,650
我们可以放入
我们的提示中，这些

43
00:01:29,650 --> 00:01:32,030
有特定目的
来帮助我们实现

44
00:01:32,030 --> 00:01:35,125
与大语言模型的
特定效果。

45
00:01:35,125 --> 00:01:37,190
这就是我们要
看的，

46
00:01:37,190 --> 00:01:38,010
是第一个

47
00:01:38,010 --> 00:01:39,350
我们要使用的
这些模式，

48
00:01:39,350 --> 00:01:41,125
这就是隐喻模式。

49
00:01:41,125 --> 00:01:45,310
我们想要做的是我们
想要让模型

50
00:01:45,310 --> 00:01:47,050
向我们解释事情

51
00:01:47,050 --> 00:01:50,360
使用我们很好理解的
隐喻和类比。

52
00:01:50,360 --> 00:01:52,450
现在，我们已经看到

53
00:01:52,450 --> 00:01:54,115
它可以去
接受概念，

54
00:01:54,115 --> 00:01:55,540
比如我的名字是朱尔斯·怀特，

55
00:01:55,540 --> 00:01:57,150
并将其翻译成一堆

56
00:01:57,150 --> 00:01:59,750
石头，所以似乎
很自然我们应该

57
00:01:59,750 --> 00:02:01,670
假设我们可以去说

58
00:02:01,670 --> 00:02:04,610
将这个概念翻译成
隐喻和类比。

59
00:02:04,610 --> 00:02:07,310
隐喻和类比
是我们的石头，我们

60
00:02:07,310 --> 00:02:10,270
希望它现在堆叠
并解释事情，

61
00:02:10,270 --> 00:02:12,390
并从这些石头中
构建想法，

62
00:02:12,390 --> 00:02:13,270
现在这些将是

63
00:02:13,270 --> 00:02:15,360
我们知道的
隐喻和类比。

64
00:02:15,360 --> 00:02:18,515
让我们看一个
这样的例子。

65
00:02:18,515 --> 00:02:21,170
这个模式是解释

66
00:02:21,170 --> 00:02:24,190
使用隐喻和类比的
某个主题，

67
00:02:24,190 --> 00:02:25,630
然后我们给它
一些信息

68
00:02:25,630 --> 00:02:29,110
关于使用什么
隐喻和类比。

69
00:02:29,690 --> 00:02:32,475
让我们举第一个例子。

70
00:02:32,475 --> 00:02:34,550
芭芭谈到了这个想法

71
00:02:34,550 --> 00:02:35,850
思考大语言

72
00:02:35,850 --> 00:02:36,890
模型及其发展，

73
00:02:36,890 --> 00:02:38,640
有点像汽车引擎。

74
00:02:38,640 --> 00:02:41,290
即使它们
发展如此迅速，

75
00:02:41,290 --> 00:02:42,630
我们仍然去学习
它，因为

76
00:02:42,630 --> 00:02:44,430
一旦你学会开车，

77
00:02:44,430 --> 00:02:46,010
你就有了基本概念。

78
00:02:46,010 --> 00:02:48,405
即使下个月出现V8，

79
00:02:48,405 --> 00:02:50,030
或者V10或其他

80
00:02:50,030 --> 00:02:51,870
东西，或者我们
得到电动引擎，

81
00:02:51,870 --> 00:02:55,065
我们仍然在开车。
这真的很重要。

82
00:02:55,065 --> 00:02:56,670
我们仍然在驾驶

83
00:02:56,670 --> 00:02:59,000
生成式AI，当我们
学习它时。

84
00:02:59,000 --> 00:03:01,465
所以我们要
回到这个

85
00:03:01,465 --> 00:03:03,510
我们要看，

86
00:03:03,510 --> 00:03:10,095
ChatGPT能否使用
汽车和引擎类比来解释这个？

87
00:03:10,095 --> 00:03:12,800
解释为什么我们应该
学习生成式AI，

88
00:03:12,800 --> 00:03:15,520
即使模型
变化如此迅速。

89
00:03:15,520 --> 00:03:17,800
使用汽车和引擎类比。

90
00:03:17,800 --> 00:03:19,660
我正在设置它

91
00:03:19,660 --> 00:03:22,860
以与我刚才做的方式
非常相似地解释事情。

92
00:03:22,860 --> 00:03:24,950
ChatGPT说什么？

93
00:03:24,950 --> 00:03:27,460
它回来说，
学习生成式AI，

94
00:03:27,460 --> 00:03:28,740
即使模型正在

95
00:03:28,740 --> 00:03:31,380
快速变化，对于理解

96
00:03:31,380 --> 00:03:33,340
基础原理和

97
00:03:33,340 --> 00:03:35,700
这项技术的
潜在应用至关重要。

98
00:03:35,700 --> 00:03:38,420
使用汽车类比
可以帮助说明。

99
00:03:38,420 --> 00:03:40,520
汽车类比，学习驾驶

100
00:03:40,520 --> 00:03:41,660
涉及理解

101
00:03:41,660 --> 00:03:43,300
基本原理
如转向，

102
00:03:43,300 --> 00:03:44,660
加速和制动，

103
00:03:44,660 --> 00:03:46,880
这些原理
无论如何都适用

104
00:03:46,880 --> 00:03:48,100
汽车型号或品牌。

105
00:03:48,100 --> 00:03:49,640
它基本上告诉你

106
00:03:49,640 --> 00:03:52,480
芭芭告诉你的
和我告诉你的完全一样，

107
00:03:52,480 --> 00:03:53,760
也就是说，是的，

108
00:03:53,760 --> 00:03:56,615
这些东西
变化非常迅速，

109
00:03:56,615 --> 00:03:59,700
但一旦我们理解了那些
基本核心原理，

110
00:03:59,700 --> 00:04:01,620
我们现在可以去应用它们，

111
00:04:01,620 --> 00:04:03,465
即使汽车发生变化。

112
00:04:03,465 --> 00:04:05,220
汽车型号随时间变化，

113
00:04:05,220 --> 00:04:07,100
引入新技术，如

114
00:04:07,100 --> 00:04:09,500
混合动力引擎或
先进安全功能。

115
00:04:09,500 --> 00:04:12,500
如果你理解引擎
和汽车系统如何工作，

116
00:04:12,500 --> 00:04:14,480
适应这些变化变得

117
00:04:14,480 --> 00:04:16,660
更容易。现在，它做了什么？

118
00:04:16,660 --> 00:04:18,920
它基本上
又堆叠了石头，

119
00:04:18,920 --> 00:04:25,000
除了它堆叠的石头
是来自汽车的概念。

120
00:04:25,000 --> 00:04:27,480
它堆叠了与

121
00:04:27,480 --> 00:04:30,180
引擎和驾驶以及
加速和制动相关的想法，

122
00:04:30,180 --> 00:04:33,820
它将它们组织成
不同的例子和

123
00:04:33,820 --> 00:04:35,920
类比和隐喻，

124
00:04:35,920 --> 00:04:37,580
帮助我理解
概念，

125
00:04:37,580 --> 00:04:39,640
它已经进行了翻译

126
00:04:39,640 --> 00:04:43,020
成我给它规则的语言。

127
00:04:43,020 --> 00:04:45,400
我给它的语言，
正如我告诉它的，

128
00:04:45,400 --> 00:04:47,080
去将其翻译成

129
00:04:47,080 --> 00:04:49,420
与我知道的东西相关的
隐喻和类比的语言。

130
00:04:49,420 --> 00:04:52,540
这就是隐喻
模式正在做的

131
00:04:52,540 --> 00:04:57,190
它允许我们本质上
控制那个翻译。

132
00:04:57,220 --> 00:05:01,220
现在，在这个例子中，我
非常强烈地引导了它。

133
00:05:01,220 --> 00:05:03,260
我已经知道类比。

134
00:05:03,260 --> 00:05:05,300
我已经知道隐喻。

135
00:05:05,300 --> 00:05:07,040
我已经知道我

136
00:05:07,040 --> 00:05:08,700
希望它从中
汲取例子的领域。

137
00:05:08,700 --> 00:05:10,960
它已经
高度受限。

138
00:05:10,960 --> 00:05:15,040
当我们不知道
目标是什么时会发生什么？

139
00:05:15,040 --> 00:05:17,480
嗯，我们想要
做的是我们想要

140
00:05:17,480 --> 00:05:20,220
退后一步，思考
目标是什么。

141
00:05:20,220 --> 00:05:23,900
最终的目标是让它

142
00:05:23,900 --> 00:05:25,580
使用隐喻
和类比来解释事情

143
00:05:25,580 --> 00:05:27,730
这些与我们
已经知道的相关。

144
00:05:27,730 --> 00:05:29,420
所以我们可以
做的事情之一

145
00:05:29,420 --> 00:05:32,080
是而不是
明确地说，

146
00:05:32,080 --> 00:05:33,680
这些是隐喻或

147
00:05:33,680 --> 00:05:36,080
类比或例子
我希望你使用，

148
00:05:36,080 --> 00:05:38,485
我们然后告诉它
我们很了解什么。

149
00:05:38,485 --> 00:05:40,380
这是一个例子

150
00:05:40,380 --> 00:05:42,840
当我们填写
底部的括号时，

151
00:05:42,840 --> 00:05:44,400
我们告诉它
什么石头

152
00:05:44,400 --> 00:05:46,715
它必须使用
并且它堆叠，

153
00:05:46,715 --> 00:05:48,750
我们可以做的
是告诉它，

154
00:05:48,750 --> 00:05:50,790
这是我了解的
并且从

155
00:05:50,790 --> 00:05:53,510
我已经拥有的
那个知识领域汲取。

156
00:05:53,510 --> 00:05:55,550
每当你向我
解释事情时，

157
00:05:55,550 --> 00:05:57,050
将其与类比和

158
00:05:57,050 --> 00:05:58,610
我会理解的
隐喻联系起来。

159
00:05:58,610 --> 00:06:00,330
我对汽车了解很多。

160
00:06:00,330 --> 00:06:02,790
解释为什么我们应该
学习生成式AI，

161
00:06:02,790 --> 00:06:05,045
即使模型
变化如此迅速。

162
00:06:05,045 --> 00:06:07,030
我已经移除了引擎。

163
00:06:07,030 --> 00:06:08,770
我已经移除了这些其他

164
00:06:08,770 --> 00:06:10,950
来自提示的
直接引用，

165
00:06:10,950 --> 00:06:13,130
相反，我告诉了它，
这是我知道的。

166
00:06:13,130 --> 00:06:17,515
确保它是我
了解的东西。

167
00:06:17,515 --> 00:06:21,140
它回来说，
基础是关键，

168
00:06:21,140 --> 00:06:23,580
正如了解内燃机

169
00:06:23,580 --> 00:06:25,990
如何工作的基础
帮助你理解任何汽车，

170
00:06:25,990 --> 00:06:27,760
学习生成式AI的

171
00:06:27,760 --> 00:06:30,680
基础原理使你
能够掌握未来模型。

172
00:06:30,680 --> 00:06:32,720
核心概念
如机器学习

173
00:06:32,720 --> 00:06:34,360
和神经网络
保持一致，

174
00:06:34,360 --> 00:06:37,230
即使具体
实现发生变化。

175
00:06:37,230 --> 00:06:39,520
它仍然给我
汽车类比，

176
00:06:39,520 --> 00:06:41,040
但现在它实际上改变了

177
00:06:41,040 --> 00:06:42,960
它给我的
例子，

178
00:06:42,960 --> 00:06:46,360
它现在在谈论
燃烧。

179
00:06:46,360 --> 00:06:48,820
它在谈论
理解

180
00:06:48,820 --> 00:06:50,140
汽车如何运作
允许你

181
00:06:50,140 --> 00:06:51,680
更好地维护它们并做出

182
00:06:51,680 --> 00:06:53,320
关于升级的
明智决定，

183
00:06:53,320 --> 00:06:55,300
它告诉我为什么，
但它使用的是

184
00:06:55,300 --> 00:06:57,720
汽车领域的。

185
00:06:57,720 --> 00:07:00,680
我不必告诉它
从那个领域提取什么。

186
00:07:00,680 --> 00:07:03,780
我只是告诉它这是
原材料，

187
00:07:03,780 --> 00:07:06,315
我很好理解的
概念类型。

188
00:07:06,315 --> 00:07:08,480
我很好理解的那些概念

189
00:07:08,480 --> 00:07:10,680
是它要堆叠的石头。

190
00:07:10,680 --> 00:07:12,780
那些是要翻译的石头

191
00:07:12,780 --> 00:07:15,460
"我的名字是朱尔斯·怀特"
或者在这种情况下，

192
00:07:15,460 --> 00:07:17,980
它要翻译，你知道，

193
00:07:17,980 --> 00:07:20,380
所有关于生成式AI和

194
00:07:20,380 --> 00:07:21,960
为什么它在变化以及

195
00:07:21,960 --> 00:07:23,460
我们仍然想要去
学习它。

196
00:07:23,460 --> 00:07:25,640
但现在它被
翻译成石头

197
00:07:25,640 --> 00:07:29,560
从汽车领域的
想法中汲取。
