1
00:00:00,753 --> 00:00:05,598
One of my favorite things in generative AI
is the concept of multimodal prompting.

2
00:00:05,598 --> 00:00:07,022
So what does this mean?

3
00:00:07,022 --> 00:00:08,246
Well, just like,

4
00:00:08,246 --> 00:00:12,718
you can go and you can send an image
to a friend through a text message.

5
00:00:12,718 --> 00:00:16,022
So you can type in a message like,
hey, check out this crazy tree.

6
00:00:16,022 --> 00:00:19,020
You take a picture of the tree and
you text it to your friend, and

7
00:00:19,020 --> 00:00:22,018
then they can react and respond to it,
and they can say, yeah,

8
00:00:22,018 --> 00:00:25,638
check out it's this type of tree, and
here's some information about it.

9
00:00:25,638 --> 00:00:27,482
Like, did you know this?

10
00:00:27,482 --> 00:00:30,256
And we can go and
do the same thing with generative AI.

11
00:00:30,256 --> 00:00:34,934
Rather than sending a photo off to
a friend, we can send a photograph off to

12
00:00:34,934 --> 00:00:39,839
one of these large language models, to
one of these systems that can help us to

13
00:00:39,839 --> 00:00:43,680
translate imagery into
discovery of information.

14
00:00:43,680 --> 00:00:47,987
And this is one of my favorite things
to do with generative AI is to go and

15
00:00:47,987 --> 00:00:51,992
discover things about the world
around me using photographs.

16
00:00:51,992 --> 00:00:53,416
So what does this look like?

17
00:00:53,416 --> 00:00:55,610
Well, let me give you some examples.

18
00:00:55,610 --> 00:01:00,209
So one thing that we can do is we can
think about what are things that we

19
00:01:00,209 --> 00:01:02,666
could do with the things around us.

20
00:01:02,666 --> 00:01:06,485
How do we brainstorm and come up with
interesting ideas of how to reimagine

21
00:01:06,485 --> 00:01:09,682
the things that we're interacting
with on a daily basis?

22
00:01:09,682 --> 00:01:13,643
And I'm always thinking about this with my
son because this is one of the things that

23
00:01:13,643 --> 00:01:17,265
we really like doing together, is going
and thinking about how to do crazy,

24
00:01:17,265 --> 00:01:19,755
fun things with the things
that we already have, but

25
00:01:19,755 --> 00:01:22,402
using generative AI as
an exploration mechanism.

26
00:01:22,402 --> 00:01:26,302
So I sat down with him a couple months
ago, and I put together this prompt, and

27
00:01:26,302 --> 00:01:28,750
it was,
I'm going to take pictures of things.

28
00:01:28,750 --> 00:01:32,181
And whenever I take pictures of things,
you give me a crazy, fun,

29
00:01:32,181 --> 00:01:35,230
totally unique new game that
I could play with them.

30
00:01:35,230 --> 00:01:37,844
And then what we did is we went and
sat around and

31
00:01:37,844 --> 00:01:42,539
took different everyday objects around the
house and put them together to try to see

32
00:01:42,539 --> 00:01:47,280
what crazy games we could play and
to brainstorm and inspire ourselves.

33
00:01:47,280 --> 00:01:50,800
And so the very first picture that we took
was my son went and grabbed a bunch of

34
00:01:50,800 --> 00:01:54,212
wiffle balls that were still in their
boxes, put them down on the ground.

35
00:01:54,212 --> 00:01:55,446
He grabbed the tv remote and

36
00:01:55,446 --> 00:01:58,872
he grabbed this wooden ship that we
had made together when he was younger.

37
00:01:58,872 --> 00:02:01,589
He put it all together,
we snapped a picture of it.

38
00:02:01,589 --> 00:02:04,400
And what did it say,
what does ChatGPT say?

39
00:02:04,400 --> 00:02:07,670
It says, here's a fun game that
you could play with the items,

40
00:02:07,670 --> 00:02:09,461
remote control, shuffleboard,

41
00:02:09,461 --> 00:02:13,912
slide the boxes along the wooden plate to
score points by landing in specific zones.

42
00:02:13,912 --> 00:02:17,426
And it gives the whole information
about how to go and play this game.

43
00:02:17,426 --> 00:02:21,204
And this was really cool because, right,
it allowed us to go and start assembling

44
00:02:21,204 --> 00:02:24,764
objects and begin understanding how we
might interact with them in a new way,

45
00:02:24,764 --> 00:02:27,158
how you might think of
building a game with them.

46
00:02:27,158 --> 00:02:29,350
We've done amazing things
where we've gone and

47
00:02:29,350 --> 00:02:32,877
had virtual science fairs where we go
around setting up everyday objects, and

48
00:02:32,877 --> 00:02:36,950
we take pictures of them and discover
interesting science facts about them.

49
00:02:36,950 --> 00:02:40,590
Let me show you an example of
something that looks like that.

50
00:02:40,590 --> 00:02:43,752
So, at the same time, we went and
we said, you know, let's go and

51
00:02:43,752 --> 00:02:46,914
discover some actual, you know,
things that we could learn about

52
00:02:46,914 --> 00:02:50,131
the objects around uS, not just how
we could play games with them and

53
00:02:50,131 --> 00:02:53,968
put them together in UNique Ways, but
what are real information about them.

54
00:02:53,968 --> 00:02:56,024
And so we crafted this prompt, and it was,

55
00:02:56,024 --> 00:02:58,648
tell me the physics of
anything I take a picture of.

56
00:02:58,648 --> 00:03:02,056
And then I handed it to my son, and
he said, I know ExacTLy what to do.

57
00:03:02,056 --> 00:03:05,801
And with one hand, he threw a Ball up
in the air, and with the OTher one,

58
00:03:05,801 --> 00:03:10,264
he captured this picture of the ball as
it's flying through the air and rotating.

59
00:03:10,264 --> 00:03:12,368
And he also captured
a picture of our dog and

60
00:03:12,368 --> 00:03:14,425
a bunch of other stuff in the background.

61
00:03:14,425 --> 00:03:17,320
And what does ChatGPT say to this?

62
00:03:17,320 --> 00:03:20,717
It comes back and it says,
let's break down the physics in the scene.

63
00:03:20,717 --> 00:03:25,344
Motion blur and ball in motion, so
it's gotten what this scene is about.

64
00:03:25,344 --> 00:03:30,056
It's translated the image into
the Physics that we see in the image or

65
00:03:30,056 --> 00:03:31,584
some of the PhySics.

66
00:03:31,584 --> 00:03:34,136
And that's what's amazing,
is this is a translation.

67
00:03:34,136 --> 00:03:36,976
It is a translation of,
this is what you showed me.

68
00:03:36,976 --> 00:03:39,258
And with respect to your question,

69
00:03:39,258 --> 00:03:42,999
these are the physics that we
could consider in the scene.

70
00:03:42,999 --> 00:03:47,092
There's probably more, it says, kinematics
and dynamics and projectile motion.

71
00:03:47,092 --> 00:03:49,604
It translates it into static objects and
forces.

72
00:03:49,604 --> 00:03:53,433
Because despite the fact that we thought
that this was a picture of the ball

73
00:03:53,433 --> 00:03:57,037
in motion, it picked up that in
the background, there's shelves.

74
00:03:57,037 --> 00:04:00,912
And it says, okay, the shelving unit and
the various items are stationary,

75
00:04:00,912 --> 00:04:05,206
demonstrating equilibrium and Newton's
first law and normal force and gravity.

76
00:04:05,206 --> 00:04:08,846
And all these discoveries were made
through translating a photograph into

77
00:04:08,846 --> 00:04:10,926
information about the world around us.

78
00:04:10,926 --> 00:04:14,292
And now we can start thinking about,
wow, what else can we

79
00:04:14,292 --> 00:04:19,114
discover about the items around us every
day that maybe we're not thinking about?

80
00:04:19,114 --> 00:04:22,437
And then I was thinking one day,
what I really want my son to take

81
00:04:22,437 --> 00:04:27,800
away from this is I want him to understand
that these models make mistakes, right?

82
00:04:27,800 --> 00:04:30,648
I don't want him to just assume
that what it puts out is right.

83
00:04:30,648 --> 00:04:34,360
And he was sitting there reading a
Guinness Book of Worlds Records book, and

84
00:04:34,360 --> 00:04:36,506
I thought, aha, I know the answer to this,

85
00:04:36,506 --> 00:04:39,640
of the fun thing that we can do
that will help emphasize this.

86
00:04:39,640 --> 00:04:41,506
And so I built this prompt, and I said,

87
00:04:41,506 --> 00:04:44,461
tell me a world record related
to anything I take a picture of.

88
00:04:44,461 --> 00:04:48,600
So we're going to translate
pictures into world records.

89
00:04:48,600 --> 00:04:52,730
And he went, and we took this same picture
that we'd taken a little earlier, and

90
00:04:52,730 --> 00:04:54,752
we translated it into world records.

91
00:04:54,752 --> 00:04:56,366
And notice now what it does.

92
00:04:56,366 --> 00:04:58,690
It sees the dog in the image, our dog.

93
00:04:58,690 --> 00:05:02,231
And it says, the world record for the
longest ears on a living dog is held by

94
00:05:02,231 --> 00:05:04,990
Lou, a black and
town coonhound from the United States.

95
00:05:04,990 --> 00:05:10,930
Lou's ears measure an impressive
34 centimeters, or 13.38 inches.

96
00:05:10,930 --> 00:05:15,670
And so we just discovered something about
the longest ears on a dog because it

97
00:05:15,670 --> 00:05:19,770
translated what we saw in that
photograph into a world record.

98
00:05:19,770 --> 00:05:22,328
It also went and did a bunch of
world records related to stairs,

99
00:05:22,328 --> 00:05:23,983
because there's stairs in the background.

100
00:05:23,983 --> 00:05:26,293
And it said,
given the stairs in the background,

101
00:05:26,293 --> 00:05:28,772
fastest time to run up
the Empire State building.

102
00:05:28,772 --> 00:05:32,642
Tallest stack of shelves, which it turns
out was made by Ikea, which is kind of

103
00:05:32,642 --> 00:05:37,228
funny because Ikea made the shelves in the
background, so it's somewhat appropriate.

104
00:05:37,228 --> 00:05:40,180
And then what we said is, now let's go and
see if these facts are correct.

105
00:05:40,180 --> 00:05:43,804
Let's look them up in your world records
book, or let's go look them up online.

106
00:05:43,804 --> 00:05:46,256
But it's giving us an avenue to go and

107
00:05:46,256 --> 00:05:50,608
discover information about
the things around us, because it can

108
00:05:50,608 --> 00:05:55,950
translate pictures of the things
around us into information about them.

109
00:05:55,950 --> 00:05:59,030
What else might we want
it to translate into?

110
00:05:59,030 --> 00:06:01,923
Well, maybe we want to know
how to make something.

111
00:06:01,923 --> 00:06:05,870
So we translate an image into
instructions to make something.

112
00:06:05,870 --> 00:06:10,770
And so I took this picture of some
sushi that we had in the refrigerator,

113
00:06:10,770 --> 00:06:14,462
and I said,
give me step by step plans to make this.

114
00:06:14,462 --> 00:06:18,179
Create a complete list of materials
with identifiers like one a, one b, and

115
00:06:18,179 --> 00:06:21,710
then output your step by step plan and
reference the materials.

116
00:06:21,710 --> 00:06:24,839
And it takes the picture
of the sushi in my prompt,

117
00:06:24,839 --> 00:06:26,934
and it translates it into a plan.

118
00:06:26,934 --> 00:06:30,036
First, we have the list of
materials like sushi rice and

119
00:06:30,036 --> 00:06:33,468
rice vinegar and sugar and salt and
salmon and cream cheese and

120
00:06:33,468 --> 00:06:37,038
nori seaweed sheets and
bamboo sushi mat optional for rolling.

121
00:06:37,038 --> 00:06:41,460
So it's translated what was seen into
a list of resources that I'm going to need

122
00:06:41,460 --> 00:06:42,958
to create this thing.

123
00:06:42,958 --> 00:06:45,726
And then it goes through, and
it creates a step by step plan.

124
00:06:45,726 --> 00:06:48,932
Measure one cup of sushi, rice,
and rinse it under cold water and

125
00:06:48,932 --> 00:06:50,414
it gives steps for making it.

126
00:06:50,414 --> 00:06:56,270
So it's created a recipe for
going and reproducing what it saw.

127
00:06:56,270 --> 00:07:00,778
And what's interesting, though, is it
can continue to translate and help us.

128
00:07:00,778 --> 00:07:05,541
So I thought, let me test it and
its ability to translate updates to how

129
00:07:05,541 --> 00:07:10,750
I'm going about and following its
instructions into feedback for me.

130
00:07:10,750 --> 00:07:14,149
And so I went and I put all these items
on the shelf or on the counter, and

131
00:07:14,149 --> 00:07:17,518
I thought I would try to confuse it,
see if I can test its ability.

132
00:07:17,518 --> 00:07:19,797
And so I said, look,
here's some cheese spread,

133
00:07:19,797 --> 00:07:21,865
that's going to be
supposedly my cream cheese.

134
00:07:21,865 --> 00:07:25,481
I've got soy sauce, which is correct,
but I've translated sushi

135
00:07:25,481 --> 00:07:29,980
rice into yellow rice and I've translated
nori sheets into these seaweed snacks.

136
00:07:29,980 --> 00:07:30,724
What does it do?

137
00:07:30,724 --> 00:07:34,772
When I ask it to translate and
I tell it, here is what I bought?

138
00:07:34,772 --> 00:07:36,332
What did I forget or get wrong?

139
00:07:36,332 --> 00:07:39,525
And so I follow up in
the conversation with this and

140
00:07:39,525 --> 00:07:43,409
I want to translation into feedback for
me on what I've done.

141
00:07:43,409 --> 00:07:47,049
And it comes back and it says, analysis
of purchased items, Cabernet Sauvignon,

142
00:07:47,049 --> 00:07:49,206
cheddar cheese spread
instead of cream cheese.

143
00:07:49,206 --> 00:07:52,350
So it's identified that
this is the wrong item.

144
00:07:52,350 --> 00:07:53,886
Soy sauce is correct.

145
00:07:53,886 --> 00:07:56,196
The yellow rice instead of sushi rice,

146
00:07:56,196 --> 00:07:59,454
the roasted seaweed snacks
instead of nori sheets.

147
00:07:59,454 --> 00:08:02,324
And now it's telling me missing or
incorrect items,

148
00:08:02,324 --> 00:08:03,930
so it's giving me feedback.

149
00:08:03,930 --> 00:08:08,277
It's translating the image in
the context of the conversation and

150
00:08:08,277 --> 00:08:11,934
into feedback on what I've
gotten wrong in the recipe.

151
00:08:11,934 --> 00:08:17,390
Or maybe you are dealing with
some unfamiliar machine.

152
00:08:17,390 --> 00:08:21,859
You've walked into a hotel room and
it's got a coffee maker that you've never

153
00:08:21,859 --> 00:08:24,846
used before, and
you feel absolutely befuddled.

154
00:08:24,846 --> 00:08:27,534
How on earth am I going
to use this coffee maker?

155
00:08:27,534 --> 00:08:32,022
And we can translate images into
instructions for how to use something.

156
00:08:32,022 --> 00:08:36,384
And in fact, it absolutely baffles me
today that every single product that I'm

157
00:08:36,384 --> 00:08:39,508
buying in the store,
that I cannot take a picture of it and

158
00:08:39,508 --> 00:08:43,852
that there's not some app put out by
the manufacturer to tell me how to use it.

159
00:08:43,852 --> 00:08:46,405
Like, if you are a manufacturer, please,

160
00:08:46,405 --> 00:08:49,140
please tap into these
large language models.

161
00:08:49,140 --> 00:08:52,495
Tap into these models and give us,
you know, the ability to go and

162
00:08:52,495 --> 00:08:56,140
do multimodal prompting to
discover how to use your product.

163
00:08:56,140 --> 00:08:59,661
So I took a picture of this coffee
maker in a hotel room and I said,

164
00:08:59,661 --> 00:09:02,380
explain to me step by
step how to use this.

165
00:09:02,380 --> 00:09:05,932
I actually know how to use this one,
but I thought it'd be kind of fun.

166
00:09:05,932 --> 00:09:07,124
It comes back.

167
00:09:07,124 --> 00:09:08,853
Here's a step by step guide.

168
00:09:08,853 --> 00:09:13,052
Plug in the machine, power on, fill
the water reservoir, insert the K cup,

169
00:09:13,052 --> 00:09:15,520
place a cup, and it goes on,
on how to use it.

170
00:09:16,900 --> 00:09:22,372
And then I thought, can it translate
not only images but textual feedback?

171
00:09:22,372 --> 00:09:27,788
I give it into how I need to,
you know, react to some situation.

172
00:09:27,788 --> 00:09:31,408
In this case,
I've apparently done all these things,

173
00:09:31,408 --> 00:09:34,111
but the machine is just blinking a light.

174
00:09:34,111 --> 00:09:37,289
And so I say I did all of these steps,
but there's just a light flashing and

175
00:09:37,289 --> 00:09:38,492
nothing is happening.

176
00:09:38,492 --> 00:09:39,748
What might be going wrong?

177
00:09:39,748 --> 00:09:41,567
Give me a step-by-step plan to diagnose.

178
00:09:41,567 --> 00:09:44,236
So now it's taking that
image of the thing,

179
00:09:44,236 --> 00:09:48,884
it's taking its plan of how to use it,
and now it's helping me diagnose it.

180
00:09:48,884 --> 00:09:54,732
So it's translating what I'm seeing and
describing to it into a diagnosis plan.

181
00:09:54,732 --> 00:09:58,726
But the key thing is,
it becomes this outlet for discovery.

182
00:09:58,726 --> 00:10:03,110
All the things around us, crazy,
fun, brainstorming things around.

183
00:10:03,110 --> 00:10:05,238
What could I do with these objects?

184
00:10:05,238 --> 00:10:07,294
It's about how do I make something?

185
00:10:07,294 --> 00:10:10,365
Maybe I have a pile of Cheerios and
some glue, and I ask it,

186
00:10:10,365 --> 00:10:11,918
what can I make with this?

187
00:10:11,918 --> 00:10:15,542
It might be how do I make this thing that
I really want to make in front of me?

188
00:10:15,542 --> 00:10:21,534
Like, tell me step by step how to cook it,
how to whatever it is, how to build it.

189
00:10:21,534 --> 00:10:23,458
It can be how to use something.

190
00:10:23,458 --> 00:10:25,333
And I think about the matrix,

191
00:10:25,333 --> 00:10:29,442
the movie where Trinity is about
to climb into this helicopter.

192
00:10:29,442 --> 00:10:33,133
And she needs to know how to fly this
helicopter right then, right there.

193
00:10:33,133 --> 00:10:35,586
And they download
complete instructions for

194
00:10:35,586 --> 00:10:39,994
how to fly the helicopter into her head,
and suddenly she's an expert pilot.

195
00:10:39,994 --> 00:10:42,038
Now, we're not quite there yet,

196
00:10:42,038 --> 00:10:46,268
but we're pretty close in the sense
that we can discover on demand how to

197
00:10:46,268 --> 00:10:49,942
use the things around us simply
by taking pictures of them.

198
00:10:49,942 --> 00:10:51,874
Now, to put this in perspective,

199
00:10:51,874 --> 00:10:56,870
I think if I'd had all the supercomputing
in the world, you know, 100 PhD students,

200
00:10:56,870 --> 00:11:01,198
and you told me to go and build a system
where I can take a picture of an arbitrary

201
00:11:01,198 --> 00:11:05,860
object and be given instructions on how to
use it, how to make it, how to diagnose,

202
00:11:05,860 --> 00:11:10,031
problem with it, I would have told you,
no way, no how, cannot be done.

203
00:11:10,031 --> 00:11:11,830
And it's absolutely in your hands.

204
00:11:11,830 --> 00:11:16,426
You have that capability today to
translate a picture of things around you

205
00:11:16,426 --> 00:11:18,616
into discovery of information.

206
00:11:18,616 --> 00:11:22,311
You need that things that you can
then learn about, used to inform you,

207
00:11:22,311 --> 00:11:24,651
to help you go and
know what questions to ask or

208
00:11:24,651 --> 00:11:27,560
how to fix some simple issue
that you're running into.

