1
00:00:00,520 --> 00:00:05,401
In this video, we're going to be
talking about specific techniques you

2
00:00:05,401 --> 00:00:08,390
can use to help advance your learning.

3
00:00:08,390 --> 00:00:12,070
First off, I have a question for you.

4
00:00:12,070 --> 00:00:16,972
What is the most effective
technique you've found that

5
00:00:16,972 --> 00:00:20,110
helps you learn most efficiently?

6
00:00:20,110 --> 00:00:25,970
Is it rereading or
maybe highlighting or underlining?

7
00:00:25,970 --> 00:00:31,373
Is it retrieval practice or
recall that is like using a flashcard

8
00:00:31,373 --> 00:00:35,986
to see if you know what's on
the other side of the card?

9
00:00:35,986 --> 00:00:39,666
Or is it creating a concept map?

10
00:00:39,666 --> 00:00:41,666
Which one is it?

11
00:00:41,666 --> 00:00:46,458
Let's keep you in suspense for a second.

12
00:00:46,458 --> 00:00:50,730
Actually, it is retrieval practice.

13
00:00:50,730 --> 00:00:57,610
Using a flashcard or even seeing
if something is in your own mind.

14
00:00:57,610 --> 00:01:02,106
Retrieval practice is much better
than the other approaches.

15
00:01:02,106 --> 00:01:08,186
Why it is helping you to
learn this initial material.

16
00:01:08,186 --> 00:01:12,983
You initially put those weak
links in long term memory

17
00:01:12,983 --> 00:01:17,034
when you're first learning something, but

18
00:01:17,034 --> 00:01:22,682
then each time you retrieve that
something from your own mind,

19
00:01:22,682 --> 00:01:28,360
you are actually strengthening
those sets of links.

20
00:01:28,360 --> 00:01:34,360
Retrieval practice, as it turns out,
is incredibly powerful.

21
00:01:34,360 --> 00:01:39,758
If you're just reading a page and
then you reread it, actually,

22
00:01:39,758 --> 00:01:46,336
your eyes are moving over the page, but
you're not really thinking so much.

23
00:01:46,336 --> 00:01:49,317
It's really easy to let your eyes go over

24
00:01:49,317 --> 00:01:54,440
the page without ensuring
you've got that in your brain.

25
00:01:54,440 --> 00:02:00,480
People will sometimes say, well,
what about teaching other people?

26
00:02:00,480 --> 00:02:05,376
That's actually probably
the most effective way to learn.

27
00:02:05,376 --> 00:02:09,261
And it's true,
teaching others is really effective.

28
00:02:09,261 --> 00:02:14,391
But teaching other people is
actually retrieval practice.

29
00:02:14,391 --> 00:02:20,451
You're retrieving your knowledge from your
own brain, strengthening those links.

30
00:02:20,451 --> 00:02:24,137
That's how you learn effectively.

31
00:02:24,137 --> 00:02:29,645
Retrieval practice can be harder
than just rereading something or

32
00:02:29,645 --> 00:02:32,310
moving a highlighter on a page.

33
00:02:32,310 --> 00:02:36,376
But that's what's so
valuable about retrieval.

34
00:02:36,376 --> 00:02:42,320
It's what makes those sets of
links really solid and strong.

35
00:02:42,320 --> 00:02:49,050
This has been shown in hundreds and
hundreds of research papers.

36
00:02:49,050 --> 00:02:53,826
It's very important to try
to use retrieval practice as

37
00:02:53,826 --> 00:02:56,070
much as you possibly can.

38
00:02:57,570 --> 00:03:04,390
So let's say you have electronic documents
that relate to whatever you're learning.

39
00:03:04,390 --> 00:03:11,030
What you want to do is upload those
documents into, say, ChatGPT.

40
00:03:12,050 --> 00:03:18,302
As you're loading things in,
you just get your electronic document,

41
00:03:18,302 --> 00:03:24,445
get it loaded in, and then once it's up,
you can ask generative AI for

42
00:03:24,445 --> 00:03:28,870
practice questions, retrieval questions.

43
00:03:28,870 --> 00:03:30,510
But there's more you can do.

44
00:03:30,510 --> 00:03:36,790
And that is, for example,
asking for pretesting questions.

45
00:03:36,790 --> 00:03:38,630
What does that mean?

46
00:03:38,630 --> 00:03:43,183
Well, research has shown
something very interesting.

47
00:03:43,183 --> 00:03:46,922
That is,
if you're going to be learning something,

48
00:03:46,922 --> 00:03:52,970
you often just don't know what's important
about what you're trying to learn.

49
00:03:52,970 --> 00:03:59,248
You struggle, you're reading and it seems
like there's lots of different points and

50
00:03:59,248 --> 00:04:03,465
they're all important and
your mind feels overwhelmed.

51
00:04:03,465 --> 00:04:07,910
But if you create
a pretest on the material,

52
00:04:07,910 --> 00:04:14,533
you may know nothing and
you know you're going to flunk the test.

53
00:04:14,533 --> 00:04:19,967
It turns out though, that if you have
some really good pretest questions,

54
00:04:19,967 --> 00:04:24,366
that will actually feed your
brain insight into what the most

55
00:04:24,366 --> 00:04:28,920
important material is that
you're about to study.

56
00:04:28,920 --> 00:04:34,033
Pretesting questions, that is,
taking a little pretesting

57
00:04:34,033 --> 00:04:39,244
quiz that you give yourself before
you you begin to study things

58
00:04:39,244 --> 00:04:44,848
can give you insight your brain will
use happily to know what the most

59
00:04:44,848 --> 00:04:49,893
important things are that
you're supposed to be learning.

60
00:04:49,893 --> 00:04:53,612
It will also start promoting curiosity,

61
00:04:53,612 --> 00:04:58,081
which, as we'll see,
is helpful for learning.

62
00:04:58,081 --> 00:05:04,100
Sometimes it's valuable to ask for
key ideas related to what you're learning.

63
00:05:04,100 --> 00:05:10,346
This can help if you're struggling to
pick out the main ideas from a text or

64
00:05:10,346 --> 00:05:16,694
video when it comes to a repetitive
question that you might often ask chat,

65
00:05:16,694 --> 00:05:22,134
GPT or other engines,
there's a trick I want to show you because

66
00:05:22,134 --> 00:05:27,776
the reality is that you can go to
some of these different engines and

67
00:05:27,776 --> 00:05:32,410
apps, but really you can only pay for
so many different

68
00:05:32,410 --> 00:05:38,308
specialized websites if you can
even afford to pay for any of them.

69
00:05:38,308 --> 00:05:43,138
It can sometimes be beneficial
then to find little tricks

70
00:05:43,138 --> 00:05:48,080
to use engines and
apps a bit more easily and effectively.

71
00:05:49,180 --> 00:05:55,220
Along these lines,
I like to use something called abbreviate.

72
00:05:55,220 --> 00:05:59,420
I don't get any money from them,
I just like them.

73
00:05:59,420 --> 00:06:04,660
This app is what's called a text expander.

74
00:06:04,660 --> 00:06:09,680
You can take this little tool and
load it on your machine.

75
00:06:09,680 --> 00:06:11,777
Watch what it does.

76
00:06:11,777 --> 00:06:17,940
See a long sentence like this which says,
can you provide a three-page

77
00:06:17,940 --> 00:06:23,090
synopsis in layperson's terms
of the attached document?

78
00:06:23,090 --> 00:06:28,359
All I have to do instead of
that sentence is type ..c,

79
00:06:28,359 --> 00:06:33,286
and using my text expander,
it will automatically

80
00:06:33,286 --> 00:06:38,328
expand that ..c into the long
complete sentence or

81
00:06:38,328 --> 00:06:44,290
several sentences that I
might frequently type in.

82
00:06:44,290 --> 00:06:49,071
I like to use a text expander
that says please give me

83
00:06:49,071 --> 00:06:53,751
a synopsis of along the lines
of what you see here.

84
00:06:53,751 --> 00:07:00,090
The text expander will do a very nice
job of typing that entire sentence in.

85
00:07:00,090 --> 00:07:04,547
What I do is take the paperclip,
I load a PDF on it,

86
00:07:04,547 --> 00:07:09,005
I type c, and
then I get a nice little discussion or

87
00:07:09,005 --> 00:07:14,710
summary of what that paper
might have been discussing.

88
00:07:14,710 --> 00:07:19,661
I generally find that Claude seems
to do a really good job with this

89
00:07:19,661 --> 00:07:23,150
sort of synthesizing of key ideas.

90
00:07:23,150 --> 00:07:28,457
What you'll find is that certain
foundational models will have

91
00:07:28,457 --> 00:07:35,690
advantages for certain tasks, others
will have advantages for other tasks.

92
00:07:35,690 --> 00:07:38,130
Just a quick insight.

93
00:07:38,130 --> 00:07:42,970
I do like to use some of the flashcard
apps that are available.

94
00:07:42,970 --> 00:07:48,146
Generative AI is going
into many of these apps.

95
00:07:48,146 --> 00:07:50,435
So here's an example.

96
00:07:50,435 --> 00:07:54,680
It's a flashcard system called iDoRecall.

97
00:07:54,680 --> 00:08:01,389
In this system, what you can do
is upload an electronic document.

98
00:08:01,389 --> 00:08:05,520
Once you've got it uploaded,
you can tag it and save it.

99
00:08:05,520 --> 00:08:10,368
Then you've got your document online and

100
00:08:10,368 --> 00:08:13,977
then you can take AI and use it.

101
00:08:13,977 --> 00:08:18,770
Generate retrieval practice
flashcards from that page.

102
00:08:18,770 --> 00:08:23,040
You can also use these for pretesting.

103
00:08:23,040 --> 00:08:29,062
If you want to, you can,
on the other hand, select text and

104
00:08:29,062 --> 00:08:34,129
then generate a flashcard using AI for
that text.

105
00:08:34,129 --> 00:08:40,441
This one says, what do transformer
networks learn to predict?

106
00:08:40,441 --> 00:08:45,450
Then, of course, you can save
that recall if you like it, or

107
00:08:45,450 --> 00:08:51,140
tweak it as you might desire,
then you can practice with it.

108
00:08:51,140 --> 00:08:55,473
What do transformer
networks learn to predict?

109
00:08:55,473 --> 00:08:59,240
They predict long-term dependencies.

110
00:08:59,240 --> 00:09:03,570
Got that 100% correct.

111
00:09:03,570 --> 00:09:10,172
Of course, there's far more than simple
electronic documents you can work with.

112
00:09:10,172 --> 00:09:16,318
You can go to YouTube, create
a profile over time of your recalls.

113
00:09:16,318 --> 00:09:21,228
It's so
much that it's too much to describe here,

114
00:09:21,228 --> 00:09:24,889
but these systems are very powerful.

115
00:09:24,889 --> 00:09:29,281
Also, I should mention
there's quite a few different

116
00:09:29,281 --> 00:09:33,205
flashcard apps out there
alongside iDoRecall.

117
00:09:33,205 --> 00:09:37,678
There's of course,
the ever popular Quizlet,

118
00:09:37,678 --> 00:09:43,210
Kahoot, and
sort of the old grand master, Anki.

119
00:09:43,210 --> 00:09:48,733
This gives a little insight that I hope
gives you some help when it comes to

120
00:09:48,733 --> 00:09:55,290
really keying in on the important issues
related to what you want to be learning.

121
00:09:55,290 --> 00:09:58,410
Now, Jules will be going even deeper.