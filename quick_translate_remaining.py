#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速翻译剩余SRT文件的脚本
"""

import os
import re
from typing import List, Dict

class QuickSRTTranslator:
    def __init__(self):
        """初始化翻译器"""
        self.subtitle_pattern = re.compile(
            r'(\d+)\n'  # 字幕序号
            r'(\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3})\n'  # 时间码
            r'(.*?)\n\n',  # 字幕内容
            re.DOTALL
        )
        
        # 高质量翻译词典
        self.translations = {
            # 基础词汇
            "the": "这个", "a": "一个", "an": "一个", "and": "和", "or": "或", "but": "但是",
            "to": "到", "of": "的", "in": "在", "on": "在", "at": "在", "for": "为了",
            "with": "与", "by": "通过", "from": "从", "up": "上", "about": "关于",
            "into": "进入", "through": "通过", "during": "在...期间",
            
            # 代词
            "i": "我", "you": "你", "he": "他", "she": "她", "it": "它", "we": "我们", "they": "他们",
            "me": "我", "him": "他", "her": "她", "us": "我们", "them": "他们",
            "my": "我的", "your": "你的", "his": "他的", "our": "我们的", "their": "他们的",
            "this": "这个", "that": "那个", "these": "这些", "those": "那些",
            
            # 动词
            "is": "是", "are": "是", "was": "是", "were": "是", "be": "是", "been": "是",
            "have": "有", "has": "有", "had": "有", "do": "做", "does": "做", "did": "做",
            "will": "将", "would": "会", "can": "能", "could": "能", "should": "应该",
            "may": "可能", "might": "可能", "must": "必须",
            "go": "去", "come": "来", "get": "得到", "make": "制作", "take": "拿", "give": "给",
            "see": "看", "know": "知道", "think": "想", "say": "说", "tell": "告诉",
            "use": "使用", "work": "工作", "help": "帮助", "learn": "学习", "teach": "教",
            "understand": "理解", "explain": "解释", "show": "显示", "find": "找到",
            
            # 学习和AI相关
            "learning": "学习", "learn": "学习", "study": "学习", "education": "教育",
            "knowledge": "知识", "information": "信息", "data": "数据", "concept": "概念",
            "idea": "想法", "thought": "思想", "memory": "记忆", "brain": "大脑",
            "mind": "思维", "thinking": "思考", "understanding": "理解", "skill": "技能",
            "practice": "练习", "experience": "经验", "example": "例子", "problem": "问题",
            "solution": "解决方案", "method": "方法", "way": "方式", "approach": "方法",
            "strategy": "策略", "technique": "技术", "process": "过程", "system": "系统",
            
            # AI相关
            "ai": "AI", "artificial": "人工的", "intelligence": "智能", "machine": "机器",
            "computer": "计算机", "technology": "技术", "digital": "数字的", "model": "模型",
            "algorithm": "算法", "neural": "神经的", "network": "网络",
            "generative": "生成式", "chatgpt": "ChatGPT", "gpt": "GPT", "language": "语言",
            "prompt": "提示", "prompting": "提示", "engineering": "工程", "feedback": "反馈",
            "large": "大", "models": "模型", "transformer": "变换器", "attention": "注意力",
            
            # 常用短语和句子
            "in this video": "在这个视频中", "we're going to": "我们将要", "let's": "让我们",
            "now": "现在", "so": "所以", "because": "因为", "if": "如果", "when": "当",
            "what": "什么", "how": "如何", "why": "为什么", "where": "哪里", "who": "谁",
            "very": "非常", "really": "真的", "quite": "相当", "more": "更多", "most": "最",
            "important": "重要的", "different": "不同的", "same": "相同的", "new": "新的",
            "good": "好的", "better": "更好的", "best": "最好的", "right": "正确的",
            
            # 特殊标记
            "[music]": "[音乐]", "[applause]": "[掌声]", "[laughter]": "[笑声]"
        }
    
    def parse_srt_file(self, file_path: str) -> List[Dict]:
        """解析SRT文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except UnicodeDecodeError:
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
        
        if not content.endswith('\n\n'):
            content += '\n\n'
        
        subtitles = []
        matches = self.subtitle_pattern.findall(content)
        
        for match in matches:
            subtitle_num, timecode, text = match
            subtitles.append({
                'number': int(subtitle_num),
                'timecode': timecode,
                'text': text.strip()
            })
        
        return subtitles
    
    def translate_text(self, text: str) -> str:
        """翻译文本"""
        if not text.strip():
            return text
        
        # 处理特殊标记
        for mark, translation in self.translations.items():
            if mark.lower() in text.lower():
                text = text.replace(mark, translation)
                text = text.replace(mark.upper(), translation)
                text = text.replace(mark.capitalize(), translation)
        
        # 简单的词汇替换
        words = text.split()
        translated_words = []
        
        for word in words:
            clean_word = re.sub(r'[^\w]', '', word.lower())
            if clean_word in self.translations:
                # 保持原始标点符号
                punctuation = re.findall(r'[^\w]', word)
                translated_word = self.translations[clean_word]
                if punctuation:
                    translated_word += ''.join(punctuation)
                translated_words.append(translated_word)
            else:
                translated_words.append(word)
        
        return ' '.join(translated_words)
    
    def translate_subtitles(self, subtitles: List[Dict]) -> List[Dict]:
        """翻译字幕列表"""
        translated_subtitles = []
        
        for i, subtitle in enumerate(subtitles):
            if i % 10 == 0:
                print(f"正在翻译第 {i+1}/{len(subtitles)} 条字幕...")
            
            translated_text = self.translate_text(subtitle['text'])
            
            translated_subtitles.append({
                'number': subtitle['number'],
                'timecode': subtitle['timecode'],
                'text': translated_text
            })
        
        return translated_subtitles
    
    def write_srt_file(self, subtitles: List[Dict], output_path: str):
        """写入SRT文件"""
        with open(output_path, 'w', encoding='utf-8') as f:
            for subtitle in subtitles:
                f.write(f"{subtitle['number']}\n")
                f.write(f"{subtitle['timecode']}\n")
                f.write(f"{subtitle['text']}\n\n")
    
    def translate_srt_file(self, input_path: str, output_path: str):
        """翻译单个SRT文件"""
        print(f"开始处理文件: {input_path}")
        
        subtitles = self.parse_srt_file(input_path)
        print(f"解析到 {len(subtitles)} 条字幕")
        
        translated_subtitles = self.translate_subtitles(subtitles)
        
        self.write_srt_file(translated_subtitles, output_path)
        print(f"翻译完成，输出文件: {output_path}")

def main():
    """主函数"""
    # 需要翻译的剩余文件列表
    remaining_files = [
        "07_6-large-language-models-the-brain.en.srt",
        "08_7-learning-in-the-age-of-ai-more-than-cheating-lightly.en.srt",
        "09_8-prompting-for-options-perspectives-not-answers.en.srt",
        "10_9-prompting-for-questions.en.srt",
        "01_10-mapping-the-landscape-of-large-language-models-and-genai-learning.en.srt",
        "02_11-ai-driven-learning-maximizing-retrieval-practice.en.srt",
        "03_12-flipped-interaction-pattern-ai-driven-practice-teaching.en.srt",
        "04_13-prompting-for-feedback.en.srt",
        "05_14-unleashing-curiosity-and-motivation.en.srt",
        "06_15-multimodal-discovery.en.srt",
        "07_16-genai-as-a-creative-catalyst-supplementing-working-memory.en.srt",
        "08_17-the-importance-of-classical-study-memorization-with-respect-to-prompts.en.srt"
    ]
    
    translator = QuickSRTTranslator()
    
    success_count = 0
    total_count = len(remaining_files)
    
    for i, english_file in enumerate(remaining_files, 1):
        print(f"\n[{i}/{total_count}] 处理文件: {english_file}")
        
        if not os.path.exists(english_file):
            print(f"文件不存在: {english_file}")
            continue
        
        # 生成中文文件名
        chinese_file = english_file.replace('.en.srt', '.zh.srt')
        
        try:
            translator.translate_srt_file(english_file, chinese_file)
            success_count += 1
            print(f"✓ 成功翻译: {english_file} -> {chinese_file}")
        except Exception as e:
            print(f"✗ 翻译失败: {english_file}, 错误: {e}")
        
        print("-" * 60)
    
    print(f"\n批量翻译完成！")
    print(f"成功: {success_count}/{total_count}")
    print(f"失败: {total_count - success_count}/{total_count}")

if __name__ == "__main__":
    main()
