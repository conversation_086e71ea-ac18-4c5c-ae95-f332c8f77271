1
00:00:00,000 --> 00:00:03,140
Hi, <PERSON><PERSON>,
back again here.

2
00:00:03,140 --> 00:00:05,400
In this video, we're going to

3
00:00:05,400 --> 00:00:08,700
peek under the neural
hood, so to speak,

4
00:00:08,700 --> 00:00:11,260
to help you understand how to

5
00:00:11,260 --> 00:00:14,940
transform your
generative AI skills.

6
00:00:14,940 --> 00:00:17,440
As we're doing this, we should

7
00:00:17,440 --> 00:00:20,010
keep in mind the
words of <PERSON><PERSON><PERSON><PERSON><PERSON>,

8
00:00:20,010 --> 00:00:23,020
the prominent researcher in AI,

9
00:00:23,020 --> 00:00:26,760
who said, "AI won't
replace humans,

10
00:00:26,760 --> 00:00:33,180
but humans who use AI will
replace humans that don't."

11
00:00:33,180 --> 00:00:36,140
I'd like to give you
just a little sense

12
00:00:36,140 --> 00:00:37,930
of my own background.

13
00:00:37,930 --> 00:00:40,735
I'm from Oakland University,

14
00:00:40,735 --> 00:00:43,255
people often hear Oakland,

15
00:00:43,255 --> 00:00:44,685
and they think, well,

16
00:00:44,685 --> 00:00:47,700
she must be from that
city in California.

17
00:00:47,700 --> 00:00:51,420
Palm trees, warm
weather, nice things.

18
00:00:51,420 --> 00:00:56,100
Well, no, actually, I'm
from Oakland University,

19
00:00:56,100 --> 00:00:57,530
which is in Rochester,

20
00:00:57,530 --> 00:00:59,520
Michigan and that's me going

21
00:00:59,520 --> 00:01:02,670
to work on a typical
February day.

22
00:01:02,670 --> 00:01:05,740
I created this course learning

23
00:01:05,740 --> 00:01:08,565
how to learn with
Terry Sejnowski,

24
00:01:08,565 --> 00:01:12,040
the Francis Crick Professor
at the Salk Institute,

25
00:01:12,040 --> 00:01:14,500
where he is also the director of

26
00:01:14,500 --> 00:01:18,525
the Computational
Neuroscience Laboratory.

27
00:01:18,525 --> 00:01:22,280
This is Terry, while he's

28
00:01:22,280 --> 00:01:26,320
taking a break from his
day job in California.

29
00:01:26,320 --> 00:01:30,155
That's one of the nice things
about online teaching.

30
00:01:30,155 --> 00:01:32,460
Not only can students get

31
00:01:32,460 --> 00:01:35,260
together from very
different areas,

32
00:01:35,260 --> 00:01:37,800
but professors can come from

33
00:01:37,800 --> 00:01:41,225
very different
backgrounds as well.

34
00:01:41,225 --> 00:01:44,110
One thing that surprised me

35
00:01:44,110 --> 00:01:47,850
was that I was asked to
give a talk at Harvard.

36
00:01:47,850 --> 00:01:53,130
I was rather nervous
because it's Harvard.

37
00:01:53,130 --> 00:01:55,770
To my surprise, I walked into

38
00:01:55,770 --> 00:01:59,850
the room and it was packed,
standing room only.

39
00:01:59,850 --> 00:02:01,410
I thought, well,

40
00:02:01,410 --> 00:02:04,080
why are there so
many people here?

41
00:02:04,080 --> 00:02:07,250
Turns out our one little course

42
00:02:07,250 --> 00:02:09,480
made for next to nothing,

43
00:02:09,480 --> 00:02:11,330
mostly in my basement,

44
00:02:11,330 --> 00:02:16,870
had on the order of the same
number of students as all of

45
00:02:16,870 --> 00:02:20,090
Harvard's massive
open online courses

46
00:02:20,090 --> 00:02:23,290
and online courses put together,

47
00:02:23,290 --> 00:02:27,930
made for millions of dollars
with hundreds of people.

48
00:02:27,930 --> 00:02:30,950
What this tells you is there's

49
00:02:30,950 --> 00:02:34,270
a lot of interest
in how we learn,

50
00:02:34,270 --> 00:02:37,470
and particularly,
interest related

51
00:02:37,470 --> 00:02:41,350
to how our brain actually
works when we're learning.

52
00:02:41,350 --> 00:02:45,610
We've created many courses.

53
00:02:45,610 --> 00:02:49,210
Some are very good
courses on teaching,

54
00:02:49,210 --> 00:02:54,010
learning for kids, and
changing your job or career.

55
00:02:54,010 --> 00:02:56,870
They all grow from
an underpinning

56
00:02:56,870 --> 00:03:00,215
of knowing how the
brain actually works.

57
00:03:00,215 --> 00:03:04,595
As it turns out,
that is very much

58
00:03:04,595 --> 00:03:06,570
related to what's going on in

59
00:03:06,570 --> 00:03:09,270
the world of artificial
intelligence.

60
00:03:09,270 --> 00:03:11,730
Let's see how this works.

61
00:03:11,730 --> 00:03:16,900
First off, what does ChatGPT do?

62
00:03:16,900 --> 00:03:20,570
Many of you may know
that what it's doing is

63
00:03:20,570 --> 00:03:25,010
often predicting the
next word in a sentence.

64
00:03:25,010 --> 00:03:26,810
That's how it's trained,

65
00:03:26,810 --> 00:03:29,630
or at least one
of the aspects of

66
00:03:29,630 --> 00:03:34,130
training of foundational,
large language models.

67
00:03:34,130 --> 00:03:36,530
What it's also doing is

68
00:03:36,530 --> 00:03:40,930
relating words in
sentences to one another.

69
00:03:40,930 --> 00:03:42,990
If you think about it,

70
00:03:42,990 --> 00:03:45,270
and I hope you can
forgive me for

71
00:03:45,270 --> 00:03:48,590
anthropomorphizing a little bit.

72
00:03:48,590 --> 00:03:53,810
In other words, thinking of
generative AI as human like.

73
00:03:53,810 --> 00:03:56,490
We know it's also
very different.

74
00:03:56,490 --> 00:03:59,930
But if we think about
generative AI and

75
00:03:59,930 --> 00:04:04,690
its ability to focus on
sentences, for example,

76
00:04:04,690 --> 00:04:08,530
this explains why
some years back,

77
00:04:08,530 --> 00:04:11,030
if you went to Google or one of

78
00:04:11,030 --> 00:04:13,230
the translation devices or

79
00:04:13,230 --> 00:04:17,150
apps and asked it to
translate a sentence,

80
00:04:17,150 --> 00:04:20,330
it would do a terrible job.

81
00:04:20,330 --> 00:04:24,530
You could barely understand
the translation.

82
00:04:24,530 --> 00:04:29,110
The reason for this was
because the algorithm,

83
00:04:29,110 --> 00:04:32,190
the smarts at that time of

84
00:04:32,190 --> 00:04:37,010
computers were such that they
could only pay attention,

85
00:04:37,010 --> 00:04:38,530
so to speak, to

86
00:04:38,530 --> 00:04:42,980
one small aspect of what
was in front of them.

87
00:04:42,980 --> 00:04:47,030
By the time the computer got
to the end of the sentence,

88
00:04:47,030 --> 00:04:49,310
it would forget what

89
00:04:49,310 --> 00:04:51,830
was at the beginning
of the sentence.

90
00:04:51,830 --> 00:04:56,850
But using some innovative
new techniques, now,

91
00:04:56,850 --> 00:05:01,070
computers can focus
not only on sentences,

92
00:05:01,070 --> 00:05:05,870
but on entire essays and
not only entire essays,

93
00:05:05,870 --> 00:05:09,255
but actually on the web itself.

94
00:05:09,255 --> 00:05:12,180
How does it put this together?

95
00:05:12,180 --> 00:05:15,145
How does it connect things?

96
00:05:15,145 --> 00:05:18,580
Well, there was a paper
published back in

97
00:05:18,580 --> 00:05:23,935
2017 called Attention
Is All You Need.

98
00:05:23,935 --> 00:05:26,260
This paper, when it came out,

99
00:05:26,260 --> 00:05:30,080
well, nobody really
thought it was very big.

100
00:05:30,080 --> 00:05:32,980
In fact, Terry Sejnowski was

101
00:05:32,980 --> 00:05:36,955
the president of the
Society Neural IPs,

102
00:05:36,955 --> 00:05:38,965
where it was presented.

103
00:05:38,965 --> 00:05:42,380
The presentation was
pretty much relegated to

104
00:05:42,380 --> 00:05:47,990
a small corner that nobody
paid much attention to.

105
00:05:47,990 --> 00:05:51,990
But now, people have
come to recognize that

106
00:05:51,990 --> 00:05:54,970
this one paper actually had

107
00:05:54,970 --> 00:05:58,870
the seed of such an
important new concept,

108
00:05:58,870 --> 00:06:04,490
that this paper has been
cited over 100,000 times,

109
00:06:04,490 --> 00:06:09,365
and it's been the basis
of things like Suno,

110
00:06:09,365 --> 00:06:15,530
for example, which is a
music creation app or tool.

111
00:06:18,830 --> 00:06:21,430
How did this happen?

112
00:06:21,430 --> 00:06:24,610
As it turns out, this paper

113
00:06:24,610 --> 00:06:28,450
developed the idea
of the transformer.

114
00:06:28,450 --> 00:06:33,465
The transformer has two
main components in it.

115
00:06:33,465 --> 00:06:36,900
One is the encoder.

116
00:06:36,900 --> 00:06:41,210
The encoder is where we
take in the information.

117
00:06:41,210 --> 00:06:43,595
For example, a prompt.

118
00:06:43,595 --> 00:06:46,440
Then we have the decoder,

119
00:06:46,440 --> 00:06:50,500
which spits out whatever
your output is.

120
00:06:50,500 --> 00:06:55,800
This encoder decoder and
its flow of information,

121
00:06:55,800 --> 00:06:58,660
as it turns out, is very

122
00:06:58,660 --> 00:07:04,380
similar to the flow of
information in the human brain.

123
00:07:04,380 --> 00:07:08,940
This is why it behooves
us to learn a little bit

124
00:07:08,940 --> 00:07:11,360
about the brain so
we can use some

125
00:07:11,360 --> 00:07:14,160
of the insights of transformers,

126
00:07:14,160 --> 00:07:17,040
large language
models, and things

127
00:07:17,040 --> 00:07:20,560
like ChatGPT more effectively.

128
00:07:20,560 --> 00:07:23,105
Think of large
language models like

129
00:07:23,105 --> 00:07:26,900
ChatGPT as being like
different car engines.

130
00:07:26,900 --> 00:07:29,280
Once you know how to drive,

131
00:07:29,280 --> 00:07:32,300
switching between
cars is a breeze,

132
00:07:32,300 --> 00:07:35,380
even if they've got different
engines under the hood,

133
00:07:35,380 --> 00:07:38,965
it's the same deal with
generative AI platforms like

134
00:07:38,965 --> 00:07:42,920
ChatGPT, Gemini, and Claude.

135
00:07:42,920 --> 00:07:45,020
They're just slightly
different models,

136
00:07:45,020 --> 00:07:46,980
powering similar tools.

137
00:07:46,980 --> 00:07:49,740
Get the hang of one,
and you'll find

138
00:07:49,740 --> 00:07:52,585
the others feel
pretty familiar too.

139
00:07:52,585 --> 00:07:54,890
The basics stay the same,

140
00:07:54,890 --> 00:07:58,870
even if the details under the
hood are a bit different.

141
00:07:58,870 --> 00:08:01,610
Now, let's turn to Jules,

142
00:08:01,610 --> 00:08:04,470
and he'll give you a
little more insight

143
00:08:04,470 --> 00:08:08,790
into these very important ideas.