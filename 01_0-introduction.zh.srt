1
00:00:00,000 --> 00:00:05,246
[音乐]

2
00:00:05,246 --> 00:00:06,587
欢迎 到 我们的 课程 .

3
00:00:06,587 --> 00:00:09,268
加速 你的 学习 与 ChatGPT .

4
00:00:09,268 --> 00:00:11,092
i ' m barbara oakley ,

5
00:00:11,092 --> 00:00:17,190
a 杰出的 教授 的 工程 at oakland 大学 .

6
00:00:17,190 --> 00:00:21,783
你 可能 知道 我 从 我们的 课程 , 学习 如何 到 学习 ,

7
00:00:21,783 --> 00:00:28,082
一个 的 <PERSON>ra ' s 最 受欢迎的 课程 , 或 我的 书籍 关于 学习 技术 .

8
00:00:28,082 --> 00:00:33,759
今天 , i ' m 兴奋的 到 介绍 你 到 这个 令人兴奋的 课程 那个

9
00:00:33,759 --> 00:00:41,150
探索 这个 交集 的 生成式 AI 和 有效的 学习 策略 .

10
00:00:41,150 --> 00:00:45,099
我们 ' ll 深入 进入 这个 神经科学 的 学习 ,

11
00:00:45,099 --> 00:00:49,819
展示 你 什么 发生 在 你的 大脑 当 你 学习 , 和

12
00:00:49,819 --> 00:00:54,080
如何 生成式 AI 可以 补充 这些 过程 .

13
00:00:54,080 --> 00:00:59,769
你 ' ll 发现 如何 到 使用 AI 到 创建 引人入胜的 钩子 为了

14
00:00:59,769 --> 00:01:06,039
你的 研究 , 到 连接 想法 到 你的 个人的 经验 ,

15
00:01:06,039 --> 00:01:11,860
和 甚至 克服 这个 限制 的 工作 记忆 .

16
00:01:11,860 --> 00:01:18,084
加入 我 是 我的 朋友 , 教授 jules white 从 vanderbilt 大学 ,

17
00:01:18,084 --> 00:01:21,208
a 领先的 专业知识 在 生成式 AI .

18
00:01:21,208 --> 00:01:24,996
jules , 什么 可以 我们的 学习者 期待 ?

19
00:01:24,996 --> 00:01:29,128
和 请 , 没有 机器人 模仿 这个 时间 .

20
00:01:29,128 --> 00:01:32,604
> > 好 , barb , i 承诺 不 到 模仿 任何 机器人 .

21
00:01:32,604 --> 00:01:37,214
好 , 不 完全 , 因为 如果 我们 思考 关于 星 战争 和 c 三 po ,

22
00:01:37,214 --> 00:01:42,044
he ' s 这个 协议 机器人 那个 帮助 到 翻译 什么 人们 是 说 周围

23
00:01:42,044 --> 00:01:44,040
他 进入 不同的 语言 .

24
00:01:44,040 --> 00:01:46,860
和 这个 是 什么 我们 ' re 去 到 学习 作为 一个 的 这个 最

25
00:01:46,860 --> 00:01:51,202
重要的 概念 与 这些 模型 当 我们 开始 思考 关于 学习 .

26
00:01:51,202 --> 00:01:55,454
是 如何 做 我们 得到 他们 到 翻译 想法 进入 方式 那个 我们 可以 学习 他们 和

27
00:01:55,454 --> 00:01:58,455
方法 他们 更多 有效地 进入 类比 和

28
00:01:58,455 --> 00:02:00,608
隐喻 那个 共鸣 与 我们 ?

29
00:02:00,608 --> 00:02:03,040
i don ' t 思考 的 它 作为 那个 i 可以 ' t 学习 某事 .

30
00:02:03,040 --> 00:02:06,357
它 ' s 那个 i haven ' t 看到 这个 translation 的 这个 想法 或

31
00:02:06,357 --> 00:02:09,784
这个 概念 进入 an 解释 那个 使 意义 到 我 .

32
00:02:09,784 --> 00:02:13,788
和 我们 ' re 去 到 帮助 你 是 能够 到 桥梁 那个 差距 , 到 是 能够 到 去 和

33
00:02:13,788 --> 00:02:15,495
创建 和 翻译 想法 和

34
00:02:15,495 --> 00:02:19,252
复杂的 概念 进入 解释 那个 使 意义 为了 你 .

35
00:02:19,252 --> 00:02:22,492
但是 我们 ' re 也 去 到 有 到 看 如何 到 做 惊人的 事情 ,

36
00:02:22,492 --> 00:02:26,790
像 拍摄 a 图片 的 某事 和 学习 如何 到 烘烤 它 , 或 如何 到 制作 它 , 或

37
00:02:26,790 --> 00:02:30,312
拍摄 a 图片 的 a 设备 那个 我们 ' d 像 到 学习 如何 到 使用 .

38
00:02:30,312 --> 00:02:34,256
我们 ' re 去 到 谈论 关于 为什么 我们 可以 ' t 给 上 关于 传统的 学习 和

39
00:02:34,256 --> 00:02:38,702
这个 重要性 的 实际上 知道 a 很多 的 概念 从 传统的 学习 当

40
00:02:38,702 --> 00:02:40,280
我们 去 和 开始 提示 .

41
00:02:40,280 --> 00:02:44,402
现在 , 你 可能 知道 我 从 这个 课程 那个 i 教 叫做 提示 工程 为了

42
00:02:44,402 --> 00:02:48,536
聊天 GPT , 哪个 是 一个 的 这个 最 受欢迎的 课程 关于 coursera last 年 .

43
00:02:48,536 --> 00:02:53,063
和 i ' m 真的 兴奋的 关于 帮助 到 探索 如何 我们 使用 简单的 提示

44
00:02:53,063 --> 00:02:58,028
技术 到 丰富 我们的 理解 和 我们的 学习 关于 不同的 主题 .

45
00:02:58,028 --> 00:03:02,141
现在 , i 承诺 那个 i won ' t 制作 任何 机器人 模仿 , 但是

46
00:03:02,141 --> 00:03:06,903
i 绝对 将 告诉 你 一些 迷人的 能力 那个 AI 将

47
00:03:06,903 --> 00:03:11,197
允许 你 到 利用 进入 到 改进 和 加速 你的 学习 .

48
00:03:11,197 --> 00:03:12,180
[音乐]

