#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SRT字幕文件英译中翻译工具（离线版本）
使用本地翻译库将英文SRT字幕文件翻译成中文，保持原有格式和时间码
"""

import os
import re
import time
from typing import List, Tuple, Dict
import argparse

# 尝试导入翻译库
try:
    from googletrans import Translator
    TRANSLATOR_AVAILABLE = True
except ImportError:
    TRANSLATOR_AVAILABLE = False
    print("警告: googletrans库未安装，请运行: pip install googletrans==4.0.0rc1")

class SRTTranslatorOffline:
    def __init__(self):
        """初始化翻译器"""
        self.subtitle_pattern = re.compile(
            r'(\d+)\n'  # 字幕序号
            r'(\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3})\n'  # 时间码
            r'(.*?)\n\n',  # 字幕内容
            re.DOTALL
        )
        
        if TRANSLATOR_AVAILABLE:
            self.translator = Translator()
        else:
            self.translator = None
    
    def parse_srt_file(self, file_path: str) -> List[Dict]:
        """
        解析SRT文件
        
        Args:
            file_path: SRT文件路径
            
        Returns:
            包含字幕信息的字典列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except UnicodeDecodeError:
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
        
        # 确保文件以双换行结尾，便于正则匹配
        if not content.endswith('\n\n'):
            content += '\n\n'
        
        subtitles = []
        matches = self.subtitle_pattern.findall(content)
        
        for match in matches:
            subtitle_num, timecode, text = match
            subtitles.append({
                'number': int(subtitle_num),
                'timecode': timecode,
                'text': text.strip()
            })
        
        return subtitles
    
    def translate_text(self, text: str) -> str:
        """
        翻译文本
        
        Args:
            text: 要翻译的英文文本
            
        Returns:
            翻译后的中文文本
        """
        # 如果是音乐标记或空文本，直接返回
        if text.strip() in ['[MUSIC]', '[APPLAUSE]', '[LAUGHTER]', ''] or not text.strip():
            return text
        
        if not self.translator:
            print(f"翻译器不可用，保持原文: {text}")
            return text
        
        try:
            # 使用googletrans库进行翻译，使用正确的语言代码
            result = self.translator.translate(text, src='en', dest='zh-cn')
            translated_text = result.text
            return translated_text

        except Exception as e:
            print(f"翻译出错: {e}，保持原文: {text}")
            return text
    
    def translate_subtitles(self, subtitles: List[Dict]) -> List[Dict]:
        """
        翻译字幕列表
        
        Args:
            subtitles: 字幕信息列表
            
        Returns:
            翻译后的字幕信息列表
        """
        translated_subtitles = []
        
        for i, subtitle in enumerate(subtitles):
            print(f"正在翻译第 {i+1}/{len(subtitles)} 条字幕: {subtitle['text'][:50]}...")
            
            translated_text = self.translate_text(subtitle['text'])
            
            translated_subtitles.append({
                'number': subtitle['number'],
                'timecode': subtitle['timecode'],
                'text': translated_text
            })
            
            # 添加延迟避免API限制
            time.sleep(0.5)
        
        return translated_subtitles
    
    def write_srt_file(self, subtitles: List[Dict], output_path: str):
        """
        写入SRT文件
        
        Args:
            subtitles: 字幕信息列表
            output_path: 输出文件路径
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            for subtitle in subtitles:
                f.write(f"{subtitle['number']}\n")
                f.write(f"{subtitle['timecode']}\n")
                f.write(f"{subtitle['text']}\n\n")
    
    def translate_srt_file(self, input_path: str, output_path: str):
        """
        翻译单个SRT文件
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
        """
        print(f"开始处理文件: {input_path}")
        
        # 解析SRT文件
        subtitles = self.parse_srt_file(input_path)
        print(f"解析到 {len(subtitles)} 条字幕")
        
        # 翻译字幕
        translated_subtitles = self.translate_subtitles(subtitles)
        
        # 写入翻译后的文件
        self.write_srt_file(translated_subtitles, output_path)
        print(f"翻译完成，输出文件: {output_path}")
    
    def translate_all_srt_files(self, directory: str = "."):
        """
        翻译目录中所有的英文SRT文件
        
        Args:
            directory: 目录路径，默认为当前目录
        """
        # 查找所有英文SRT文件
        srt_files = []
        for filename in os.listdir(directory):
            if filename.endswith('.en.srt'):
                srt_files.append(filename)
        
        if not srt_files:
            print("未找到英文SRT文件（*.en.srt）")
            return
        
        print(f"找到 {len(srt_files)} 个英文SRT文件")
        
        for srt_file in srt_files:
            input_path = os.path.join(directory, srt_file)
            # 生成中文文件名：将.en.srt替换为.zh.srt
            output_filename = srt_file.replace('.en.srt', '.zh.srt')
            output_path = os.path.join(directory, output_filename)
            
            try:
                self.translate_srt_file(input_path, output_path)
                print(f"✓ 成功翻译: {srt_file} -> {output_filename}")
            except Exception as e:
                print(f"✗ 翻译失败: {srt_file}, 错误: {e}")
            
            print("-" * 50)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SRT字幕文件英译中翻译工具（离线版本）')
    parser.add_argument('--input', '-i', help='输入SRT文件路径')
    parser.add_argument('--output', '-o', help='输出SRT文件路径')
    parser.add_argument('--directory', '-d', default='.', help='处理目录，默认为当前目录')
    parser.add_argument('--all', '-a', action='store_true', help='翻译目录中所有英文SRT文件')
    
    args = parser.parse_args()
    
    translator = SRTTranslatorOffline()
    
    if args.all or (not args.input and not args.output):
        # 翻译目录中所有文件
        translator.translate_all_srt_files(args.directory)
    elif args.input and args.output:
        # 翻译单个文件
        translator.translate_srt_file(args.input, args.output)
    else:
        print("请指定输入和输出文件，或使用 --all 参数翻译所有文件")

if __name__ == "__main__":
    main()
