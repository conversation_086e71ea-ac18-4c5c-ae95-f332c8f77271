1
00:00:00,000 --> 00:00:03,140
One really important
component of learning is

2
00:00:03,140 --> 00:00:05,760
getting feedback on your ideas,

3
00:00:05,760 --> 00:00:07,820
your problem solving,
your answers,

4
00:00:07,820 --> 00:00:09,600
all of this stuff that a teacher

5
00:00:09,600 --> 00:00:11,940
provides or that some tool

6
00:00:11,940 --> 00:00:14,360
provides to help you learn
what you're doing well

7
00:00:14,360 --> 00:00:17,540
or what you're not doing well
or think of how to improve.

8
00:00:17,540 --> 00:00:20,240
Now, this is a really
important concept,

9
00:00:20,240 --> 00:00:22,600
and I actually have a
colleague the other day who

10
00:00:22,600 --> 00:00:25,730
showed me one of the really
important examples of this.

11
00:00:25,730 --> 00:00:27,980
His son was studying for

12
00:00:27,980 --> 00:00:30,840
the AP history exam
in the United States,

13
00:00:30,840 --> 00:00:32,875
and part of that
is writing essays.

14
00:00:32,875 --> 00:00:34,970
One of the things he
thought about is,

15
00:00:34,970 --> 00:00:36,825
Well, how do I get
feedback on my essay?

16
00:00:36,825 --> 00:00:38,510
This is something that
was kind of outside

17
00:00:38,510 --> 00:00:39,650
of what he was doing in school,

18
00:00:39,650 --> 00:00:40,710
and he wanted to get a lot of

19
00:00:40,710 --> 00:00:42,335
feedback so he could approve.

20
00:00:42,335 --> 00:00:47,010
What did he do? He
used ChatGPT to act

21
00:00:47,010 --> 00:00:49,430
as an AP history examiner

22
00:00:49,430 --> 00:00:51,930
and give him feedback
on his essay.

23
00:00:51,930 --> 00:00:54,150
It would generate not
only the questions,

24
00:00:54,150 --> 00:00:56,450
but it would give
him feedback that

25
00:00:56,450 --> 00:01:00,070
is in the style of what an
AP examiner might give.

26
00:01:00,070 --> 00:01:03,230
This is a fascinating concept
of how do we tap into

27
00:01:03,230 --> 00:01:07,350
this ability to give
feedback on things.

28
00:01:07,350 --> 00:01:09,130
Now, I'm going to start with

29
00:01:09,130 --> 00:01:13,005
a very simple example
of a way to do this,

30
00:01:13,005 --> 00:01:16,135
and it's using the question
refinement pattern.

31
00:01:16,135 --> 00:01:19,270
I'm going to show you
this pattern directly in

32
00:01:19,270 --> 00:01:20,810
a prompt because that's

33
00:01:20,810 --> 00:01:22,990
the best way in my
opinion to see it.

34
00:01:22,990 --> 00:01:25,650
But the idea behind
this is we want to

35
00:01:25,650 --> 00:01:29,160
go and get feedback on the
questions we're asking.

36
00:01:29,160 --> 00:01:30,710
Because a lot of times if we ask

37
00:01:30,710 --> 00:01:32,730
the right question or

38
00:01:32,730 --> 00:01:34,930
if we see the right
form of the question,

39
00:01:34,930 --> 00:01:38,130
we learn from that, but it
also helps us to improve and

40
00:01:38,130 --> 00:01:39,930
refine our thinking
and understand

41
00:01:39,930 --> 00:01:42,970
more about the problem
we're working with.

42
00:01:42,970 --> 00:01:46,030
Here is the question
refinement pattern.

43
00:01:46,030 --> 00:01:47,550
Whenever I ask you a question,

44
00:01:47,550 --> 00:01:51,150
you will suggest a better
version of my question that is

45
00:01:51,150 --> 00:01:52,890
clearer and more specific and

46
00:01:52,890 --> 00:01:55,650
ask me if you should
answer it instead.

47
00:01:55,650 --> 00:01:57,710
Now, we can go and have

48
00:01:57,710 --> 00:02:00,210
it adjust the question
in many different ways.

49
00:02:00,210 --> 00:02:01,650
I'm saying clear
and more specific.

50
00:02:01,650 --> 00:02:03,070
We could also give
it different type

51
00:02:03,070 --> 00:02:04,490
of guidance about how we want it

52
00:02:04,490 --> 00:02:06,210
to react and refine

53
00:02:06,210 --> 00:02:08,455
and given us feedback
on our questions.

54
00:02:08,455 --> 00:02:11,355
Now, what do we see?

55
00:02:11,355 --> 00:02:13,540
Sure. I can do that, and then

56
00:02:13,540 --> 00:02:15,720
I go and I ask it a question.

57
00:02:15,720 --> 00:02:18,960
Who is the fastest
person on Earth?

58
00:02:18,960 --> 00:02:22,880
Now, my question is not
really the greatest question,

59
00:02:22,880 --> 00:02:25,485
and we're going to get
some feedback on why.

60
00:02:25,485 --> 00:02:29,450
A clear more specific version
of your question could be,

61
00:02:29,450 --> 00:02:31,050
who currently holds the record

62
00:02:31,050 --> 00:02:32,850
for the fastest
100 meter sprint,

63
00:02:32,850 --> 00:02:34,370
and what is their record time?

64
00:02:34,370 --> 00:02:36,730
That's probably a
better question than

65
00:02:36,730 --> 00:02:39,250
my original one because it's
clear, it's more concrete.

66
00:02:39,250 --> 00:02:40,650
Who is the fastest person

67
00:02:40,650 --> 00:02:42,270
could be somebody
who's an astronaut,

68
00:02:42,270 --> 00:02:43,490
who's getting on a rocket,

69
00:02:43,490 --> 00:02:45,270
and they traveled the fastest.

70
00:02:45,270 --> 00:02:47,520
It could be the fastest
person on a bicycle,

71
00:02:47,520 --> 00:02:49,650
it could be it's not clear.

72
00:02:49,650 --> 00:02:54,255
Now it's providing clarity
and feedback on the question.

73
00:02:54,255 --> 00:02:55,605
I'm learning in the process,

74
00:02:55,605 --> 00:02:56,830
oh this is how I

75
00:02:56,830 --> 00:02:58,630
need to think about
improving the question,

76
00:02:58,630 --> 00:03:02,210
and it's more broad
than I thought it was.

77
00:03:02,210 --> 00:03:04,630
Where should I go to college?

78
00:03:04,630 --> 00:03:06,390
A clearer more specific

79
00:03:06,390 --> 00:03:07,690
version of your
question would be,

80
00:03:07,690 --> 00:03:08,850
what factors should I

81
00:03:08,850 --> 00:03:10,950
consider when choosing
a college and how can I

82
00:03:10,950 --> 00:03:12,210
determine which
would be the best

83
00:03:12,210 --> 00:03:14,570
fit for my interest
in career goals?

84
00:03:14,570 --> 00:03:16,500
That's a much better question,

85
00:03:16,500 --> 00:03:18,810
is trying to figure

86
00:03:18,810 --> 00:03:21,690
out what are the right factors
in the selection process?

87
00:03:21,690 --> 00:03:23,880
Not tell me where to go,

88
00:03:23,880 --> 00:03:26,330
and you see I'm getting
feedback on the question,

89
00:03:26,330 --> 00:03:28,730
and I'm learning from
it in the process.

90
00:03:28,730 --> 00:03:32,230
It is a really
important concept.

91
00:03:32,230 --> 00:03:37,240
Now, what is the other
thing we can do with this?

92
00:03:37,240 --> 00:03:40,960
We can start using the
cognitive verifier pattern.

93
00:03:40,960 --> 00:03:42,720
Now, the idea behind this is,

94
00:03:42,720 --> 00:03:44,520
instead of just
taking one question,

95
00:03:44,520 --> 00:03:45,600
let's take the question,

96
00:03:45,600 --> 00:03:47,080
and let's decompose it

97
00:03:47,080 --> 00:03:49,420
into other questions that
we might want to ask,

98
00:03:49,420 --> 00:03:52,120
that would help us better
answer the overall question.

99
00:03:52,120 --> 00:03:54,240
By seeing how we can
take a question,

100
00:03:54,240 --> 00:03:56,840
and we can drill down
into additional levels of

101
00:03:56,840 --> 00:03:58,260
detail and think of

102
00:03:58,260 --> 00:04:01,020
other things we may want
to go and ask and explore.

103
00:04:01,020 --> 00:04:02,640
Whenever I ask you a question,

104
00:04:02,640 --> 00:04:04,900
think of four more
specific questions

105
00:04:04,900 --> 00:04:06,620
that if you had the answer to,

106
00:04:06,620 --> 00:04:09,820
you could better answer the
original question then ask

107
00:04:09,820 --> 00:04:11,220
me if you should answer

108
00:04:11,220 --> 00:04:13,800
those questions before answering
the original question.

109
00:04:13,800 --> 00:04:18,350
It says, got it. What is the
best way to make a bridge?

110
00:04:18,350 --> 00:04:20,010
To better answer your question

111
00:04:20,010 --> 00:04:21,330
about the best way
to make a bridge,

112
00:04:21,330 --> 00:04:23,210
here are four more
specific questions.

113
00:04:23,210 --> 00:04:25,770
What is the intended
purpose and load capacity?

114
00:04:25,770 --> 00:04:27,210
What is the span length?

115
00:04:27,210 --> 00:04:29,250
What are the
environmental conditions?

116
00:04:29,250 --> 00:04:31,600
What is the budget and
available materials?

117
00:04:31,600 --> 00:04:35,470
Now I have a richer
understanding of what goes

118
00:04:35,470 --> 00:04:37,730
into thinking about how

119
00:04:37,730 --> 00:04:39,930
to build the bridge?
What is the best way?

120
00:04:39,930 --> 00:04:42,130
Well, that involves thinking
about these other things.

121
00:04:42,130 --> 00:04:45,530
I'm getting feedback on
my ideas and thinking

122
00:04:45,530 --> 00:04:49,520
about ways that I can improve.
What else might we do?

123
00:04:49,520 --> 00:04:52,140
Well, one of the things
that we have a hard time as

124
00:04:52,140 --> 00:04:53,360
human beings is we have

125
00:04:53,360 --> 00:04:56,140
a hard time seeing
other points of view,

126
00:04:56,140 --> 00:04:59,000
in particular, skeptical
points of view.

127
00:04:59,000 --> 00:05:00,380
People who disagree with this,

128
00:05:00,380 --> 00:05:01,860
we have such a hard time seeing

129
00:05:01,860 --> 00:05:03,480
their point of view and often,

130
00:05:03,480 --> 00:05:05,560
we have a really hard
time anticipating

131
00:05:05,560 --> 00:05:07,665
their point of view
and learning from it.

132
00:05:07,665 --> 00:05:09,800
As a grad student,

133
00:05:09,800 --> 00:05:11,060
this used to terrify me.

134
00:05:11,060 --> 00:05:12,700
I would go and give
presentations,

135
00:05:12,700 --> 00:05:14,680
and I would be worried,
what are they going to ask?

136
00:05:14,680 --> 00:05:16,180
What is that skeptic
in the audience

137
00:05:16,180 --> 00:05:18,000
going to ask me that I
wasn't prepared for,

138
00:05:18,000 --> 00:05:19,420
I hadn't thought through,

139
00:05:19,420 --> 00:05:21,400
because I had a hard
time seeing it because

140
00:05:21,400 --> 00:05:23,490
I was so excited about the
work that I was doing.

141
00:05:23,490 --> 00:05:25,750
I'm going to say,

142
00:05:25,750 --> 00:05:27,650
act as an entrenched skeptic.

143
00:05:27,650 --> 00:05:29,630
Challenge my thinking on

144
00:05:29,630 --> 00:05:31,510
a presentation I'm
about to give,

145
00:05:31,510 --> 00:05:33,550
identify my assumptions
and challenge them,

146
00:05:33,550 --> 00:05:36,170
and I'm giving it my
presentation and then I say,

147
00:05:36,170 --> 00:05:37,870
ask me pointed questions one at

148
00:05:37,870 --> 00:05:39,770
a time to challenge
my assumptions.

149
00:05:39,770 --> 00:05:42,530
Be persistent and don't let
me off the hook easily.

150
00:05:42,530 --> 00:05:45,150
Challenge the assumptions
underlying my answers.

151
00:05:45,150 --> 00:05:46,500
Ask me the first question.

152
00:05:46,500 --> 00:05:48,530
This is using a pattern called

153
00:05:48,530 --> 00:05:51,225
the flipped interaction
pattern as well.

154
00:05:51,225 --> 00:05:53,960
Comes back, it says, let's
dive into your presentation,

155
00:05:53,960 --> 00:05:55,700
title ChatGPT is
just the beginning,

156
00:05:55,700 --> 00:05:57,780
Generative AI will
transform computing.

157
00:05:57,780 --> 00:06:00,630
You assert that Generative
AI will supplant the GUI in

158
00:06:00,630 --> 00:06:01,840
traditional programming as

159
00:06:01,840 --> 00:06:03,740
the dominant interface
for computing.

160
00:06:03,740 --> 00:06:06,300
What evidence or trends
support this claim?

161
00:06:06,300 --> 00:06:09,340
And how do you address potential
counter arguments that

162
00:06:09,340 --> 00:06:10,820
GUIs and traditional
programming are

163
00:06:10,820 --> 00:06:13,100
deeply entrenched
and still evolving?

164
00:06:13,100 --> 00:06:16,060
I come back, and I say
computing is about translation.

165
00:06:16,060 --> 00:06:18,480
We translate some goal
that we want to accomplish

166
00:06:18,480 --> 00:06:20,800
into interactions with
different software tools,

167
00:06:20,800 --> 00:06:22,240
and I go on with my answer.

168
00:06:22,240 --> 00:06:23,780
But it doesn't accept it,

169
00:06:23,780 --> 00:06:25,530
it continues to challenge it.

170
00:06:25,530 --> 00:06:28,620
That's an interesting
perspective, assumption,

171
00:06:28,620 --> 00:06:30,260
Generative AI will improve

172
00:06:30,260 --> 00:06:31,780
the translation process more

173
00:06:31,780 --> 00:06:33,510
effectively than
current methods.

174
00:06:33,510 --> 00:06:34,860
What evidence suggests that

175
00:06:34,860 --> 00:06:37,200
Generative AI can
outperform existing GUIs,

176
00:06:37,200 --> 00:06:38,840
and it's continuing
to challenge me.

177
00:06:38,840 --> 00:06:40,900
It's continuing to
give me feedback on

178
00:06:40,900 --> 00:06:44,560
my idea from a
perspective of a skeptic.

179
00:06:44,560 --> 00:06:45,880
But it's not only challenging

180
00:06:45,880 --> 00:06:47,180
my initial idea and giving

181
00:06:47,180 --> 00:06:48,540
me feedback on my initial idea,

182
00:06:48,540 --> 00:06:50,800
but as I go through
and converse with it,

183
00:06:50,800 --> 00:06:55,080
every single interaction
is feedback on my ideas,

184
00:06:55,080 --> 00:06:56,440
on how I'm responding.

185
00:06:56,440 --> 00:07:00,870
I'm basically learning to go
through and deal with that.

186
00:07:00,870 --> 00:07:02,440
Then I go again, and that's

187
00:07:02,440 --> 00:07:04,000
a compelling argument
about the limitations

188
00:07:04,000 --> 00:07:05,740
of GUIs, but let's delve deeper.

189
00:07:05,740 --> 00:07:07,500
Generative AI can seamlessly

190
00:07:07,500 --> 00:07:09,680
integrate and operate across
diverse software tools.

191
00:07:09,680 --> 00:07:10,940
How do you envision overcoming

192
00:07:10,940 --> 00:07:12,640
the technical and logistical

193
00:07:12,640 --> 00:07:14,345
challenges that involve this?

194
00:07:14,345 --> 00:07:16,740
It's continuing over
and over to give me

195
00:07:16,740 --> 00:07:20,445
feedback to help me
refine my ideas but

196
00:07:20,445 --> 00:07:23,350
this concept of being
able to tap into

197
00:07:23,350 --> 00:07:26,850
it to get feedback right
then when we need it.

198
00:07:26,850 --> 00:07:28,990
Now, does it mean this
is the only feedback

199
00:07:28,990 --> 00:07:31,290
that it replaces
teachers? Absolutely not.

200
00:07:31,290 --> 00:07:34,050
But what we have right
now often is that

201
00:07:34,050 --> 00:07:37,070
we go to class, we leave class,

202
00:07:37,070 --> 00:07:38,550
and then the time in between,

203
00:07:38,550 --> 00:07:39,910
we have no feedback
on what we're

204
00:07:39,910 --> 00:07:41,330
doing or we turn
in an assignment,

205
00:07:41,330 --> 00:07:42,490
and we don't get feedback

206
00:07:42,490 --> 00:07:44,150
until it's graded
and back to us.

207
00:07:44,150 --> 00:07:46,610
The more people there
are in the class,

208
00:07:46,610 --> 00:07:49,210
probably the longer it
takes us to get feedback,

209
00:07:49,210 --> 00:07:50,930
or the more difficult
it is to get

210
00:07:50,930 --> 00:07:53,530
really precise feedback
for the individual person.

211
00:07:53,530 --> 00:07:55,750
But this allows us to take

212
00:07:55,750 --> 00:07:58,890
control of that and get
feedback in between.

213
00:07:58,890 --> 00:08:02,150
They can often help us to

214
00:08:02,150 --> 00:08:06,590
understand and practice and
improve what we're doing.

215
00:08:06,590 --> 00:08:08,730
It can be really effective
when we want to start

216
00:08:08,730 --> 00:08:11,030
looking at other
perspectives on our ideas,

217
00:08:11,030 --> 00:08:14,430
on our plans, on the
things that we're doing.

