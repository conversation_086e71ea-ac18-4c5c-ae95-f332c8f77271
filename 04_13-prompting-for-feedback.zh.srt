1
00:00:00,000 --> 00:00:03,140
one 真的 重要的 component 的 学习 是

2
00:00:03,140 --> 00:00:05,760
getting 反馈 在 你的 ideas ,

3
00:00:05,760 --> 00:00:07,820
你的 问题 solving , 你的 answers ,

4
00:00:07,820 --> 00:00:09,600
所有 的 这个 stuff 那个 一个 teacher

5
00:00:09,600 --> 00:00:11,940
provides 或 那个 一些 tool

6
00:00:11,940 --> 00:00:14,360
provides 到 帮助 你 学习 什么 你 ' re doing well

7
00:00:14,360 --> 00:00:17,540
或 什么 你 ' re 不 doing well 或 想 的 如何 到 improve .

8
00:00:17,540 --> 00:00:20,240
现在 , 这个 是 一个 真的 重要的 概念 ,

9
00:00:20,240 --> 00:00:22,600
和 我 actually 有 一个 colleague 这个 其他 day 谁

10
00:00:22,600 --> 00:00:25,730
showed 我 one 的 这个 真的 重要的 examples 的 这个 .

11
00:00:25,730 --> 00:00:27,980
他的 son 是 studying 为了

12
00:00:27,980 --> 00:00:30,840
这个 ap history exam 在 这个 united states ,

13
00:00:30,840 --> 00:00:32,875
和 部分 的 那个 是 writing essays .

14
00:00:32,875 --> 00:00:34,970
one 的 这个 things 他 思想 关于 是 ,

15
00:00:34,970 --> 00:00:36,825
well , 如何 做 我 得到 反馈 在 我的 essay ?

16
00:00:36,825 --> 00:00:38,510
这个 是 something 那个 是 kind 的 outside

17
00:00:38,510 --> 00:00:39,650
的 什么 他 是 doing 在 school ,

18
00:00:39,650 --> 00:00:40,710
和 他 wanted 到 得到 一个 lot 的

19
00:00:40,710 --> 00:00:42,335
反馈 所以 他 能 approve .

20
00:00:42,335 --> 00:00:47,010
什么 做 他 做 ? 他 used ChatGPT 到 act

21
00:00:47,010 --> 00:00:49,430
as 一个 ap history examiner

22
00:00:49,430 --> 00:00:51,930
和 给 他 反馈 在 他的 essay .

23
00:00:51,930 --> 00:00:54,150
它 会 generate 不 only 这个 questions ,

24
00:00:54,150 --> 00:00:56,450
但是 它 会 给 他 反馈 那个

25
00:00:56,450 --> 00:01:00,070
是 在 这个 style 的 什么 一个 ap examiner 可能 给 .

26
00:01:00,070 --> 00:01:03,230
这个 是 一个 fascinating 概念 的 如何 做 我们 tap 进入

27
00:01:03,230 --> 00:01:07,350
这个 ability 到 给 反馈 在 things .

28
00:01:07,350 --> 00:01:09,130
现在 , 我 ' m going 到 start 与

29
00:01:09,130 --> 00:01:13,005
一个 非常 简单的 例子 的 一个 方式 到 做 这个 ,

30
00:01:13,005 --> 00:01:16,135
和 它 ' s using 这个 question refinement pattern .

31
00:01:16,135 --> 00:01:19,270
我 ' m going 到 显示 你 这个 pattern directly 在

32
00:01:19,270 --> 00:01:20,810
一个 提示 因为 那个 ' s

33
00:01:20,810 --> 00:01:22,990
这个 best 方式 在 我的 opinion 到 看 它 .

34
00:01:22,990 --> 00:01:25,650
但是 这个 想法 behind 这个 是 我们 want 到

35
00:01:25,650 --> 00:01:29,160
去 和 得到 反馈 在 这个 questions 我们 ' re asking .

36
00:01:29,160 --> 00:01:30,710
因为 一个 lot 的 times 如果 我们 ask

37
00:01:30,710 --> 00:01:32,730
这个 正确的 question 或

38
00:01:32,730 --> 00:01:34,930
如果 我们 看 这个 正确的 form 的 这个 question ,

39
00:01:34,930 --> 00:01:38,130
我们 学习 从 那个 , 但是 它 also helps 我们 到 improve 和

40
00:01:38,130 --> 00:01:39,930
refine 我们的 思考 和 理解

41
00:01:39,930 --> 00:01:42,970
更多 关于 这个 问题 我们 ' re working 与 .

42
00:01:42,970 --> 00:01:46,030
这里 是 这个 question refinement pattern .

43
00:01:46,030 --> 00:01:47,550
whenever 我 ask 你 一个 question ,

44
00:01:47,550 --> 00:01:51,150
你 将 suggest 一个 better version 的 我的 question 那个 是

45
00:01:51,150 --> 00:01:52,890
clearer 和 更多 specific 和

46
00:01:52,890 --> 00:01:55,650
ask 我 如果 你 应该 answer 它 instead .

47
00:01:55,650 --> 00:01:57,710
现在 , 我们 能 去 和 有

48
00:01:57,710 --> 00:02:00,210
它 adjust 这个 question 在 许多 不同的 ways .

49
00:02:00,210 --> 00:02:01,650
我 ' m saying clear 和 更多 specific .

50
00:02:01,650 --> 00:02:03,070
我们 能 also 给 它 不同的 type

51
00:02:03,070 --> 00:02:04,490
的 guidance 关于 如何 我们 want 它

52
00:02:04,490 --> 00:02:06,210
到 react 和 refine

53
00:02:06,210 --> 00:02:08,455
和 given 我们 反馈 在 我们的 questions .

54
00:02:08,455 --> 00:02:11,355
现在 , 什么 做 我们 看 ?

55
00:02:11,355 --> 00:02:13,540
sure . 我 能 做 那个 , 和 然后

56
00:02:13,540 --> 00:02:15,720
我 去 和 我 ask 它 一个 question .

57
00:02:15,720 --> 00:02:18,960
谁 是 这个 fastest person 在 earth ?

58
00:02:18,960 --> 00:02:22,880
现在 , 我的 question 是 不 真的 这个 greatest question ,

59
00:02:22,880 --> 00:02:25,485
和 我们 ' re going 到 得到 一些 反馈 在 为什么 .

60
00:02:25,485 --> 00:02:29,450
一个 clear 更多 specific version 的 你的 question 能 是 ,

61
00:02:29,450 --> 00:02:31,050
谁 currently holds 这个 record

62
00:02:31,050 --> 00:02:32,850
为了 这个 fastest 100 meter sprint ,

63
00:02:32,850 --> 00:02:34,370
和 什么 是 他们的 record time ?

64
00:02:34,370 --> 00:02:36,730
那个 ' s probably 一个 better question than

65
00:02:36,730 --> 00:02:39,250
我的 original one 因为 它 ' s clear , 它 ' s 更多 concrete .

66
00:02:39,250 --> 00:02:40,650
谁 是 这个 fastest person

67
00:02:40,650 --> 00:02:42,270
能 是 somebody 谁 ' s 一个 astronaut ,

68
00:02:42,270 --> 00:02:43,490
谁 ' s getting 在 一个 rocket ,

69
00:02:43,490 --> 00:02:45,270
和 他们 traveled 这个 fastest .

70
00:02:45,270 --> 00:02:47,520
它 能 是 这个 fastest person 在 一个 bicycle ,

71
00:02:47,520 --> 00:02:49,650
它 能 是 它 ' s 不 clear .

72
00:02:49,650 --> 00:02:54,255
现在 它 ' s providing clarity 和 反馈 在 这个 question .

73
00:02:54,255 --> 00:02:55,605
我 ' m 学习 在 这个 过程 ,

74
00:02:55,605 --> 00:02:56,830
oh 这个 是 如何 我

75
00:02:56,830 --> 00:02:58,630
need 到 想 关于 improving 这个 question ,

76
00:02:58,630 --> 00:03:02,210
和 它 ' s 更多 broad than 我 思想 它 是 .

77
00:03:02,210 --> 00:03:04,630
哪里 应该 我 去 到 college ?

78
00:03:04,630 --> 00:03:06,390
一个 clearer 更多 specific

79
00:03:06,390 --> 00:03:07,690
version 的 你的 question 会 是 ,

80
00:03:07,690 --> 00:03:08,850
什么 factors 应该 我

81
00:03:08,850 --> 00:03:10,950
consider 当 choosing 一个 college 和 如何 能 我

82
00:03:10,950 --> 00:03:12,210
determine 哪个 会 是 这个 best

83
00:03:12,210 --> 00:03:14,570
fit 为了 我的 interest 在 career goals ?

84
00:03:14,570 --> 00:03:16,500
那个 ' s 一个 很多 better question ,

85
00:03:16,500 --> 00:03:18,810
是 trying 到 figure

86
00:03:18,810 --> 00:03:21,690
out 什么 是 这个 正确的 factors 在 这个 selection 过程 ?

87
00:03:21,690 --> 00:03:23,880
不 告诉 我 哪里 到 去 ,

88
00:03:23,880 --> 00:03:26,330
和 你 看 我 ' m getting 反馈 在 这个 question ,

89
00:03:26,330 --> 00:03:28,730
和 我 ' m 学习 从 它 在 这个 过程 .

90
00:03:28,730 --> 00:03:32,230
它 是 一个 真的 重要的 概念 .

91
00:03:32,230 --> 00:03:37,240
现在 , 什么 是 这个 其他 thing 我们 能 做 与 这个 ?

92
00:03:37,240 --> 00:03:40,960
我们 能 start using 这个 cognitive verifier pattern .

93
00:03:40,960 --> 00:03:42,720
现在 , 这个 想法 behind 这个 是 ,

94
00:03:42,720 --> 00:03:44,520
instead 的 just taking one question ,

95
00:03:44,520 --> 00:03:45,600
let ' s 拿 这个 question ,

96
00:03:45,600 --> 00:03:47,080
和 let ' s decompose 它

97
00:03:47,080 --> 00:03:49,420
进入 其他 questions 那个 我们 可能 want 到 ask ,

98
00:03:49,420 --> 00:03:52,120
那个 会 帮助 我们 better answer 这个 overall question .

99
00:03:52,120 --> 00:03:54,240
通过 seeing 如何 我们 能 拿 一个 question ,

100
00:03:54,240 --> 00:03:56,840
和 我们 能 drill down 进入 additional levels 的

101
00:03:56,840 --> 00:03:58,260
detail 和 想 的

102
00:03:58,260 --> 00:04:01,020
其他 things 我们 可能 want 到 去 和 ask 和 explore .

103
00:04:01,020 --> 00:04:02,640
whenever 我 ask 你 一个 question ,

104
00:04:02,640 --> 00:04:04,900
想 的 four 更多 specific questions

105
00:04:04,900 --> 00:04:06,620
那个 如果 你 有 这个 answer 到 ,

106
00:04:06,620 --> 00:04:09,820
你 能 better answer 这个 original question 然后 ask

107
00:04:09,820 --> 00:04:11,220
我 如果 你 应该 answer

108
00:04:11,220 --> 00:04:13,800
那些 questions 在...之前 answering 这个 original question .

109
00:04:13,800 --> 00:04:18,350
它 says , got 它 . 什么 是 这个 best 方式 到 制作 一个 bridge ?

110
00:04:18,350 --> 00:04:20,010
到 better answer 你的 question

111
00:04:20,010 --> 00:04:21,330
关于 这个 best 方式 到 制作 一个 bridge ,

112
00:04:21,330 --> 00:04:23,210
这里 是 four 更多 specific questions .

113
00:04:23,210 --> 00:04:25,770
什么 是 这个 intended purpose 和 load capacity ?

114
00:04:25,770 --> 00:04:27,210
什么 是 这个 span length ?

115
00:04:27,210 --> 00:04:29,250
什么 是 这个 environmental conditions ?

116
00:04:29,250 --> 00:04:31,600
什么 是 这个 budget 和 available materials ?

117
00:04:31,600 --> 00:04:35,470
现在 我 有 一个 richer 理解 的 什么 goes

118
00:04:35,470 --> 00:04:37,730
进入 思考 关于 如何

119
00:04:37,730 --> 00:04:39,930
到 build 这个 bridge ? 什么 是 这个 best 方式 ?

120
00:04:39,930 --> 00:04:42,130
well , 那个 involves 思考 关于 这些 其他 things .

121
00:04:42,130 --> 00:04:45,530
我 ' m getting 反馈 在 我的 ideas 和 思考

122
00:04:45,530 --> 00:04:49,520
关于 ways 那个 我 能 improve . 什么 else 可能 我们 做 ?

123
00:04:49,520 --> 00:04:52,140
well , one 的 这个 things 那个 我们 有 一个 困难的 time as

124
00:04:52,140 --> 00:04:53,360
human beings 是 我们 有

125
00:04:53,360 --> 00:04:56,140
一个 困难的 time seeing 其他 points 的 view ,

126
00:04:56,140 --> 00:04:59,000
在 particular , skeptical points 的 view .

127
00:04:59,000 --> 00:05:00,380
people 谁 disagree 与 这个 ,

128
00:05:00,380 --> 00:05:01,860
我们 有 这样的 一个 困难的 time seeing

129
00:05:01,860 --> 00:05:03,480
他们的 point 的 view 和 often ,

130
00:05:03,480 --> 00:05:05,560
我们 有 一个 真的 困难的 time anticipating

131
00:05:05,560 --> 00:05:07,665
他们的 point 的 view 和 学习 从 它 .

132
00:05:07,665 --> 00:05:09,800
as 一个 grad student ,

133
00:05:09,800 --> 00:05:11,060
这个 used 到 terrify 我 .

134
00:05:11,060 --> 00:05:12,700
我 会 去 和 给 presentations ,

135
00:05:12,700 --> 00:05:14,680
和 我 会 是 worried , 什么 是 他们 going 到 ask ?

136
00:05:14,680 --> 00:05:16,180
什么 是 那个 skeptic 在 这个 audience

137
00:05:16,180 --> 00:05:18,000
going 到 ask 我 那个 我 wasn ' t prepared 为了 ,

138
00:05:18,000 --> 00:05:19,420
我 hadn ' t 思想 通过 ,

139
00:05:19,420 --> 00:05:21,400
因为 我 有 一个 困难的 time seeing 它 因为

140
00:05:21,400 --> 00:05:23,490
我 是 所以 excited 关于 这个 工作 那个 我 是 doing .

141
00:05:23,490 --> 00:05:25,750
我 ' m going 到 说 ,

142
00:05:25,750 --> 00:05:27,650
act as 一个 entrenched skeptic .

143
00:05:27,650 --> 00:05:29,630
challenge 我的 思考 在

144
00:05:29,630 --> 00:05:31,510
一个 presentation 我 ' m 关于 到 给 ,

145
00:05:31,510 --> 00:05:33,550
identify 我的 assumptions 和 challenge 他们 ,

146
00:05:33,550 --> 00:05:36,170
和 我 ' m giving 它 我的 presentation 和 然后 我 说 ,

147
00:05:36,170 --> 00:05:37,870
ask 我 pointed questions one 在

148
00:05:37,870 --> 00:05:39,770
一个 time 到 challenge 我的 assumptions .

149
00:05:39,770 --> 00:05:42,530
是 persistent 和 don ' t let 我 off 这个 hook easily .

150
00:05:42,530 --> 00:05:45,150
challenge 这个 assumptions underlying 我的 answers .

151
00:05:45,150 --> 00:05:46,500
ask 我 这个 first question .

152
00:05:46,500 --> 00:05:48,530
这个 是 using 一个 pattern called

153
00:05:48,530 --> 00:05:51,225
这个 flipped interaction pattern as well .

154
00:05:51,225 --> 00:05:53,960
comes back , 它 says , let ' s dive 进入 你的 presentation ,

155
00:05:53,960 --> 00:05:55,700
title ChatGPT 是 just 这个 beginning ,

156
00:05:55,700 --> 00:05:57,780
生成式 AI 将 transform computing .

157
00:05:57,780 --> 00:06:00,630
你 assert 那个 生成式 AI 将 supplant 这个 gui 在

158
00:06:00,630 --> 00:06:01,840
traditional programming as

159
00:06:01,840 --> 00:06:03,740
这个 dominant interface 为了 computing .

160
00:06:03,740 --> 00:06:06,300
什么 evidence 或 trends support 这个 claim ?

161
00:06:06,300 --> 00:06:09,340
和 如何 做 你 address potential counter arguments 那个

162
00:06:09,340 --> 00:06:10,820
guis 和 traditional programming 是

163
00:06:10,820 --> 00:06:13,100
deeply entrenched 和 still evolving ?

164
00:06:13,100 --> 00:06:16,060
我 来 back , 和 我 说 computing 是 关于 translation .

165
00:06:16,060 --> 00:06:18,480
我们 translate 一些 goal 那个 我们 want 到 accomplish

166
00:06:18,480 --> 00:06:20,800
进入 interactions 与 不同的 software tools ,

167
00:06:20,800 --> 00:06:22,240
和 我 去 在 与 我的 answer .

168
00:06:22,240 --> 00:06:23,780
但是 它 doesn ' t accept 它 ,

169
00:06:23,780 --> 00:06:25,530
它 continues 到 challenge 它 .

170
00:06:25,530 --> 00:06:28,620
那个 ' s 一个 interesting perspective , assumption ,

171
00:06:28,620 --> 00:06:30,260
生成式 AI 将 improve

172
00:06:30,260 --> 00:06:31,780
这个 translation 过程 更多

173
00:06:31,780 --> 00:06:33,510
effectively than current methods .

174
00:06:33,510 --> 00:06:34,860
什么 evidence suggests 那个

175
00:06:34,860 --> 00:06:37,200
生成式 AI 能 outperform existing guis ,

176
00:06:37,200 --> 00:06:38,840
和 它 ' s continuing 到 challenge 我 .

177
00:06:38,840 --> 00:06:40,900
它 ' s continuing 到 给 我 反馈 在

178
00:06:40,900 --> 00:06:44,560
我的 想法 从 一个 perspective 的 一个 skeptic .

179
00:06:44,560 --> 00:06:45,880
但是 它 ' s 不 only challenging

180
00:06:45,880 --> 00:06:47,180
我的 initial 想法 和 giving

181
00:06:47,180 --> 00:06:48,540
我 反馈 在 我的 initial 想法 ,

182
00:06:48,540 --> 00:06:50,800
但是 as 我 去 通过 和 converse 与 它 ,

183
00:06:50,800 --> 00:06:55,080
每个 single interaction 是 反馈 在 我的 ideas ,

184
00:06:55,080 --> 00:06:56,440
在 如何 我 ' m responding .

185
00:06:56,440 --> 00:07:00,870
我 ' m basically 学习 到 去 通过 和 deal 与 那个 .

186
00:07:00,870 --> 00:07:02,440
然后 我 去 again , 和 那个 ' s

187
00:07:02,440 --> 00:07:04,000
一个 compelling argument 关于 这个 limitations

188
00:07:04,000 --> 00:07:05,740
的 guis , 但是 let ' s delve deeper .

189
00:07:05,740 --> 00:07:07,500
生成式 AI 能 seamlessly

190
00:07:07,500 --> 00:07:09,680
integrate 和 operate across diverse software tools .

191
00:07:09,680 --> 00:07:10,940
如何 做 你 envision overcoming

192
00:07:10,940 --> 00:07:12,640
这个 technical 和 logistical

193
00:07:12,640 --> 00:07:14,345
challenges 那个 involve 这个 ?

194
00:07:14,345 --> 00:07:16,740
它 ' s continuing over 和 over 到 给 我

195
00:07:16,740 --> 00:07:20,445
反馈 到 帮助 我 refine 我的 ideas 但是

196
00:07:20,445 --> 00:07:23,350
这个 概念 的 是 able 到 tap 进入

197
00:07:23,350 --> 00:07:26,850
它 到 得到 反馈 正确的 然后 当 我们 need 它 .

198
00:07:26,850 --> 00:07:28,990
现在 , 做 它 mean 这个 是 这个 only 反馈

199
00:07:28,990 --> 00:07:31,290
那个 它 replaces teachers ? absolutely 不 .

200
00:07:31,290 --> 00:07:34,050
但是 什么 我们 有 正确的 现在 often 是 那个

201
00:07:34,050 --> 00:07:37,070
我们 去 到 class , 我们 leave class ,

202
00:07:37,070 --> 00:07:38,550
和 然后 这个 time 在 在...之间 ,

203
00:07:38,550 --> 00:07:39,910
我们 有 不 反馈 在 什么 我们 ' re

204
00:07:39,910 --> 00:07:41,330
doing 或 我们 turn 在 一个 assignment ,

205
00:07:41,330 --> 00:07:42,490
和 我们 don ' t 得到 反馈

206
00:07:42,490 --> 00:07:44,150
until 它 ' s graded 和 back 到 我们 .

207
00:07:44,150 --> 00:07:46,610
这个 更多 people 那里 是 在 这个 class ,

208
00:07:46,610 --> 00:07:49,210
probably 这个 longer 它 takes 我们 到 得到 反馈 ,

209
00:07:49,210 --> 00:07:50,930
或 这个 更多 difficult 它 是 到 得到

210
00:07:50,930 --> 00:07:53,530
真的 precise 反馈 为了 这个 individual person .

211
00:07:53,530 --> 00:07:55,750
但是 这个 allows 我们 到 拿

212
00:07:55,750 --> 00:07:58,890
control 的 那个 和 得到 反馈 在 在...之间 .

213
00:07:58,890 --> 00:08:02,150
他们 能 often 帮助 我们 到

214
00:08:02,150 --> 00:08:06,590
理解 和 练习 和 improve 什么 我们 ' re doing .

215
00:08:06,590 --> 00:08:08,730
它 能 是 真的 有效的 当 我们 want 到 start

216
00:08:08,730 --> 00:08:11,030
looking 在 其他 perspectives 在 我们的 ideas ,

217
00:08:11,030 --> 00:08:14,430
在 我们的 plans , 在 这个 things 那个 我们 ' re doing .

