1
00:00:00,600 --> 00:00:04,236
我 want 到 pick 上 在 something 非常 重要的 那个 芭芭 talked 关于 ,

2
00:00:04,236 --> 00:00:07,570
和 它 ' s 这个 想法 的 这些 models helping 与 translation .

3
00:00:07,570 --> 00:00:09,475
所以 什么 做 translation mean ?

4
00:00:09,475 --> 00:00:12,806
well , 这个 是 这个 merriam - webster definition 的 translation .

5
00:00:12,806 --> 00:00:16,656
我 ' ve excerpted 部分 的 它 因为 我 wanted 你 到 真的 拿 一个 moment 到 想 关于

6
00:00:16,656 --> 00:00:17,536
这个 definition .

7
00:00:17,536 --> 00:00:21,201
和 我们 ' re going 到 start 到 explore 这个 definition 的 translation 和

8
00:00:21,201 --> 00:00:24,612
为什么 translation 是 所以 重要的 在 这个 context 的 学习 , 和

9
00:00:24,612 --> 00:00:28,550
一些 的 这个 things 我们 ' ve begun 到 discuss 在 如何 这个 大脑 learns .

10
00:00:28,550 --> 00:00:33,274
所以 如果 我们 look 在 这个 definition , 我 want 到 start 通过 looking 在 这个 最 common 方式

11
00:00:33,274 --> 00:00:38,000
那个 我们 想 关于 translation , 哪个 是 这个 first 部分 的 这个 translation ,

12
00:00:38,000 --> 00:00:40,751
一个 act , 过程 或 instance 的 translating ,

13
00:00:40,751 --> 00:00:43,876
这样的 as 一个 rendering 从 one 语言 进入 另一个 .

14
00:00:43,876 --> 00:00:47,390
所以 那个 ' s 这个 first 部分 的 这个 definition .

15
00:00:47,390 --> 00:00:49,699
现在 , 这个 是 什么 我们 想 的 , 和

16
00:00:49,699 --> 00:00:54,079
我们 tend 到 想 的 这个 as 是 一个 非常 literal act 那个 我 去 word 为了

17
00:00:54,079 --> 00:00:58,398
word 和 我 immediately match 上 word 为了 word , 什么 它 means 到 .

18
00:00:58,398 --> 00:01:02,351
但是 translation actually 能 是 something 很多 richer than 那个 , 和

19
00:01:02,351 --> 00:01:07,175
那个 ' s begun 到 是 captured 在 这个 second piece , 一个 change 到 一个 不同的 substance ,

20
00:01:07,175 --> 00:01:09,780
form , 或 appearance , conversion .

21
00:01:09,780 --> 00:01:14,167
和 这个 是 这个 type 的 translation 那个 我们 真的 , 真的 want 到 想 关于 当

22
00:01:14,167 --> 00:01:18,441
我们 start working 与 生成式 AI 到 想 关于 如何 它 能 帮助 我们的 学习 .

23
00:01:18,441 --> 00:01:22,753
所以 我 ' m going 到 给 你 一个 例子 的 translation 那个 是 不 literal

24
00:01:22,753 --> 00:01:23,755
translation .

25
00:01:23,755 --> 00:01:27,630
所以 我们 ' re going 到 start off 与 这个 提示 到 explore 这个 概念 .

26
00:01:27,630 --> 00:01:29,373
所以 这里 ' s 这个 提示 .

27
00:01:29,373 --> 00:01:34,104
assume 那个 这个 语言 你 是 translating 进入 是 based 在 stacking

28
00:01:34,104 --> 00:01:37,244
rocks 在 top 的 one 另一个 到 create meaning .

29
00:01:37,244 --> 00:01:41,660
translate , 你好 , 我的 name 是 朱尔斯 怀特 , 进入 这个 语言 和

30
00:01:41,660 --> 00:01:43,910
解释 这个 translation .

31
00:01:43,910 --> 00:01:45,558
现在 , 你 notice 什么 我 ' ve created 这里 .

32
00:01:45,558 --> 00:01:48,361
我 ' ve created 一个 translation 问题 为了

33
00:01:48,361 --> 00:01:51,742
哪个 一个 语言 做 不 actually exist yet .

34
00:01:51,742 --> 00:01:54,567
所以 不 only 是 它 going 到 有 到 translate , 但是

35
00:01:54,567 --> 00:01:57,191
它 ' s going 到 有 到 design 这个 语言 .

36
00:01:57,191 --> 00:02:01,659
所以 这个 act 的 translation 那个 它 ' s going 到 是 performing 是 不 only

37
00:02:01,659 --> 00:02:06,289
这个 translation 的 这个 literal words , 你好 , 我的 name 是 朱尔斯 怀特 .

38
00:02:06,289 --> 00:02:09,501
但是 它 ' s actually translating 我的 想法 那个 那里 是

39
00:02:09,501 --> 00:02:13,962
一些 语言 的 stacking rocks 那个 能 是 used 到 represent concepts .

40
00:02:13,962 --> 00:02:18,267
它 ' s going 到 有 到 translate 那个 进入 一个 actual 语言 那个 能 represent

41
00:02:18,267 --> 00:02:20,720
concepts 和 然后 translate 我的 name 进入 它 .

42
00:02:20,720 --> 00:02:24,438
所以 let ' s 拿 一个 look 在 什么 ChatGPT 做 .

43
00:02:24,438 --> 00:02:27,301
translating , 你好 , 我的 name 是 朱尔斯 怀特 ,

44
00:02:27,301 --> 00:02:31,669
进入 一个 语言 based 在 stacking rocks requires 我们 到 assign meaning 和

45
00:02:31,669 --> 00:02:35,052
symbols 到 不同的 types 和 arrangements 的 rocks .

46
00:02:35,052 --> 00:02:39,561
所以 它 ' s understood 什么 我 ' m asking 为了 , 它 also understands 那个

47
00:02:39,561 --> 00:02:44,544
语言 requires developing 一些 symbology 的 什么 是 在 这个 语言 和

48
00:02:44,544 --> 00:02:48,620
如何 这个 arrangement 的 那些 symbols creates meaning .

49
00:02:48,620 --> 00:02:53,192
和 然后 它 comes 上 和 它 says , 这里 是 一个 possible translation 和 explanation .

50
00:02:53,192 --> 00:02:57,313
greeting , 你好 , 小的 , round pebble , represents 这个 beginning 的

51
00:02:57,313 --> 00:03:00,769
一个 conversation , like 一个 简单的 和 friendly introduction .

52
00:03:00,769 --> 00:03:06,172
first stone laid horizontally symbolizes stability 和 openness 在 communication .

53
00:03:06,172 --> 00:03:07,415
所以 它 ' s actually going further .

54
00:03:07,415 --> 00:03:13,139
它 ' s also 思考 关于 , contextually , 什么 其他 sort 的 representation 和

55
00:03:13,139 --> 00:03:17,485
meaning 可能 是 behind 这个 actual organization 的 这个 stone .

56
00:03:17,485 --> 00:03:21,180
所以 我 told 它 到 translate , 我的 name 是 朱尔斯 怀特 , 和 它 ' s saying ,

57
00:03:21,180 --> 00:03:22,090
这里 ' s 你好 .

58
00:03:22,090 --> 00:03:27,460
但是 它 ' s also telling 我 , 从 一个 meaning perspective , 如何 这个 first

59
00:03:27,460 --> 00:03:33,640
lane 的 这个 stone horizontally creates 一个 symbol 的 openness 和 stability .

60
00:03:33,640 --> 00:03:38,578
这个 是 很多 greater than just direct , literal , word 为了 word translation .

61
00:03:38,578 --> 00:03:41,056
second , identity , 我的 name 是 .

62
00:03:41,056 --> 00:03:47,154
所以 它 doesn ' t actually 拿 我的 name 是 , 但是 它 takes 这个 概念 的 identity .

63
00:03:47,154 --> 00:03:51,675
和 这个 是 真的 fascinating 因为 它 ' s 不 translating literal words ,

64
00:03:51,675 --> 00:03:55,658
我的 name 是 , 它 ' s translating 这个 概念 behind 他们 , identity .

65
00:03:55,658 --> 00:04:01,086
和 它 says , medium - sized , smooth stone , represents identity 和

66
00:04:01,086 --> 00:04:05,342
self as smoothness symbolizes clarity 和 certainty .

67
00:04:05,342 --> 00:04:09,197
stack 的 three 小的 stones , 每个 stone represents 一个 word ,

68
00:04:09,197 --> 00:04:13,330
emphasizing 这个 sequence 和 connection 的 我的 name 和 是 .

69
00:04:13,330 --> 00:04:17,418
和 所以 它 是 , as 我 是 一个 little bit incorrect jumping 这个 gun .

70
00:04:17,418 --> 00:04:20,212
它 是 somewhat literally translating , 但是

71
00:04:20,212 --> 00:04:24,521
它 ' s also extracted 这个 fundamental 概念 behind 这些 words .

72
00:04:24,521 --> 00:04:27,271
和 然后 它 goes 在 和 它 says , name , 朱尔斯 怀特 .

73
00:04:27,271 --> 00:04:30,500
tall , narrow stone 为了 朱尔斯 represents uniqueness 和

74
00:04:30,500 --> 00:04:33,066
individuality as 一个 tall stone stands out .

75
00:04:33,066 --> 00:04:37,718
和 所以 它 ' s 这个 想法 那个 一个 name 是 something somewhat unique 那个 helps 到

76
00:04:37,718 --> 00:04:41,183
identify 那个 person 和 所以 create identity 为了 他们 .

77
00:04:41,183 --> 00:04:45,284
和 然后 wide , 怀特 stone 为了 怀特 , 这个 color symbolizes 这个 literal meaning 的

78
00:04:45,284 --> 00:04:49,104
这个 surname , 和 这个 width conveys 一个 sense 的 prominence 或 significance .

79
00:04:49,104 --> 00:04:50,967
well , 我 love 这个 translation .

80
00:04:50,967 --> 00:04:55,632
这个 怀特 部分 ' s probably fairly obvious 那个 更多 的 这个 type 的 translation 我们 ' re

81
00:04:55,632 --> 00:04:56,418
思考 的 .

82
00:04:56,418 --> 00:05:02,114
但是 那里 ' s 所以 很多 rich translation 的 conversion , 的 taking 这个 想法 ,

83
00:05:02,114 --> 00:05:08,262
我的 name 是 朱尔斯 怀特 , 和 思考 关于 如何 到 express 它 as stacking 的 rocks .

84
00:05:08,262 --> 00:05:14,562
如何 可能 我们 去 和 build 在 那个 meaning , 但是 在 top 的 rocks ?

85
00:05:14,562 --> 00:05:18,258
那个 ' s 不 just 一个 literal word 为了 word 的 translation ,

86
00:05:18,258 --> 00:05:21,958
那个 ' s 一个 很多 richer 和 broader sense 的 translation .

87
00:05:21,958 --> 00:05:26,679
的 converting 这个 fundamental 想法 那个 我 ' m trying 到 express ,

88
00:05:26,679 --> 00:05:31,630
我的 name 是 朱尔斯 怀特 , 进入 一个 representation 的 stacked rocks .

89
00:05:31,630 --> 00:05:34,752
和 那个 是 一个 wild transformation , 一个 conversion ,

90
00:05:34,752 --> 00:05:37,446
一个 translation 那个 ' s 真的 强大的 .

91
00:05:37,446 --> 00:05:41,070
现在 , 如何 能 我们 使用 这个 到 tap 进入 这个 方式 那个 我们 学习 到 .

