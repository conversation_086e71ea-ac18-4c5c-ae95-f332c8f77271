1
00:00:00,600 --> 00:00:04,236
我想接着芭芭谈到的一个
非常重要的观点，

2
00:00:04,236 --> 00:00:07,570
那就是这些模型
帮助翻译的想法。

3
00:00:07,570 --> 00:00:09,475
那么翻译意味着什么？

4
00:00:09,475 --> 00:00:12,806
嗯，这是韦氏词典
对翻译的定义。

5
00:00:12,806 --> 00:00:16,656
我摘录了其中的一部分，因为我希望
你们真正花一点时间思考

6
00:00:16,656 --> 00:00:17,536
这个定义。

7
00:00:17,536 --> 00:00:21,201
我们将开始探索
这个翻译的定义，以及

8
00:00:21,201 --> 00:00:24,612
为什么翻译在学习的
语境中如此重要，还有

9
00:00:24,612 --> 00:00:28,550
我们已经开始讨论的
大脑如何学习的一些内容。

10
00:00:28,550 --> 00:00:33,274
如果我们看这个定义，我想
首先看看我们思考翻译的最常见方式，

11
00:00:33,274 --> 00:00:38,000
也就是这个翻译定义的第一部分，

12
00:00:38,000 --> 00:00:40,751
翻译的行为、过程或
实例，

13
00:00:40,751 --> 00:00:43,876
比如从一种语言
转换到另一种语言。

14
00:00:43,876 --> 00:00:47,390
这就是定义的
第一部分。

15
00:00:47,390 --> 00:00:49,699
现在，这是我们想到的，而且

16
00:00:49,699 --> 00:00:54,079
我们倾向于认为这是一个
非常字面的行为，我逐字

17
00:00:54,079 --> 00:00:58,398
进行，立即匹配每个词
的含义。

18
00:00:58,398 --> 00:01:02,351
但翻译实际上可以是
比这更丰富的东西，

19
00:01:02,351 --> 00:01:07,175
这开始在第二部分中得到体现，
转变为不同的物质、

20
00:01:07,175 --> 00:01:09,780
形式或外观，转换。

21
00:01:09,780 --> 00:01:14,167
这就是我们真的、真的想要思考的
翻译类型，当

22
00:01:14,167 --> 00:01:18,441
我们开始与生成式AI合作，
思考它如何帮助我们学习时。

23
00:01:18,441 --> 00:01:22,753
所以我要给你们一个
非字面翻译的例子。

24
00:01:22,753 --> 00:01:23,755
翻译。

25
00:01:23,755 --> 00:01:27,630
我们将从这个提示开始
来探索这个概念。

26
00:01:27,630 --> 00:01:29,373
这是提示。

27
00:01:29,373 --> 00:01:34,104
假设你要翻译成的语言
是基于将石头

28
00:01:34,104 --> 00:01:37,244
一个叠一个地堆叠
来创造意义。

29
00:01:37,244 --> 00:01:41,660
将"你好，我的名字是朱尔斯·怀特"
翻译成这种语言，并

30
00:01:41,660 --> 00:01:43,910
解释这个翻译。

31
00:01:43,910 --> 00:01:45,558
现在，你注意到我在这里创造了什么。

32
00:01:45,558 --> 00:01:48,361
我创造了一个翻译问题，

33
00:01:48,361 --> 00:01:51,742
而这种语言实际上
还不存在。

34
00:01:51,742 --> 00:01:54,567
所以它不仅要翻译，

35
00:01:54,567 --> 00:01:57,191
还要设计这种语言。

36
00:01:57,191 --> 00:02:01,659
所以它要执行的翻译行为
不仅仅是

37
00:02:01,659 --> 00:02:06,289
字面词汇的翻译，
"你好，我的名字是朱尔斯·怀特"。

38
00:02:06,289 --> 00:02:09,501
而是实际上在翻译
我的想法，即存在

39
00:02:09,501 --> 00:02:13,962
某种堆叠石头的语言，
可以用来表示概念。

40
00:02:13,962 --> 00:02:18,267
它必须将其翻译成
一种能够表示

41
00:02:18,267 --> 00:02:20,720
概念的实际语言，
然后将我的名字翻译成它。

42
00:02:20,720 --> 00:02:24,438
让我们看看ChatGPT做了什么。

43
00:02:24,438 --> 00:02:27,301
翻译"你好，
我的名字是朱尔斯·怀特"

44
00:02:27,301 --> 00:02:31,669
成基于堆叠石头的语言
需要我们为不同类型和

45
00:02:31,669 --> 00:02:35,052
石头的排列分配
意义和符号。

46
00:02:35,052 --> 00:02:39,561
所以它理解了我的要求，
它也理解

47
00:02:39,561 --> 00:02:44,544
语言需要开发一些
关于语言中有什么以及

48
00:02:44,544 --> 00:02:48,620
这些符号的排列如何
创造意义的符号学。

49
00:02:48,620 --> 00:02:53,192
然后它提出并说，这是
一个可能的翻译和解释。

50
00:02:53,192 --> 00:02:57,313
问候，你好，小的、圆的鹅卵石，
代表

51
00:02:57,313 --> 00:03:00,769
对话的开始，就像简单而
友好的介绍。

52
00:03:00,769 --> 00:03:06,172
第一块水平放置的石头象征着
沟通中的稳定性和开放性。

53
00:03:06,172 --> 00:03:07,415
所以它实际上走得更远。

54
00:03:07,415 --> 00:03:13,139
它还在思考，从语境上，
石头的实际组织背后

55
00:03:13,139 --> 00:03:17,485
可能有什么其他类型的
表示和意义。

56
00:03:17,485 --> 00:03:21,180
所以我告诉它翻译
"我的名字是朱尔斯·怀特"，它说，

57
00:03:21,180 --> 00:03:22,090
这是"你好"。

58
00:03:22,090 --> 00:03:27,460
但它也从意义的角度
告诉我，第一块

59
00:03:27,460 --> 00:03:33,640
水平放置的石头如何创造
开放性和稳定性的象征。

60
00:03:33,640 --> 00:03:38,578
这远远超过了直接的、
字面的、逐词翻译。

61
00:03:38,578 --> 00:03:41,056
第二，身份，我的名字是。

62
00:03:41,056 --> 00:03:47,154
所以它实际上没有取"我的名字是"，
而是取了身份的概念。

63
00:03:47,154 --> 00:03:51,675
这真的很迷人，因为
它不是在翻译字面词汇，

64
00:03:51,675 --> 00:03:55,658
"我的名字是"，它在翻译
它们背后的概念，身份。

65
00:03:55,658 --> 00:04:01,086
它说，中等大小的、
光滑的石头，代表身份和

66
00:04:01,086 --> 00:04:05,342
自我，因为光滑象征着
清晰和确定性。

67
00:04:05,342 --> 00:04:09,197
三块小石头的堆叠，
每块石头代表一个词，

68
00:04:09,197 --> 00:04:13,330
强调我的名字和"是"的
序列和连接。

69
00:04:13,330 --> 00:04:17,418
所以它是，我有点
不正确地抢先了。

70
00:04:17,418 --> 00:04:20,212
它在某种程度上是字面翻译，但

71
00:04:20,212 --> 00:04:24,521
它也提取了这些词汇
背后的基本概念。

72
00:04:24,521 --> 00:04:27,271
然后它继续说，
名字，朱尔斯·怀特。

73
00:04:27,271 --> 00:04:30,500
朱尔斯的高而窄的石头
代表独特性和

74
00:04:30,500 --> 00:04:33,066
个性，因为高石头很突出。

75
00:04:33,066 --> 00:04:37,718
所以这是名字是某种
独特的东西的想法，有助于

76
00:04:37,718 --> 00:04:41,183
识别那个人，从而
为他们创造身份。

77
00:04:41,183 --> 00:04:45,284
然后怀特的宽白石头，
颜色象征着

78
00:04:45,284 --> 00:04:49,104
姓氏的字面意思，宽度传达了
突出或重要的感觉。

79
00:04:49,104 --> 00:04:50,967
嗯，我喜欢这个翻译。

80
00:04:50,967 --> 00:04:55,632
怀特部分可能相当明显，
更多的是我们正在

81
00:04:55,632 --> 00:04:56,418
思考的翻译类型。

82
00:04:56,418 --> 00:05:02,114
但有如此丰富的翻译
转换，取这个想法，

83
00:05:02,114 --> 00:05:08,262
"我的名字是朱尔斯·怀特"，并思考
如何将其表达为石头的堆叠。

84
00:05:08,262 --> 00:05:14,562
我们如何去构建那种意义，
但在石头之上？

85
00:05:14,562 --> 00:05:18,258
这不仅仅是字面的
逐词翻译，

86
00:05:18,258 --> 00:05:21,958
这是一种更丰富、
更广泛的翻译意义。

87
00:05:21,958 --> 00:05:26,679
将我试图表达的
基本想法转换，

88
00:05:26,679 --> 00:05:31,630
"我的名字是朱尔斯·怀特"，
转换成堆叠石头的表示。

89
00:05:31,630 --> 00:05:34,752
这是一个疯狂的转换，
一个转化，

90
00:05:34,752 --> 00:05:37,446
一个真正强大的翻译。

91
00:05:37,446 --> 00:05:41,070
现在，我们如何使用这个来利用
我们学习的方式。
